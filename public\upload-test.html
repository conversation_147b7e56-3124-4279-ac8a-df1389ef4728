<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Tariff Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>PDF Tariff Upload Test</h1>
    
    <form id="uploadForm">
        <div class="form-group">
            <label for="hotelId">Hotel ID:</label>
            <input type="text" id="hotelId" name="hotelId" value="123" required>
        </div>
        
        <div class="form-group">
            <label for="roomId">Room ID:</label>
            <input type="text" id="roomId" name="roomId" value="456" required>
        </div>
        
        <div class="form-group">
            <label for="pdfFile">PDF File:</label>
            <input type="file" id="pdfFile" name="pdfFile" accept="application/pdf" required>
        </div>
        
        <button type="submit">Upload PDF</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Uploading...';
            
            const formData = new FormData();
            formData.append('hotelId', document.getElementById('hotelId').value);
            formData.append('roomId', document.getElementById('roomId').value);
            formData.append('pdfFile', document.getElementById('pdfFile').files[0]);
            
            try {
                const response = await fetch('/api/admin/hotel/tariff-extraction/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.textContent = 'Upload successful!\n\n' + JSON.stringify(data, null, 2);
                    
                    // Now extract data from the uploaded PDF
                    const extractResponse = await fetch('/api/admin/hotel/tariff/extract', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            filePath: data.result.filePath,
                            hotelId: document.getElementById('hotelId').value,
                            roomId: document.getElementById('roomId').value
                        })
                    });
                    
                    const extractData = await extractResponse.json();
                    
                    resultDiv.textContent += '\n\nExtraction result:\n\n' + JSON.stringify(extractData, null, 2);
                } else {
                    resultDiv.textContent = 'Upload failed!\n\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        });
    </script>
</body>
</html>

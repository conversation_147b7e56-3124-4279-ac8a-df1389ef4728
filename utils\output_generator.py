#!/usr/bin/env python3
"""
Output Generator for Tariff Extraction

This module provides enhanced output generation functionality,
with support for different output formats and schemas.
"""

import re
import json
import logging
from typing import Dict, List, Tuple, Any, Optional, Union, Set
from datetime import datetime

# Configure logging
logger = logging.getLogger('tariff_extractor')

class OutputGenerator:
    """Enhanced output generator with support for different output formats and schemas"""
    
    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the output generator
        
        Args:
            config: Optional configuration object
        """
        self.config = config
        
        # Default output formats
        self.output_formats = {
            'simple': {
                'include_metadata': False,
                'include_confidence_scores': True,
                'include_source_info': True,
                'include_warnings': True,
                'include_extra_charges': True,
                'include_derived_info': True
            },
            'rich': {
                'include_metadata': True,
                'include_confidence_scores': True,
                'include_source_info': True,
                'include_warnings': True,
                'include_extra_charges': True,
                'include_derived_info': True,
                'include_extraction_stats': True
            },
            'minimal': {
                'include_metadata': False,
                'include_confidence_scores': False,
                'include_source_info': False,
                'include_warnings': False,
                'include_extra_charges': True,
                'include_derived_info': False
            },
            'tripmilestone': {
                'include_metadata': False,
                'include_confidence_scores': False,
                'include_source_info': False,
                'include_warnings': False,
                'include_extra_charges': True,
                'include_derived_info': False,
                'use_tripmilestone_schema': True
            }
        }
        
        # Load output formats from config if available
        if config and hasattr(config, 'get_full_config'):
            config_formats = config.get_full_config().get('output_formats', {})
            for format_name, format_config in config_formats.items():
                if format_name in self.output_formats:
                    self.output_formats[format_name].update(format_config)
                else:
                    self.output_formats[format_name] = format_config
        
        # Statistics for output operations
        self.output_stats = {
            'total_entries': 0,
            'output_format': '',
            'output_size_bytes': 0,
            'generation_time_ms': 0
        }
    
    def generate_output(self, entries: List[Dict[str, Any]], 
                       metadata: Dict[str, Any], 
                       extraction_stats: Dict[str, Any],
                       format_name: str = 'simple') -> Dict[str, Any]:
        """
        Generate output in the specified format
        
        Args:
            entries: List of entries to include in the output
            metadata: Dictionary of metadata
            extraction_stats: Dictionary of extraction statistics
            format_name: Name of the output format to use
            
        Returns:
            Dictionary containing the generated output
        """
        start_time = datetime.now()
        
        # Get format configuration
        format_config = self.output_formats.get(format_name, self.output_formats['simple'])
        
        # Update statistics
        self.output_stats['total_entries'] = len(entries)
        self.output_stats['output_format'] = format_name
        
        # Generate output based on format
        if format_config.get('use_tripmilestone_schema', False):
            output = self._generate_tripmilestone_output(entries, metadata, extraction_stats, format_config)
        elif format_name == 'rich':
            output = self._generate_rich_output(entries, metadata, extraction_stats, format_config)
        elif format_name == 'minimal':
            output = self._generate_minimal_output(entries, metadata, extraction_stats, format_config)
        else:
            output = self._generate_simple_output(entries, metadata, extraction_stats, format_config)
        
        # Update statistics
        self.output_stats['generation_time_ms'] = (datetime.now() - start_time).total_seconds() * 1000
        self.output_stats['output_size_bytes'] = len(json.dumps(output))
        
        return output
    
    def _generate_simple_output(self, entries: List[Dict[str, Any]], 
                               metadata: Dict[str, Any], 
                               extraction_stats: Dict[str, Any],
                               format_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate simple output format
        
        Args:
            entries: List of entries to include in the output
            metadata: Dictionary of metadata
            extraction_stats: Dictionary of extraction statistics
            format_config: Dictionary of format configuration
            
        Returns:
            Dictionary containing the generated output
        """
        output_entries = []
        
        for entry in entries:
            output_entry = {
                'room_type': entry.get('room_type', ''),
                'meal_plan': entry.get('meal_plan', ''),
                'price': entry.get('price', 0),
                'start_date': entry.get('start_date', ''),
                'end_date': entry.get('end_date', '')
            }
            
            # Add extra charges if configured
            if format_config.get('include_extra_charges', True):
                if 'extra_adult_price' in entry and entry['extra_adult_price'] > 0:
                    output_entry['extra_adult_price'] = entry['extra_adult_price']
                
                if 'extra_child_with_bed_price' in entry and entry['extra_child_with_bed_price'] > 0:
                    output_entry['extra_child_with_bed_price'] = entry['extra_child_with_bed_price']
                
                if 'extra_child_without_bed_price' in entry and entry['extra_child_without_bed_price'] > 0:
                    output_entry['extra_child_without_bed_price'] = entry['extra_child_without_bed_price']
            
            # Add derived info if configured
            if format_config.get('include_derived_info', True):
                if 'is_derived' in entry:
                    output_entry['is_derived'] = entry['is_derived']
                
                if 'extra_adult_is_derived' in entry:
                    output_entry['extra_adult_is_derived'] = entry['extra_adult_is_derived']
            
            # Add confidence scores if configured
            if format_config.get('include_confidence_scores', True):
                if 'confidence' in entry:
                    output_entry['confidence'] = entry['confidence']
            
            # Add source info if configured
            if format_config.get('include_source_info', True):
                if 'source' in entry:
                    output_entry['source'] = entry['source']
            
            # Add warnings if configured
            if format_config.get('include_warnings', True):
                if 'warnings' in entry and entry['warnings']:
                    output_entry['warnings'] = entry['warnings']
            
            output_entries.append(output_entry)
        
        # Create output dictionary
        output = {
            'entries': output_entries
        }
        
        # Add metadata if configured
        if format_config.get('include_metadata', False):
            output['metadata'] = metadata
        
        # Add extraction stats if configured
        if format_config.get('include_extraction_stats', False):
            output['extraction_stats'] = extraction_stats
        
        return output
    
    def _generate_rich_output(self, entries: List[Dict[str, Any]], 
                             metadata: Dict[str, Any], 
                             extraction_stats: Dict[str, Any],
                             format_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate rich output format
        
        Args:
            entries: List of entries to include in the output
            metadata: Dictionary of metadata
            extraction_stats: Dictionary of extraction statistics
            format_config: Dictionary of format configuration
            
        Returns:
            Dictionary containing the generated output
        """
        # Start with simple output
        output = self._generate_simple_output(entries, metadata, extraction_stats, format_config)
        
        # Add additional metadata
        output['metadata'] = metadata
        output['extraction_stats'] = extraction_stats
        output['processing_time_seconds'] = metadata.get('processing_time_seconds', 0)
        output['extraction_timestamp'] = datetime.now().isoformat()
        
        return output
    
    def _generate_minimal_output(self, entries: List[Dict[str, Any]], 
                                metadata: Dict[str, Any], 
                                extraction_stats: Dict[str, Any],
                                format_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate minimal output format
        
        Args:
            entries: List of entries to include in the output
            metadata: Dictionary of metadata
            extraction_stats: Dictionary of extraction statistics
            format_config: Dictionary of format configuration
            
        Returns:
            Dictionary containing the generated output
        """
        output_entries = []
        
        for entry in entries:
            output_entry = {
                'room_type': entry.get('room_type', ''),
                'meal_plan': entry.get('meal_plan', ''),
                'price': entry.get('price', 0),
                'start_date': entry.get('start_date', ''),
                'end_date': entry.get('end_date', '')
            }
            
            # Add extra charges if configured
            if format_config.get('include_extra_charges', True):
                if 'extra_adult_price' in entry and entry['extra_adult_price'] > 0:
                    output_entry['extra_adult_price'] = entry['extra_adult_price']
                
                if 'extra_child_with_bed_price' in entry and entry['extra_child_with_bed_price'] > 0:
                    output_entry['extra_child_with_bed_price'] = entry['extra_child_with_bed_price']
                
                if 'extra_child_without_bed_price' in entry and entry['extra_child_without_bed_price'] > 0:
                    output_entry['extra_child_without_bed_price'] = entry['extra_child_without_bed_price']
            
            output_entries.append(output_entry)
        
        # Return just the entries array
        return output_entries
    
    def _generate_tripmilestone_output(self, entries: List[Dict[str, Any]], 
                                      metadata: Dict[str, Any], 
                                      extraction_stats: Dict[str, Any],
                                      format_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate output in Tripmilestone schema format
        
        Args:
            entries: List of entries to include in the output
            metadata: Dictionary of metadata
            extraction_stats: Dictionary of extraction statistics
            format_config: Dictionary of format configuration
            
        Returns:
            Dictionary containing the generated output
        """
        output_entries = []
        
        for entry in entries:
            output_entry = {
                'roomType': entry.get('room_type', ''),
                'mealPlanType': entry.get('meal_plan', '').lower(),  # Ensure lowercase
                'roomPrice': entry.get('price', 0),
                'startDate': entry.get('start_date', ''),
                'endDate': entry.get('end_date', '')
            }
            
            # Add hotel and room IDs if available
            if 'hotel_id' in metadata:
                output_entry['hotelId'] = metadata['hotel_id']
            
            if 'room_id' in metadata:
                output_entry['roomId'] = metadata['room_id']
            
            # Add extra charges if configured
            if format_config.get('include_extra_charges', True):
                if 'extra_adult_price' in entry and entry['extra_adult_price'] > 0:
                    output_entry['extraAdultCharge'] = entry['extra_adult_price']
                
                if 'extra_child_with_bed_price' in entry and entry['extra_child_with_bed_price'] > 0:
                    output_entry['extraChildWithBedCharge'] = entry['extra_child_with_bed_price']
                
                if 'extra_child_without_bed_price' in entry and entry['extra_child_without_bed_price'] > 0:
                    output_entry['extraChildWithoutBedCharge'] = entry['extra_child_without_bed_price']
            
            output_entries.append(output_entry)
        
        # Return just the entries array for Tripmilestone schema
        return output_entries
    
    def save_output(self, output: Any, output_path: str) -> None:
        """
        Save output to file
        
        Args:
            output: Output to save
            output_path: Path to save output to
        """
        try:
            with open(output_path, 'w') as f:
                json.dump(output, f, indent=2)
            logger.info(f"Output saved to {output_path}")
        except Exception as e:
            logger.error(f"Error saving output to {output_path}: {str(e)}")
            raise
    
    def get_output_stats(self) -> Dict[str, Any]:
        """
        Get statistics about output operations
        
        Returns:
            Dictionary of output statistics
        """
        return self.output_stats

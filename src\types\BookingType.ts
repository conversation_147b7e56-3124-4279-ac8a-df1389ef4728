/* eslint-disable @typescript-eslint/no-explicit-any */
export interface BookingType {
  _id: string;
  bookingId: string;
  packageRootId: string;
  packageName: string;
  userId: string;
  planId: string;
  planName: string;
  interestId: string;
  interestName: string;
  destination: DestinationType[];
  startFrom: string;
  perRoom: number;
  packageImg: string[];
  noOfDays: number;
  noOfNight: number;
  noOfAdult: number;
  noOfChild: number;
  offer: number;
  hotelMeal: HotelMealType[];
  vehicleDetail: string[];
  period: PeriodType[];
  activity: ActivityType[];
  bonusRedeemCoin: number;
  vehicleCount: number;
  hotelCount: number;
  activityCount: number;
  additionalFees: number;
  marketingPer: number;
  transPer: number;
  agentCommissionPer: number;
  gstPer: number;
  status: string;
  checkStartDate: string;
  checkEndDate: string;
  fullStartDate: string;
  fullEndDate: string;
  totalRoomPrice: number;
  totalAdditionalFee: number;
  totalTransportFee: number;
  totalVehiclePrice: number;
  totalActivityPrice: number;
  totalCalculationPrice: number;
  agentAmount: number;
  totalPackagePrice: number;
  gstPrice: number;
  perPerson: number;
  packagePrice: number;
  couponCode: any;
  discountType: any;
  discountValue: number;
  paymentDate: string;
  discountPrice: number;
  finalPrice: number;
  isPrepaid: boolean;
  claimRedeemCoin: number;
  redeemCoin: number;
  redeemAmount: number;
  balanceAmount: number;
  createdAt: string;
  updatedAt: string;
  __v: number;
  userDetails: UserType[];
}
export interface UserType {
  _id: string;
  userId: string;
  email: string;
  userType: string;
  password: string;
  fullName: string;
  wishList: any[];
  gender: string;
  pinCode: string;
  profileImg: string;
  status: boolean;
  redeemCoins: number;
  claimRedeemCoins: number;
  refreshToken: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  dob: string;
  mobileNo: any;
}
export interface HotelMealType {
  hotelRoomId: string;
  mealPlan: string;
  noOfNight: number;
  startDateWise: number;
  endDateWise: number;
  sort: number;
  price: number;
  yStartDate: string;
  yEndDate: string;
  fullStartDate: string;
  fullEndDate: string;
  totalAdultPrice: number;
  gstAdultPrice: number;
  totalChildPrice: number;
  gstChildPrice: number;
  totalExtraAdultPrice: number;
  gstExtraAdultPrice: number;
  isAddOn: boolean;
  _id: string;
}
export interface PeriodType {
  startDate: string;
  endDate: string;
  _id: string;
}

export interface ActivityEventType {
  slot: 1;
  activityType: string;
  activityId: string;
  timePeriod: string;
  price: 0;
  _id: string;
}
export interface ActivityType {
    day: 1;
    from: string;
    to: string;
    startDateWise: 1;
    event: ActivityEventType[]
    _id: string;
  }

export interface DestinationType{
    destinationId: string;
    destinationName: string;
    noOfNight: 2;
    _id: string;
  }
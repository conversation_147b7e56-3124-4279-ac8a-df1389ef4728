# Advanced PDF Tariff Extractor

This is an advanced PDF tariff extraction system for Tripmilestone that uses both rule-based and ML approaches to extract hotel tariff data from PDFs.

## Features

- **Format Detection**: Automatically detects different PDF formats (standard tabular, season-meal matrix, dual rate format)
- **Complex Pricing Logic**: Handles meal plan supplements, extra person charges, and derived prices
- **ML Integration**: Uses OCR and NER for enhanced extraction from scanned PDFs
- **Robust Date Parsing**: Handles various date formats including ordinal dates (1st April 2025)
- **Fallback Mechanisms**: Multiple extraction strategies with graceful degradation

## Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Install Tesseract OCR (for OCR support):
   - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
   - Linux: `sudo apt-get install tesseract-ocr`
   - macOS: `brew install tesseract`

3. Download spaCy model:
   ```bash
   python -m spacy download en_core_web_sm
   ```

## Usage

### Command Line

```bash
python advanced_pdf_extractor.py <pdf_file_path>
```

### API Integration

The extractor is integrated with the Tripmilestone admin backend through the `/api/admin/hotel/tariff/extract` endpoint.

## How It Works

1. **PDF Format Classification**:
   - Analyzes the PDF to determine its structure
   - Identifies key indicators like season headers, meal plan columns, etc.

2. **Text and Table Extraction**:
   - Extracts all text and tables from the PDF
   - Cleans and preprocesses the data (removing currency symbols, etc.)

3. **Date Range Extraction**:
   - Identifies date ranges from text and tables
   - Handles various date formats and patterns

4. **Extra Charges Extraction**:
   - Extracts extra person charges (adults, children)
   - Identifies meal plan supplements

5. **Format-Specific Extraction**:
   - Uses specialized extractors for different PDF formats
   - Falls back to generic extraction if format is unknown

6. **OCR Fallback**:
   - If standard extraction fails, uses OCR to extract text from images
   - Applies the same extraction logic to the OCR text

7. **Post-Processing**:
   - Removes duplicates
   - Adds derived meal plans based on supplements
   - Adds extra person charges to entries

## Supported PDF Formats

1. **Standard Tabular Format**:
   - Tables with room types, prices, and dates in columns
   - Example: Simple price lists with room types in rows

2. **Season-Meal Matrix Format**:
   - Tables with seasons in columns and room types in rows
   - Different meal plans as sub-columns

3. **Dual Rate Format**:
   - Tables with both rack rates and net rates
   - Filters out rack rates and focuses on net/special rates

## Handling Complex Pricing Logic

- **Meal Plan Supplements**:
  - Extracts base CP rates and MAP/AP supplements
  - Calculates derived prices (e.g., MAP = CP + MAP supplement × room capacity)

- **Extra Person Charges**:
  - Extracts separate charges for extra adults and children
  - Handles different meal plan variations for extra persons

## Troubleshooting

If extraction fails or produces unexpected results:

1. **Check PDF Format**:
   - Make sure the PDF is not scanned as an image
   - Ensure tables are properly structured

2. **Install Dependencies**:
   - Verify all Python dependencies are installed
   - Check Tesseract OCR installation

3. **Examine Logs**:
   - Check the logs for detailed information about the extraction process
   - Look for specific errors or warnings

## Contributing

To improve the extractor:

1. Add support for new PDF formats
2. Enhance pattern matching for specific hotel chains
3. Improve OCR accuracy for scanned PDFs
4. Add more ML capabilities for better extraction

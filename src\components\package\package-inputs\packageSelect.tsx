/* eslint-disable @typescript-eslint/no-explicit-any */
import { input_field_css, label_css } from "../PackageForm";

export interface PackageSelectProp{
  defaultValue:string,
  name:string,
  apiData:any,
  dataName:string,
  dataId:string,
  setId:React.Dispatch<React.SetStateAction<string>>
}


export default function PackageSelect(prop:PackageSelectProp) {

  return (
    <div className=" p-2 m-1">
          <label htmlFor={prop.name.split(" ").join()} className={label_css}>{prop.name}</label>
          <select  onChange={(e:any)=>prop.setId(e.target.value)} value={prop.defaultValue}  id={prop.name.split(" ").join()} className={input_field_css}>
            {
              (prop.apiData?.length && prop.apiData?.length >0) && prop.apiData?.map((k:any)=>{
              
             return <option key={k[prop.dataId]} value={k[prop.dataId]}>{k[prop.dataName]}</option> })
            }
          </select>
        </div>
  )
}

import { ChevronRight, Home } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import React from "react";

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItem[];
  actions?: React.ReactNode;
  icon?: React.ReactNode;
  imageUrl?: string; 
}

/**
 * A standardized page header component that can be used across the application
 * 
 * @param title - The main title of the page
 * @param subtitle - Optional subtitle or description
 * @param breadcrumbs - Optional array of breadcrumb items
 * @param actions - Optional action buttons to display on the right side
 * @param icon - Optional icon to display next to the title
 * @param imageUrl - Optional image URL to display (typically for entity details pages)
 */
export function PageHeader({
  title,
  subtitle,
  breadcrumbs,
  actions,
  icon,
  imageUrl,
}: PageHeaderProps) {
  const location = useLocation();
  
  // Default breadcrumbs if none provided - shows current path
  const defaultBreadcrumbs: BreadcrumbItem[] = React.useMemo(() => {
    if (breadcrumbs) return breadcrumbs;
    
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const result: BreadcrumbItem[] = [
      { label: 'Dashboard', href: '/dashboard', icon: <Home size={14} /> },
    ];
    
    // Add path segments as breadcrumbs
    pathSegments.forEach((segment, index) => {
      const href = `/${pathSegments.slice(0, index + 1).join('/')}`;
      // Capitalize and clean up segment for display
      const label = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
      
      result.push({
        label,
        href: index < pathSegments.length - 1 ? href : undefined
      });
    });
    
    return result;
  }, [location.pathname, breadcrumbs]);

  return (
    <div className="bg-white shadow-sm rounded-lg mb-6">
      {/* Breadcrumbs navigation */}
      <div className="px-6 py-3 border-b border-gray-100">
        <div className="flex items-center text-sm text-gray-500 flex-wrap">
          {defaultBreadcrumbs.map((item, index) => {
            const isLast = index === defaultBreadcrumbs.length - 1;
            
            return (
              <React.Fragment key={index}>
                {index > 0 && <ChevronRight size={14} className="mx-2 flex-shrink-0" />}
                
                {!isLast && item.href ? (
                  <Link 
                    to={item.href} 
                    className="flex items-center hover:text-blue-600 transition-colors"
                  >
                    {item.icon && <span className="mr-1">{item.icon}</span>}
                    <span>{item.label}</span>
                  </Link>
                ) : (
                  <div className="flex items-center text-gray-900 font-medium truncate max-w-[200px]">
                    {item.icon && <span className="mr-1">{item.icon}</span>}
                    <span>{item.label}</span>
                  </div>
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>
      
      {/* Main header content */}
      <div className="p-6 flex justify-between items-center">
        <div className="flex items-start space-x-5">
          {/* Optional image */}
          {imageUrl && (
            <div className="relative">
              <img 
                src={imageUrl} 
                className="w-24 h-24 rounded-lg object-cover border border-gray-200" 
                alt={title} 
              />
            </div>
          )}
          
          {/* Title and subtitle */}
          <div className="flex flex-col">
            <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
              {icon && <span className="mr-2 text-gray-500">{icon}</span>}
              {title}
            </h1>
            {subtitle && (
              <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
        </div>
        
        {/* Action buttons */}
        {actions && (
          <div className="flex items-center space-x-3">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
} 
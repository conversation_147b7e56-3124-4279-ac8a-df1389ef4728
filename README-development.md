# Development Setup Guide

This guide explains how to set up the Tripmilestone Admin Frontend for local development.

## Prerequisites

1. Node.js (v16 or higher)
2. npm or yarn
3. Backend API running on localhost:3001

## Environment Detection

The application automatically detects the environment based on the hostname:

- **Local Development**: `localhost` or `127.0.0.1`
- **Production**: `admin.tripxplo.com`

## API Endpoints by Environment

### Local Development (localhost:3003)
- **Main API**: `http://localhost:3001/api/`
- **Tariff API**: `http://localhost:3002/api/` 
- **Frontend**: `http://localhost:3003`

### Production (admin.tripxplo.com)
- **Main API**: `https://api.tripxplo.com/v1/api/`
- **Tariff API**: `https://api.tripxplo.com/v1/api/`
- **Frontend**: `https://admin.tripxplo.com`

## Local Development Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Start Development Server
```bash
npm run dev
```

The application will start on `http://localhost:3003`

### 3. Backend Requirements

For local development, you need these services running:

1. **Main API Server** (port 3001)
   - Authentication endpoints
   - Hotel management APIs
   - General admin APIs

2. **Tariff Processing Service** (port 3002) - Optional
   - PDF extraction APIs
   - Tariff analysis endpoints

### 4. Environment Variables (Optional)

You can override the default URLs by setting these environment variables:

```bash
# .env.local (for local development)
VITE_API_SERVER_URL=http://localhost:3001/api/
VITE_API_URL=http://localhost:3001
VITE_FRONTEND_URL=http://localhost:3003
VITE_TARIFF_API_URL=http://localhost:3002
VITE_LINODE_STORAGE_URL=https://tripemilestone.in-maa-1.linodeobjects.com
```

## Testing

### Local Testing
When running on `localhost:3003`, the app will:
- Use local API endpoints
- Allow CORS from localhost
- Enable development features

### Production Testing
When accessing `admin.tripxplo.com`, the app will:
- Use production API endpoints
- Require proper CORS configuration
- Enable production optimizations

## Common Issues

### CORS Errors
If you get CORS errors during local development:

1. **Check Backend CORS Settings**: Ensure your backend allows `localhost:3003`
2. **Verify API URLs**: Check browser console for correct API endpoints
3. **Backend Status**: Ensure your backend is running on the expected ports

### Authentication Issues
If login fails:

1. **Check API URL**: Verify the auth endpoint is correct
2. **Backend Logs**: Check your backend server logs
3. **Network Tab**: Check the actual API requests in browser DevTools

## Build for Production

```bash
npm run build
```

The build process will:
- Use production environment variables
- Optimize for production deployment
- Generate static files in `dist/` folder

## Deployment

See the main README.md for deployment instructions. 
/* eslint-disable @typescript-eslint/no-explicit-any */
import CouponsCol from "@/components/coupons/CouponsCol";
import CouponsRow from "@/components/coupons/CouponsRow";
import Pagination from "@/components/layout-components/Pagination";
import Loading from "@/components/package/Loading";
import { getCoupons } from "@/utils/api-functions/getCoupons";
import { ChangeEvent, useEffect, useState } from "react";
import { IoIosHome } from "react-icons/io";
import { Link } from "react-router-dom";

const Coupons = () => {
  const [pageNo, setPageNo] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [packages, setPackages] = useState<any[]>([]);
  const [inputText,setInputText] = useState("")
  const [showText,setShowText] = useState("");
  async function fetchPackages(offset: number,search?:string) {
    const response = await getCoupons(offset,search);
    console.log(response);
      setPackages(response?.docs);
      setTotalPages(response?.totalPages);
  }
  useEffect(() => {
    fetchPackages(0);
  }, []);
  useEffect(() => {
    fetchPackages(pageNo * 10, inputText);
  }, [pageNo]);
  console.log(packages);

  useEffect(()=>{
    fetchPackages(0,inputText)
    setShowText(inputText)
  },[inputText])

  return (
    <>
      <header className="flex items-center justify-between">
        <div className="flex gap-2">
          <a href="/packages">
          <IoIosHome className="w-[35px] h-[35px] ml-2"/>
          </a>
          <input
            type="text"
            className="border w-[300px] ml-2 text-xl px-2 py-1"
            placeholder="Search Coupon..."
            value={inputText}
            onInput={(e:ChangeEvent<HTMLInputElement>)=>setInputText(e.target.value)}
          />
          <button className="bg-blue-500 px-2 " >Search</button>
        </div>
        <Link to={"/coupons/add"}>
          <button className="w-[200px] py-4 bg-black text-white m-2 rounded-lg">
            Create Coupon
          </button>
        </Link>

      </header>
      {
        showText? <div className="px-2">Search Results for "{showText}"</div>:<></>
      }
        <div className=" mt-6 border m-1 flex flex-col gap-5 min-h-[70vh] justify-between">
          <div className="">
            <CouponsCol />
            {packages?.length>0?
            packages?.map((k) => (
              <CouponsRow key={k._id} data={k} />
            )):<Loading/>}
          </div>
         <Pagination changePage={setPageNo} pageNo={pageNo} totalPages={totalPages} setPageNo={setPageNo}/>
        </div>
    </>
  );
};

export default Coupons;
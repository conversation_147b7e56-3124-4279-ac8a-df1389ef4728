const express = require('express');
const router = express.Router();

// Allow any credentials for development
router.put('/login', (req, res) => {
  const { email, password } = req.body;
  // Always succeed for any credentials
  return res.json({
    success: true,
    data: {
      accessToken: 'dummy-access-token',
      refreshToken: 'dummy-refresh-token'
    }
  });
});

module.exports = router; 
#!/usr/bin/env python3
"""
Batch Extractor for Hotel Tariff PDFs

This script processes multiple PDF files in a directory using the improved
4-phase tariff extraction pipeline.

Usage:
    python batch_extract.py [--input <input_dir>] [--output <output_dir>] [--recursive]

Options:
    --input      Directory containing PDFs to process (default: current directory)
    --output     Directory to save results (default: results/)
    --recursive  Search subdirectories for PDFs
    --use-advanced  Use the advanced extractor instead of the 4-phase pipeline (legacy mode)
"""

import os
import sys
import argparse
import json
import glob
import logging
import time
from datetime import datetime
import traceback
import uuid
from typing import Dict, List, Any
import concurrent.futures

# Import extractors
import extract_pdf_improved
# For backward compatibility
import advanced_pdf_extractor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch_extraction.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('batch_extractor')

def process_pdf(pdf_path: str, output_dir: str, use_advanced: bool = False) -> Dict[str, Any]:
    """
    Process a single PDF file and save the result
    
    Args:
        pdf_path: Path to the PDF file
        output_dir: Directory to save results
        use_advanced: Whether to use the advanced extractor (legacy mode)
        
    Returns:
        Processing result status and details
    """
    logger.info(f"Processing {pdf_path}...")
    start_time = time.time()
    
    # Create output filename
    base_name = os.path.basename(pdf_path)
    file_id = str(uuid.uuid4())[:8]
    output_filename = f"{os.path.splitext(base_name)[0]}_{file_id}_extraction_results.json"
    output_path = os.path.join(output_dir, output_filename)
    
    result = {
        "pdf_file": pdf_path,
        "output_file": output_path,
        "status": "unknown",
        "processing_time": 0,
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        if use_advanced:
            # Legacy mode: Use advanced extractor
            logger.info(f"Using advanced extractor for {pdf_path}")
            # Create arguments for advanced extractor
            sys.argv = [
                "advanced_pdf_extractor.py",
                pdf_path,
                '--output', output_path
            ]
            # Run the advanced extractor
            extraction_result = advanced_pdf_extractor.main()
            # Since the advanced extractor doesn't have a standardized return format,
            # we need to check if the output file was created
            if os.path.exists(output_path):
                # Try to load the output file to check for valid content
                try:
                    with open(output_path, 'r') as f:
                        output_data = json.load(f)
                    # Check if the extraction was successful (minimal validation)
                    if isinstance(output_data, list) and len(output_data) > 0:
                        result["status"] = "pending"  # Mark as pending for manual review
                    else:
                        result["status"] = "failed"
                        result["error_message"] = "Advanced extractor output is empty or invalid"
                except json.JSONDecodeError:
                    result["status"] = "failed"
                    result["error_message"] = "Advanced extractor output is not valid JSON"
            else:
                result["status"] = "failed"
                result["error_message"] = "Advanced extractor failed to create output file"
                
            result["extractor"] = "advanced"
        else:
            # Use improved 4-phase pipeline
            logger.info(f"Using improved 4-phase pipeline for {pdf_path}")
            # Create extractor instance
            extractor = extract_pdf_improved.SimplifiedExtractor(pdf_path=pdf_path)
            # Run extraction
            extraction_result = extractor.extract()
            
            # Save result to output file
            with open(output_path, 'w') as f:
                json.dump(extraction_result, f, indent=2)
                
            # Check for critical data in results
            has_room_tariffs = bool(extraction_result.get("room_tariffs", []))
            has_date_ranges = bool(extraction_result.get("date_ranges", []))
            
            # Update status from result
            if extraction_result.get("status") == "error" or extraction_result.get("status") == "failed":
                result["status"] = "failed"
                result["error_message"] = extraction_result.get("error", "Unknown extraction error")
            elif not has_room_tariffs or not has_date_ranges:
                result["status"] = "failed"
                result["error_message"] = "Missing critical data in extraction results"
            else:
                result["status"] = "pending"  # Default to pending for admin review
                
            result["statistics"] = extraction_result.get("statistics", {})
            result["extractor"] = "improved"
        
        processing_time = time.time() - start_time
        result["processing_time"] = processing_time
        logger.info(f"Finished processing {pdf_path} in {processing_time:.2f} seconds with status: {result['status']}")
        
    except Exception as e:
        error_message = str(e)
        tb = traceback.format_exc()
        logger.error(f"Error processing {pdf_path}: {error_message}")
        logger.error(tb)
        
        result["status"] = "error"
        result["error_message"] = error_message
        result["traceback"] = tb
        
    return result

def batch_process(input_dir: str, output_dir: str, recursive: bool = False, 
                 use_advanced: bool = False, max_workers: int = 4) -> Dict[str, Any]:
    """
    Process all PDF files in the input directory
    
    Args:
        input_dir: Directory containing PDFs to process
        output_dir: Directory to save results
        recursive: Whether to search subdirectories
        use_advanced: Whether to use the advanced extractor (legacy mode)
        max_workers: Maximum number of parallel workers
        
    Returns:
        Batch processing statistics
    """
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logger.info(f"Created output directory: {output_dir}")
        
    # Find all PDF files
    pattern = os.path.join(input_dir, "**/*.pdf" if recursive else "*.pdf")
    pdf_files = glob.glob(pattern, recursive=recursive)
    
    if not pdf_files:
        logger.warning(f"No PDF files found in {input_dir}")
        return {
            "status": "completed",
            "files_processed": 0,
            "files_succeeded": 0,
            "files_failed": 0,
            "timestamp": datetime.now().isoformat()
        }
        
    logger.info(f"Found {len(pdf_files)} PDF files to process")
    
    # Process files in parallel
    start_time = time.time()
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Create a future for each PDF file
        future_to_pdf = {
            executor.submit(process_pdf, pdf, output_dir, use_advanced): pdf
            for pdf in pdf_files
        }
        
        # Process as they complete
        for future in concurrent.futures.as_completed(future_to_pdf):
            pdf = future_to_pdf[future]
            try:
                result = future.result()
                results.append(result)
                logger.info(f"Completed {pdf}: {result['status']}")
            except Exception as e:
                logger.error(f"Error processing {pdf}: {str(e)}")
                results.append({
                    "pdf_file": pdf,
                    "status": "error",
                    "error_message": str(e)
                })
                
    # Calculate statistics
    total_time = time.time() - start_time
    succeeded = sum(1 for r in results if r.get("status") in ["success", "completed"])
    failed = sum(1 for r in results if r.get("status") in ["error", "failed"])
    
    stats = {
        "status": "completed",
        "files_processed": len(pdf_files),
        "files_succeeded": succeeded,
        "files_failed": failed,
        "total_processing_time": total_time,
        "avg_processing_time": total_time / len(pdf_files) if pdf_files else 0,
        "timestamp": datetime.now().isoformat()
    }
    
    # Save batch processing report
    report_path = os.path.join(output_dir, f"batch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(report_path, 'w') as f:
        json.dump({
            "statistics": stats,
            "file_results": results
        }, f, indent=2)
        
    logger.info(f"Batch processing complete. Processed {len(pdf_files)} files in {total_time:.2f} seconds.")
    logger.info(f"Results: {succeeded} succeeded, {failed} failed.")
    logger.info(f"Report saved to {report_path}")
    
    return stats
    
def delete_tariff_file(tariff_path: str) -> bool:
    """
    Delete a tariff file from the filesystem
    
    Args:
        tariff_path: Path to the tariff file to delete
        
    Returns:
        True if deleted successfully, False otherwise
    """
    try:
        if os.path.exists(tariff_path):
            os.remove(tariff_path)
            logger.info(f"Deleted tariff file: {tariff_path}")
            return True
        else:
            logger.warning(f"Tariff file not found: {tariff_path}")
            return False
    except Exception as e:
        logger.error(f"Error deleting tariff file {tariff_path}: {str(e)}")
        return False

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Batch process hotel tariff PDFs')
    
    parser.add_argument('--input', '-i', default='.',
                        help='Directory containing PDFs to process (default: current directory)')
    parser.add_argument('--output', '-o', default='results',
                        help='Directory to save results (default: results/)')
    parser.add_argument('--recursive', '-r', action='store_true',
                        help='Search subdirectories for PDFs')
    parser.add_argument('--use-advanced', '-a', action='store_true',
                        help='Use the advanced extractor instead of the 4-phase pipeline (legacy mode)')
    parser.add_argument('--workers', '-w', type=int, default=4,
                        help='Maximum number of parallel workers (default: 4)')
    parser.add_argument('--delete', '-d', 
                        help='Delete a specific tariff file by path')
    
    return parser.parse_args()
    
def main():
    """Main function"""
    args = parse_args()
    
    # Handle tariff deletion if requested
    if args.delete:
        if delete_tariff_file(args.delete):
            logger.info(f"Successfully deleted tariff file: {args.delete}")
            return 0
        else:
            logger.error(f"Failed to delete tariff file: {args.delete}")
            return 1
    
    logger.info(f"Starting batch processing from {args.input} to {args.output}")
    logger.info(f"Using {'advanced extractor' if args.use_advanced else 'improved 4-phase pipeline'}")
    
    if args.recursive:
        logger.info("Searching subdirectories recursively")
        
    stats = batch_process(
        input_dir=args.input,
        output_dir=args.output,
        recursive=args.recursive,
        use_advanced=args.use_advanced,
        max_workers=args.workers
    )
    
    # Return appropriate exit code
    return 0 if stats["files_failed"] == 0 else 1
    
if __name__ == "__main__":
    sys.exit(main())

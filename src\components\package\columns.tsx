import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Eye, MoreHorizontal, Pencil, Trash } from "lucide-react";
import { Destination, PackageHotelType, PackageType, Period } from "@/types/types"; // Adjust the import path as necessary
import { Link } from "react-router-dom";
import { DayActivityType } from "@/utils/context/PackageContext";

export const columns: ColumnDef<PackageType>[] = [
    {
        accessorKey: "packageName",
        header: () => {
            return (
                <h1 className="text-center">Package Name</h1>
            );
        },
        cell: ({ row }) => {
            const packageName: string = row.getValue("packageName");
            return (
                <div className="text-center">{packageName}</div>
            );
        }
    },
    {
        accessorKey: "destination",
        header: () => {
            return (
                <h1>Destinations</h1>
            );
        },
        cell: ({ row }) => {
            const destinations : Destination[] = row.getValue("destination");
            return (
                <div>
                    {destinations.map((dest) => (
                        <div key={dest.destinationId}>{dest.destinationId} (Nights: {dest.noOfNight})</div>
                    ))}
                </div>
            );
        }
    },
    {
        accessorKey: "planId",
        header: () => {
            return (
                <h1>Plan</h1>
            );
        },
        cell: ({ row }) => {
            const planId: string = row.getValue("planId");
            return (
                <div>{planId}</div>
            );
        }
    },
    {
        accessorKey: "interestId",
        header: () => {
            return (
                <h1>Interest</h1>
            );
        },
        cell: ({ row }) => {
            const interestId: string = row.getValue("interestId");
            return (
                <div>{interestId}</div>
            );
        }
    },
    {
        accessorKey: "hotel",
        header: () => {
            return (
                <h1>Hotel</h1>
            );
        },
        cell: ({ row }) => {
            const hotels :PackageHotelType[]= row.getValue("hotel");
            return (
                <div>
                    {hotels.map((hotel) => (
                        <div key={hotel.hotelId}>{hotels.length}</div> // Assuming PackageHotelType has a hotelId and name
                    ))}
                </div>
            );
        }
    },
    {
        accessorKey: "availableHotel",
        header: () => {
            return (
                <h1>Avail. Hotel</h1>
            );
        },
        cell: ({ row }) => {
            const availableHotels : number = row.getValue("availableHotel");
            return (
                <div>{availableHotels}</div>
            );
        }
    },
    {
        accessorKey: "activity",
        header: () => {
            return (
                <h1>Activity</h1>
            );
        },
        cell: ({ row }) => {
            const activities : DayActivityType[] = row.getValue("activity");
            return (
                <div>
                    {activities.map((activity) => (
                        <div key={activity.day}>{activity.day}</div> // Assuming DayActivityType has an activityId and name
                    ))}
                </div>
            );
        }
    },
    {
        accessorKey: "availableActivity",
        header: () => {
            return (
                <h1>Avail. Activity</h1>
            );
        },
        cell: ({ row }) => {
            const availableActivities : number = row.getValue("availableActivity");
            return (
                <div>{availableActivities}</div>
            );
        }
    },
    {
        accessorKey: "period",
        header: () => {
            return (
                <h1>Start-End Dates</h1>
            );
        },
        cell: ({ row }) => {
            const periods : Period[] = row.getValue("period");
            return (
                <div>
                    {periods.map((p, index) => (
                        <div key={index}>
                            {p.startDate} - {p.endDate}
                        </div>
                    ))}
                </div>
            );
        }
    },
    {
        accessorKey: 'Actions',
        header: () => {
            return (
                <h1>Actions</h1>
            );
        },
        cell: ({ row }) => {
            const { packageId } = row.original;

            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant={"ghost"} className="h-4 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <Link to={`/packages/${packageId}`}>
                            <DropdownMenuItem className="flex items-center gap-2">
                                <Eye className="h-4 w-4 mr-2 text-green-500" />
                                View
                            </DropdownMenuItem>
                        </Link>
                        <Link to={`/packages/edit/${packageId}`}>
                            <DropdownMenuItem className="flex items-center gap-2">
                                <Pencil className="h-4 w-4 mr-2 text-slate-700" />
                                Edit
                            </DropdownMenuItem>
                        </Link>
                        <DropdownMenuItem className="flex items-center gap-2">
                            <Trash className="h-4 w-4 mr-2 text-red-500" />
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        }
    }
];

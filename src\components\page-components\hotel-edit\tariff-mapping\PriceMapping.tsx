import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  AlertCircle,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Edit,
  Save,
  Trash2
} from 'lucide-react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';
import { format, isValid } from 'date-fns';
import { HotelRoom } from '@/types/types';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface Price {
  id: string;
  roomType: string;
  mealPlan: string;
  price: number;
  startDate: string;
  endDate: string;
  confidence?: number;
  isDerived?: boolean;
}

interface PriceMappingProps {
  extractedPrices: Price[];
  roomMappings: Record<string, string>;
  hotelRooms: HotelRoom[];
  dateRanges: DateRange[];
  priceMappings: Record<string, Price>;
  onUpdatePrice: (originalPrice: Price, updatedPrice: Price) => void;
  onRemovePrice: (price: Price) => void;
  onNext: () => void;
  onBack: () => void;
}

const MEAL_PLANS = [
  { id: 'cp', name: 'CP - Continental Plan (Breakfast only)' },
  { id: 'map', name: 'MAP - Modified American Plan (Breakfast + Dinner)' },
  { id: 'ap', name: 'AP - American Plan (All meals)' },
  { id: 'ep', name: 'EP - European Plan (Room only)' }
];

const PriceMapping: React.FC<PriceMappingProps> = ({
  extractedPrices,
  roomMappings,
  hotelRooms,
  dateRanges,
  priceMappings,
  onUpdatePrice,
  onRemovePrice,
  onNext,
  onBack
}) => {
  const [editingPrice, setEditingPrice] = useState<Price | null>(null);
  const [groupByRoom, setGroupByRoom] = useState(true);

  // Format date for display
  const formatDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      if (isValid(date)) {
        return format(date, 'MMM d, yyyy');
      }
      return dateStr;
    } catch (e) {
      return dateStr;
    }
  };

  // Format price for display
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(price);
  };

  // Get room name from ID
  const getRoomName = (roomId: string) => {
    const room = hotelRooms.find(r => r.hotelRoomId === roomId);
    return room ? room.hotelRoomType : 'Unknown Room';
  };

  // Handle price update
  const handleUpdatePrice = (originalPrice: Price) => {
    if (editingPrice) {
      // Validate price data
      if (!editingPrice.mealPlan) {
        console.error('[TARIFF_MAPPING] Missing meal plan in price update');
        return;
      }

      if (typeof editingPrice.price !== 'number' || isNaN(editingPrice.price) || editingPrice.price <= 0) {
        console.error('[TARIFF_MAPPING] Invalid price value:', editingPrice.price);
        return;
      }

      if (!isValidDate(editingPrice.startDate) || !isValidDate(editingPrice.endDate)) {
        console.error('[TARIFF_MAPPING] Invalid date range in price update:',
          { startDate: editingPrice.startDate, endDate: editingPrice.endDate });
        return;
      }

      // Ensure mealPlan is lowercase
      const normalizedPrice = {
        ...editingPrice,
        mealPlan: editingPrice.mealPlan.toLowerCase()
      };

      console.log('[TARIFF_MAPPING] Updating price:', {
        original: originalPrice,
        updated: normalizedPrice
      });

      onUpdatePrice(originalPrice, normalizedPrice);
      setEditingPrice(null);
    }
  };

  // Helper function to check if a date is valid
  const isValidDate = (dateStr: string) => {
    if (!dateStr) return false;

    try {
      // Try parsing as ISO date
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return false;
      }
      return true;
    } catch (e) {
      console.error('[TARIFF_MAPPING] Error validating date:', e);
      return false;
    }
  };

  // Group prices by room type
  const getPricesByRoom = () => {
    const pricesByRoom: Record<string, Price[]> = {};

    extractedPrices.forEach(price => {
      const roomId = roomMappings[price.roomType] || '';
      if (!roomId) return;

      const mappedPrice = priceMappings[price.id] || price;

      if (!pricesByRoom[roomId]) {
        pricesByRoom[roomId] = [];
      }

      pricesByRoom[roomId].push(mappedPrice);
    });

    return pricesByRoom;
  };

  const pricesByRoom = getPricesByRoom();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">
          Step 3: Verify Prices
        </h3>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant={groupByRoom ? "default" : "outline"}
            onClick={() => setGroupByRoom(true)}
          >
            Group by Room
          </Button>
          <Button
            size="sm"
            variant={!groupByRoom ? "default" : "outline"}
            onClick={() => setGroupByRoom(false)}
          >
            List All
          </Button>
        </div>
      </div>

      <Alert className="bg-blue-50 border-blue-200">
        <AlertCircle className="h-4 w-4 text-blue-600" />
        <AlertTitle className="text-blue-800">Verify Prices</AlertTitle>
        <AlertDescription className="text-blue-700">
          Review and adjust the prices extracted from the PDF. You can edit prices, meal plans, and date ranges.
        </AlertDescription>
      </Alert>

      {groupByRoom ? (
        // Group by room view
        <div className="space-y-8">
          {Object.entries(pricesByRoom).map(([roomId, prices]) => (
            <div key={roomId} className="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div className="bg-gray-50 p-4 border-b">
                <h4 className="font-medium text-gray-800">{getRoomName(roomId)}</h4>
                <div className="text-sm text-gray-500">{prices.length} price entries</div>
              </div>

              <div className="divide-y">
                {prices.map((price) => {
                  const isEditing = editingPrice && editingPrice.id === price.id;
                  const mappedPrice = priceMappings[price.id] || price;

                  return (
                    <div key={price.id} className="p-4 hover:bg-gray-50">
                      {isEditing ? (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <div className="text-sm font-medium text-gray-500 mb-1">Meal Plan:</div>
                            <select
                              value={editingPrice.mealPlan}
                              onChange={(e) => setEditingPrice({
                                ...editingPrice,
                                mealPlan: e.target.value
                              })}
                              className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                            >
                              {MEAL_PLANS.map((plan) => (
                                <option key={plan.id} value={plan.id}>
                                  {plan.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            <div className="text-sm font-medium text-gray-500 mb-1">Price:</div>
                            <Input
                              type="number"
                              value={editingPrice.price}
                              onChange={(e) => setEditingPrice({
                                ...editingPrice,
                                price: Number(e.target.value)
                              })}
                            />
                          </div>

                          <div>
                            <div className="text-sm font-medium text-gray-500 mb-1">Date Range:</div>
                            <select
                              value={`${editingPrice.startDate}_${editingPrice.endDate}`}
                              onChange={(e) => {
                                const [startDate, endDate] = e.target.value.split('_');
                                setEditingPrice({
                                  ...editingPrice,
                                  startDate,
                                  endDate
                                });
                              }}
                              className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                            >
                              {dateRanges.map((range) => (
                                <option key={`${range.startDate}_${range.endDate}`} value={`${range.startDate}_${range.endDate}`}>
                                  {formatDate(range.startDate)} - {formatDate(range.endDate)}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div className="md:col-span-3 flex justify-end gap-2 mt-2">
                            <Button
                              size="sm"
                              onClick={() => handleUpdatePrice(price)}
                            >
                              <Save size={16} className="mr-1" />
                              Save
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingPrice(null)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col md:flex-row md:items-center gap-4">
                          <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <div className="text-sm font-medium text-gray-500 mb-1">Meal Plan:</div>
                              <div className="font-medium text-gray-800">
                                {MEAL_PLANS.find(p => p.id === mappedPrice.mealPlan)?.name || mappedPrice.mealPlan.toUpperCase()}
                              </div>
                            </div>

                            <div>
                              <div className="text-sm font-medium text-gray-500 mb-1">Price:</div>
                              <div className="font-medium text-gray-800">
                                {formatPrice(mappedPrice.price)}

                                {mappedPrice.isDerived && (
                                  <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200">
                                    Derived
                                  </Badge>
                                )}

                                {mappedPrice.confidence && (
                                  <Badge
                                    variant="outline"
                                    className={`ml-2 ${
                                      mappedPrice.confidence > 0.8
                                        ? 'bg-green-50 text-green-700 border-green-200'
                                        : 'bg-amber-50 text-amber-700 border-amber-200'
                                    }`}
                                  >
                                    {Math.round(mappedPrice.confidence * 100)}% confidence
                                  </Badge>
                                )}
                              </div>
                            </div>

                            <div>
                              <div className="text-sm font-medium text-gray-500 mb-1">Date Range:</div>
                              <div className="font-medium text-gray-800 flex items-center">
                                <Calendar size={16} className="mr-2 text-blue-500" />
                                {formatDate(mappedPrice.startDate)} - {formatDate(mappedPrice.endDate)}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingPrice(mappedPrice)}
                            >
                              <Edit size={16} />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-600 border-red-200 hover:bg-red-50"
                              onClick={() => onRemovePrice(price)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      ) : (
        // List all view
        <div className="space-y-4">
          {extractedPrices.map((price) => {
            const isEditing = editingPrice && editingPrice.id === price.id;
            const mappedPrice = priceMappings[price.id] || price;
            const roomId = roomMappings[price.roomType] || '';

            if (!roomId) return null;

            return (
              <div key={price.id} className="bg-white rounded-lg border p-4 shadow-sm">
                {isEditing ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-gray-500 mb-1">Room Type:</div>
                      <div className="font-medium text-gray-800">
                        {getRoomName(roomId)}
                      </div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-500 mb-1">Meal Plan:</div>
                      <select
                        value={editingPrice.mealPlan}
                        onChange={(e) => setEditingPrice({
                          ...editingPrice,
                          mealPlan: e.target.value
                        })}
                        className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        {MEAL_PLANS.map((plan) => (
                          <option key={plan.id} value={plan.id}>
                            {plan.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-500 mb-1">Price:</div>
                      <Input
                        type="number"
                        value={editingPrice.price}
                        onChange={(e) => setEditingPrice({
                          ...editingPrice,
                          price: Number(e.target.value)
                        })}
                      />
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-500 mb-1">Date Range:</div>
                      <select
                        value={`${editingPrice.startDate}_${editingPrice.endDate}`}
                        onChange={(e) => {
                          const [startDate, endDate] = e.target.value.split('_');
                          setEditingPrice({
                            ...editingPrice,
                            startDate,
                            endDate
                          });
                        }}
                        className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        {dateRanges.map((range) => (
                          <option key={`${range.startDate}_${range.endDate}`} value={`${range.startDate}_${range.endDate}`}>
                            {formatDate(range.startDate)} - {formatDate(range.endDate)}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="md:col-span-2 flex justify-end gap-2 mt-2">
                      <Button
                        size="sm"
                        onClick={() => handleUpdatePrice(price)}
                      >
                        <Save size={16} className="mr-1" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingPrice(null)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col md:flex-row md:items-center gap-4">
                    <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <div className="text-sm font-medium text-gray-500 mb-1">Room Type:</div>
                        <div className="font-medium text-gray-800">
                          {getRoomName(roomId)}
                        </div>
                      </div>

                      <div>
                        <div className="text-sm font-medium text-gray-500 mb-1">Meal Plan:</div>
                        <div className="font-medium text-gray-800">
                          {MEAL_PLANS.find(p => p.id === mappedPrice.mealPlan)?.name || mappedPrice.mealPlan.toUpperCase()}
                        </div>
                      </div>

                      <div>
                        <div className="text-sm font-medium text-gray-500 mb-1">Price:</div>
                        <div className="font-medium text-gray-800">
                          {formatPrice(mappedPrice.price)}

                          {mappedPrice.isDerived && (
                            <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200">
                              Derived
                            </Badge>
                          )}

                          {mappedPrice.confidence && (
                            <Badge
                              variant="outline"
                              className={`ml-2 ${
                                mappedPrice.confidence > 0.8
                                  ? 'bg-green-50 text-green-700 border-green-200'
                                  : 'bg-amber-50 text-amber-700 border-amber-200'
                              }`}
                            >
                              {Math.round(mappedPrice.confidence * 100)}% confidence
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div>
                        <div className="text-sm font-medium text-gray-500 mb-1">Date Range:</div>
                        <div className="font-medium text-gray-800 flex items-center">
                          <Calendar size={16} className="mr-2 text-blue-500" />
                          {formatDate(mappedPrice.startDate)} - {formatDate(mappedPrice.endDate)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingPrice(mappedPrice)}
                      >
                        <Edit size={16} />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 border-red-200 hover:bg-red-50"
                        onClick={() => onRemovePrice(price)}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center"
        >
          <ChevronLeft size={16} className="mr-1" />
          Back
        </Button>
        <Button
          onClick={onNext}
          className="flex items-center"
        >
          Next Step
          <ChevronRight size={16} className="ml-1" />
        </Button>
      </div>
    </div>
  );
};

export default PriceMapping;

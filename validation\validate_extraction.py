#!/usr/bin/env python3
"""
Validate PDF Extraction Results

This script validates the results of PDF extraction against business rules
and generates a validation report.
"""

import os
import sys
import json
import argparse
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from validation.tariff_validator import TariffValidator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('validate_extraction')

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Validate PDF extraction results')
    parser.add_argument('input_file', help='JSON file with extraction results')
    parser.add_argument('--hotel', help='Hotel name for hotel-specific validation rules')
    parser.add_argument('--output', help='Output file for validation report (default: input_file_validation.json)')
    parser.add_argument('--html', action='store_true', help='Generate HTML report')
    return parser.parse_args()

def load_extraction_results(file_path: str) -> List[Dict[str, Any]]:
    """Load extraction results from JSON file"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Handle different formats
        if isinstance(data, dict) and 'results' in data:
            return data['results']
        elif isinstance(data, list):
            return data
        else:
            logger.error(f"Unexpected data format in {file_path}")
            return []
    except Exception as e:
        logger.error(f"Error loading extraction results: {e}")
        return []

def save_validation_report(issues: List[Dict[str, Any]], output_file: str):
    """Save validation report to JSON file"""
    try:
        with open(output_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'issues': issues,
                'summary': summarize_issues(issues)
            }, f, indent=2)
        
        logger.info(f"Validation report saved to {output_file}")
    except Exception as e:
        logger.error(f"Error saving validation report: {e}")

def generate_html_report(issues: List[Dict[str, Any]], data: List[Dict[str, Any]], output_file: str):
    """Generate HTML validation report"""
    html_file = os.path.splitext(output_file)[0] + '.html'
    
    # Generate HTML content
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Tariff Extraction Validation Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2, h3 {{ color: #333; }}
        .summary {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .issue {{ margin-bottom: 10px; padding: 10px; border-radius: 5px; }}
        .error {{ background-color: #ffebee; }}
        .warning {{ background-color: #fff8e1; }}
        .info {{ background-color: #e8f5e9; }}
        table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
    </style>
</head>
<body>
    <h1>Tariff Extraction Validation Report</h1>
    <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>Total entries: {len(data)}</p>
        <p>Total issues: {len(issues)}</p>
"""
    
    # Add issue summary by severity
    severity_counts = {}
    for issue in issues:
        severity = issue.get('severity', 'unknown')
        if severity not in severity_counts:
            severity_counts[severity] = 0
        severity_counts[severity] += 1
    
    for severity, count in severity_counts.items():
        html_content += f"        <p>{severity.capitalize()}: {count}</p>\n"
    
    html_content += "    </div>\n"
    
    # Add issues section
    if issues:
        html_content += """    <h2>Validation Issues</h2>
"""
        
        for i, issue in enumerate(issues):
            severity = issue.get('severity', 'info')
            html_content += f"""    <div class="issue {severity}">
        <h3>Issue #{i+1}: {issue.get('type', 'Unknown')}</h3>
        <p>{issue.get('message', 'No description')}</p>
"""
            
            # Add additional details
            for key, value in issue.items():
                if key not in ['type', 'message', 'severity']:
                    html_content += f"        <p><strong>{key}:</strong> {value}</p>\n"
            
            html_content += "    </div>\n"
    
    # Add data table
    html_content += """    <h2>Extracted Data</h2>
    <table>
        <tr>
            <th>#</th>
            <th>Room Type</th>
            <th>Meal Plan</th>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Price</th>
            <th>Extra Adult</th>
            <th>Extra Child (Bed)</th>
            <th>Extra Child (No Bed)</th>
        </tr>
"""
    
    for i, entry in enumerate(data):
        html_content += f"""        <tr>
            <td>{i+1}</td>
            <td>{entry.get('roomType', '')}</td>
            <td>{entry.get('mealPlanType', '')}</td>
            <td>{entry.get('startDate', '')}</td>
            <td>{entry.get('endDate', '')}</td>
            <td>{entry.get('roomPrice', '')}</td>
            <td>{entry.get('extraAdultCharge', '')}</td>
            <td>{entry.get('extraChildWithBedCharge', '')}</td>
            <td>{entry.get('extraChildWithoutBedCharge', '')}</td>
        </tr>
"""
    
    html_content += """    </table>
</body>
</html>
"""
    
    # Save HTML file
    try:
        with open(html_file, 'w') as f:
            f.write(html_content)
        
        logger.info(f"HTML report saved to {html_file}")
    except Exception as e:
        logger.error(f"Error saving HTML report: {e}")

def summarize_issues(issues: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate a summary of validation issues"""
    summary = {
        'total_issues': len(issues),
        'by_severity': {},
        'by_type': {}
    }
    
    # Count issues by severity and type
    for issue in issues:
        severity = issue.get('severity', 'unknown')
        issue_type = issue.get('type', 'unknown')
        
        if severity not in summary['by_severity']:
            summary['by_severity'][severity] = 0
        summary['by_severity'][severity] += 1
        
        if issue_type not in summary['by_type']:
            summary['by_type'][issue_type] = 0
        summary['by_type'][issue_type] += 1
    
    return summary

def print_validation_summary(issues: List[Dict[str, Any]], data: List[Dict[str, Any]]):
    """Print a summary of validation results to the console"""
    print("\n=== Tariff Extraction Validation Summary ===")
    print(f"Total entries: {len(data)}")
    print(f"Total issues: {len(issues)}")
    
    # Group issues by severity
    issues_by_severity = {}
    for issue in issues:
        severity = issue.get('severity', 'unknown')
        if severity not in issues_by_severity:
            issues_by_severity[severity] = []
        issues_by_severity[severity].append(issue)
    
    # Print issues by severity
    for severity in ['error', 'warning', 'info']:
        if severity in issues_by_severity:
            print(f"\n{severity.upper()} issues ({len(issues_by_severity[severity])}):")
            for issue in issues_by_severity[severity]:
                print(f"  - {issue.get('message', 'No description')}")

def main():
    """Main function"""
    args = parse_args()
    
    # Load extraction results
    data = load_extraction_results(args.input_file)
    if not data:
        logger.error("No data to validate")
        return
    
    logger.info(f"Loaded {len(data)} entries from {args.input_file}")
    
    # Validate data
    validator = TariffValidator(hotel_name=args.hotel)
    issues = validator.validate(data)
    
    logger.info(f"Found {len(issues)} validation issues")
    
    # Determine output file
    output_file = args.output
    if not output_file:
        output_file = os.path.splitext(args.input_file)[0] + '_validation.json'
    
    # Save validation report
    save_validation_report(issues, output_file)
    
    # Generate HTML report if requested
    if args.html:
        generate_html_report(issues, data, output_file)
    
    # Print summary to console
    print_validation_summary(issues, data)

if __name__ == "__main__":
    main()

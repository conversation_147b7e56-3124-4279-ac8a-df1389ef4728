/* eslint-disable @typescript-eslint/no-explicit-any */
import { createContext, ReactNode, useEffect, useState } from "react";
import { fetchApiData } from "../api-functions/fetchApiData";
import {
  HOTEL_URL,
  PACKAGE_ACTIVITY_URL,
  PACKAGE_DESTINATION_URL,
  PACKAGE_EXCLUSION_URL,
  PACKAGE_INCLUSION_URL,
  PACKAGE_INTEREST_URL,
  PACKAGE_PLAN_URL,
  PACKAGE_VEHICLE_URL,
} from "../urls/urls";
import { HotelDataType } from "@/components/package/package-inputs/HotelFields";
import { ActivityEvent } from "@/components/package/package-activity/ActivityDay";
import { PackageType } from "@/types/types";
import toast from "react-hot-toast";
import { createPackage } from "../api-functions/createPackage";
import { getPackage } from "../api-functions/getPackages";
import { editPackage } from "../api-functions/editPackageId";
import { useNavigate } from "react-router-dom";
import { ActivityDetailsType } from "@/components/package/package-activity/ActivityDetails";
const PackageContext = createContext({});
export type DayActivityType = {
  day: number;
  from: string;
  to: string;
  startDateWise: number;
  event: ActivityEvent[];
};
export const PackageProvider = ({ children }: { children: ReactNode }) => {
  const navigate = useNavigate();
  const [res, setRes] = useState<any>([]);
  const [edit, setEdit] = useState(false);
  const [plan, setPlan] = useState([]);
  const [interest, setInterest] = useState([]);
  const [destination, setDestination] = useState([]);
  const [destinationName, setDestinationName] = useState<string>("");
  const [allHotels, setAllHotels] = useState([]);
  const [allVehicles, setAllVehicles] = useState([]);
  const [allInclusion, setAllInclusion] = useState([]);
  const [allExclusion, setAllExclusion] = useState([]);
  const [allActivity, setAllActivity] = useState([]);
  const [hash, setHash] = useState("");
  const [numDays, setNumDays] = useState(0);
  const [oldPackageId, setOldPackageId] = useState("");
  const [isLoading, setLoading] = useState(true);
  const [startDateWise, setStartDateWise] = useState(0);
  const [endDateWise, setEndDateWise] = useState(0);
  const [redeemPoints, setRedeemPoints] = useState(0);
  const defaultInclusions = ["Transfers","Sightseeing","Breakfast","Hotel","Welcome Drink","Child Stay Free","Airport Transfers"];
  const defaultExclusions = ["Any up gradation in hotel room category","Ticket price for activites","Entry Ticket","Any items not shown in the package inclusions.","Train/ Flight Tickets"];

  const [packageName, setPackageName] = useState("");
  const [packageId, setPackageId] = useState("");
  const [planId, setPlanId] = useState("");
  const [interestId, setInterestId] = useState("");
  const [destinationIds, setDestinationIds] = useState<
    { destinationName: string; destinationId: string; noOfNight: number }[]
  >([]);
  const [dayCount, setDayCount] = useState<number>(0);
  const [destinationId, setDestinationId] = useState<string>("");
  const [noDays, setNoDays] = useState(0);
  const [noNights, setNoNights] = useState(0);
  const [noAdults, setNoAdults] = useState(0);
  const [noChild, setNoChild] = useState(0);
  const [offer, setoffer] = useState(0);
  const [sort,setSort] = useState(0);
  const [perRoom,setPerRoom] =useState(0);
  const [marketingPer, setMarketingPer] = useState(500);
  const [transPer, setTransPer] = useState(0);
  const [agentCommission, setAgentCommission] = useState(12);
  const [gstPer, setGstPer] = useState(5);
  const [startsFrom,setStartsFrom] = useState<string>("");
  const [startsFromId,setStartsFromId] = useState<string>("");
  const [hotelSaveData, setHotelSaveData] = useState<HotelDataType[]>([]);
  const [availableHotels, setAvailableHotels] = useState<any[]>([]);
  const [vehicles, setVehicles] = useState<any[]>([]);
  const [availableVehicles, setAvailableVehicles] = useState<any[]>([]);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [inclusion, setInclusion] = useState<any[]>([]);
  const [exclusion, setExclusion] = useState<any[]>([]);
  const [additionalPrice, setAdditionalPrice] = useState(0);
  const [dayActivity, setDayActivity] = useState<DayActivityType[]>([]);
  const [availableActivity, setAvailableActivity] = useState<any[]>([]);
  const [activityPrice, setActivityPrice] = useState(0);
  const [story, setStory] = useState("");
  const [period,setPeriod] = useState<{startDate:string,endDate:string}[]>([]);

  const [disableSubmit, setDisableSubmit] = useState(false);
  const [status,setStatus] = useState<boolean>(false);
  const [disableClone, setDisableClone] = useState(false);
  function setDates(startDate: string, endDate: string) {
    setPeriod((prevPeriod)=>[...prevPeriod,{startDate,endDate}])
  }
  function deleteDates(startDate: string, endDate: string) {
    setPeriod((prevPeriod)=>prevPeriod.filter((k)=>k.startDate!==startDate && k.endDate!==endDate))
  }
  async function fetchAllApiData() {
    const url = window.location.pathname.split("/");

    const planResp = await fetchApiData(PACKAGE_PLAN_URL);
    setPlan(planResp);
    setPlanId(planResp[0].planId);

    const interestResp = await fetchApiData(PACKAGE_INTEREST_URL);
    setInterest(interestResp);
    setInterestId(interestResp[0].interestId);

    const destinationResp = await fetchApiData(PACKAGE_DESTINATION_URL);
    setDestination(destinationResp);

    const hotelsResp = await fetchApiData(HOTEL_URL);
    setAllHotels(hotelsResp);

    const vehicleResp = await fetchApiData(PACKAGE_VEHICLE_URL);
    setAllVehicles(vehicleResp);

    const inclusionResp = await fetchApiData(PACKAGE_INCLUSION_URL);
    setAllInclusion(inclusionResp);

    const exclusionResp = await fetchApiData(PACKAGE_EXCLUSION_URL);
    setAllExclusion(exclusionResp);

    const activityResp = await fetchApiData(PACKAGE_ACTIVITY_URL);
    setAllActivity(activityResp);
    

    if (url[1] === "package" && url[url?.length - 2] === "edit") {
      setEdit(true);
      fetchPackage(url[2]);
      setOldPackageId(url[2]);
    } else {
      
      setLoading(false);
     
    }
  }

  function handleHotelSave(data: HotelDataType) {
    setHotelSaveData((prev) => [...prev, data]);
  }

  function handleHotelDelete(id: string) {
    setHotelSaveData((prev) =>
      prev.filter((k) => k.hotelRoomId + k.sort !== id)
    );
  }


  useEffect(() => {
    fetchAllApiData();
  }, []);
  useEffect(()=>{
    const defInc = allInclusion?.filter((inc:any)=>{
      return defaultInclusions?.includes(inc.name);
    })
    setInclusion(defInc)
    const defExc = allExclusion?.filter((inc:any)=>{
      return defaultExclusions?.includes(inc.name);
    })
    setExclusion(defExc)
    console.log(defInc,defExc)
  },[allExclusion,allInclusion])

  function handleAddActivity(e: DayActivityType) {
    setDayActivity((prev) => [...prev, e]);
  }
  function handleActivityEvents(day: number, e: ActivityEvent[]) {
    setDayActivity((prev) =>
      prev
        ?.map((k) => (k.day === day ? { ...k, event: e } : k))
        .sort((k, l) => k.day - l.day)
    );
  }
  function handleActivityDetails(day: number, data:ActivityDetailsType) {
    setDayActivity((prev) =>
      prev
        ?.map((k) => (k.day === day ? { ...k,from:data.from,to:data.to,startDateWise:data.startDateWise } : k))
    );
  }
  function handleDeleteAcvity(day: number) {
    setDayActivity((prev) => prev.filter((k) => k.day !== day));
  }
  function clearActivityEvent() {
    setDayActivity([]);
  }
  function generateRandomHash() {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let hash = "";
    for (let i = 0; i < 4; i++) {
      hash += characters.charAt(Math.floor(Math.random() * characters?.length));
    }
    return hash;
  }
  function addDestination(
    destinationName: string,
    destinationId: string,
    noOfNight: number
  ) {
    if (!destinationName || !destinationId || noOfNight < 0) {
      return;
    }
    const data = {
      destinationName,
      destinationId,
      noOfNight: noOfNight,
    };
    setDestinationIds((prev) => [...prev, data]);

    setDestinationId("");
    setDestinationName("");
    setDayCount(0);
  }
  function removeDestination(destinationId: string) {
    const data = destinationIds.filter((k) => k.destinationId != destinationId);

    setDestinationIds(data);
  }

  useEffect(() => {
    try{
      const planPack: any = plan.find((k: any) => k.planId === planId);
      const planShort: string = planPack?.planName.slice(0, 2)?.toUpperCase();
      const destShort: string = (destinationIds[0])?.destinationName
        ?.slice(0, 3)
        ?.toUpperCase();
      const interestPack: any = interest?.find(
        (k: any) => k?.interestId === interestId
      );
      const interestShort: string = interestPack?.interestName
        .slice(0, 2)
        .toUpperCase();
      const pId = `${planShort || ""}${destShort || ""}${
        interestShort || ""
      }-${hash}-${noDays || "0"}D${noNights || "0"}N${noAdults || "0"}A`;
      setPackageId(pId);
    }catch(err){
      console.log(err)
    }
  
  
  }, [planId, destinationIds, interestId, noDays, noNights, noAdults, noChild,hash,plan,interest]);
  useEffect(() => {
    setHash(generateRandomHash());
  }, []);
  useEffect(()=>{
    
  },[])
  async function fetchPackage(id: string) {
    const resp: any[] = await getPackage(id);
  
    console.log(resp);
    const editResp = resp[0];
    setRes(editResp);
    setPackageName(editResp?.packageName);
    setPackageId(editResp?.packageId);
    setNoDays(editResp?.noOfDays);
    setNoNights(editResp?.noOfNight);
    setNoAdults(editResp?.noOfAdult);
    setNoChild(editResp?.noOfChild);
    setStory(editResp?.story);
    setActivityPrice(editResp?.activityPrice || 0);
    setAdditionalPrice(editResp?.additionalFees);
    setAgentCommission(editResp?.agentCommissionPer);
    setMarketingPer(editResp?.marketingPer);
    setGstPer(editResp?.gstPer);
    setTransPer(editResp?.transPer);
    setoffer(editResp?.offer);

    setHotelSaveData(editResp?.hotel);
    setNumDays(editResp?.activity.length);
    setDayActivity(editResp?.activity);
    setStatus(editResp?.status);
    setSort(editResp?.sort);
    setPerRoom(editResp?.perRoom);
    setDestinationIds(editResp?.destination);
    setStartsFrom(editResp?.startFrom);
    setRedeemPoints(editResp?.redeemPoint);
  }
  useEffect(() => {
    if (edit) {
    setPeriod(res?.period?.length ? res?.period : []);  
      setAvailableHotels(
        allHotels?.filter((k: any) => res?.availableHotel?.includes(k?.hotelId))
      );
      setVehicles(
        allVehicles?.filter((k: any) => res?.vehicle?.includes(k?.vehicleId))
      );
      setAvailableVehicles(
        allVehicles?.filter((k: any) =>
          res?.availableVehicle?.includes(k?.vehicleId)
        )
      );
      setAvailableActivity(
        allActivity?.filter((k: any) =>
          res?.availableActivity?.includes(k?.activityId)
        )
      );
      setPlanId(res?.planId);
      setInterestId(res?.interestId);
      setInclusion(
        allInclusion?.filter((k: any) =>
          res?.inclusion?.includes(k?.inclusionId)
        )
      );
      setExclusion(
        allExclusion?.filter((k: any) =>
          res?.exclusion?.includes(k?.exclusionId)
        )
      );
      setLoading(false);
    }
  }, [res]);
  function validateActivity(){
    let bool = true;
    for(const day of dayActivity){
      if(day?.event?.length === 0){
        bool = false
        break
      }
    }
    return bool;
  }
  async function handleSubmit(operation: string) {
    setDisableSubmit(true);
    setDisableClone(true);
    setLoading(true);
    const data: PackageType = {
      packageId: packageId,
      packageName: packageName ? packageName.trim() : "",
      planId: planId,
      interestId: interestId,
      destination: destinationIds?.map((k) =>{ return {destinationId:k?.destinationId, noOfNight:k?.noOfNight }}),
      packageImg: [],
      noOfDays: Number(noDays),
      noOfNight: Number(noNights),
      noOfAdult: Number(noAdults),
      noOfChild: Number(noChild),
      offer: offer,
      hotel: hotelSaveData.map((k) => {
        return {
          hotelId: k.hotelId,
          hotelRoomId: k.hotelRoomId,
          mealPlan: k.mealPlan,
          noOfNight: k.noOfNight,
          startDateWise: k.startDateWise,
          endDateWise: k.endDateWise,
          sort: k.sort,
          isAddOn: k.isAddOn,
        };
      }),
      startFrom:startsFrom,
      availableHotel: availableHotels?.map((k) => k?.hotelId),
      vehicle: vehicles?.map((k) => k?.vehicleId),
      availableVehicle: availableVehicles?.map((k) => k?.vehicleId),
      period:period,
      story: story ? story.trim() : "",
      inclusion: inclusion?.map((k) => k?.inclusionId),
      exclusion: exclusion?.map((k) => k?.exclusionId),
      activity: dayActivity,
      availableActivity: availableActivity?.map((k) => k?.activityId),
      activityPrice: activityPrice,
      additionalFees: additionalPrice,
      marketingPer: marketingPer,
      transPer: transPer,
      agentCommissionPer: agentCommission,
      gstPer: gstPer,
      status:status,
      sort:sort,
      redeemPoint:redeemPoints,
      perRoom
    };
    if (
      data.packageName !== "" &&
      data.planId !== "" &&
      data.interestId !== "" &&
      data.destination?.length > 0 &&
      data.packageImg?.length > 0 &&
      data.noOfDays > 0 &&
      data.noOfNight > 0 &&
      data.noOfAdult > 0 &&
      data.noOfChild >= 0 &&
      data.offer >= 0 &&
      data.hotel?.length > 0 &&
      data.availableHotel?.length > 0 &&
      data.vehicle?.length > 0 &&
      data.availableVehicle?.length > 0 &&
      data.period?.length > 0 &&
      data.story !== "" &&
      data.startFrom && 
      data.inclusion?.length > 0 &&
      data.exclusion?.length > 0 &&
      data.activity?.length > 0 &&
      validateActivity() &&
      data.availableActivity?.length > 0 &&
      data.activityPrice >= 0 &&
      data.additionalFees >= 0 &&
      data.marketingPer >= 0 &&
      data.transPer >= 0 &&
      data.agentCommissionPer >= 0 &&
      data.gstPer >= 0
    ) {
      try {
        switch (operation) {
          case "create": {
            const resp = await createPackage(data);
            console.log(resp);
            resetStates();
            navigate("/packages");
            break;
          }
          case "update": {
            data.newPackageId = data.packageId;
            delete data.packageId;
            const resp = await editPackage(oldPackageId, data);
            console.log(resp);
            resetStates();
            navigate("/packages");
            break;
          }
          case "clone": {
            if (
              res?.packageName !== data.packageName 
              //&&
              // (res?.planId !== data.planId ||
              //   res?.interestId !== data.interestId ||
              //   res?.destinationId?.length !== data.destination?.length ||
              //   res?.noOfDays !== data.noOfDays ||
              //   res?.noOfNight !== data.noOfNight ||
              //   res?.noOfAdult !== data.noOfAdult)
            ) {
              const resp = await createPackage(data);
              console.log(resp);
              resetStates();
              navigate("/packages");
            } else {
              setLoading(false);
              setDisableClone(false);
              toast.error("Package Already Exists");
            }
            break;
          }
        }
      } catch (e) {
        console.log(e);
        setLoading(false);
      }
    } else {
      setDisableSubmit(false);
      setLoading(false);
      toast.error("Enter all the fields");
    }
  }
  //end

  function resetStates() {
    setRes([]);
    setEdit(false);
    setPlan([]);
    setInterest([]);
    setDestination([]);
    setAllHotels([]);
    setAllVehicles([]);
    setAllInclusion([]);
    setAllExclusion([]);
    setAllActivity([]);
    setHash("");
    setNumDays(0);
    setOldPackageId("");
    setLoading(true);
    setPackageName("");
    setPackageId("");
    setPlanId("");
    setInterestId("");
    setDestinationIds([]);
    setNoDays(0);
    setNoNights(0);
    setNoAdults(0);
    setNoChild(0);
    setoffer(0);
    setMarketingPer(500);
    setTransPer(0);
    setAgentCommission(12);
    setGstPer(5);
    setHotelSaveData([]);
    setAvailableHotels([]);
    setVehicles([]);
    setAvailableVehicles([]);
    setStartDate("");
    setEndDate("");
    setInclusion([]);
    setExclusion([]);
    setAdditionalPrice(0);
    setDayActivity([]);
    setAvailableActivity([]);
    setActivityPrice(0);
    setStory("");
    setPeriod([]);

    setDisableSubmit(false);
  }
  return (
    <PackageContext.Provider
      value={{
        removeDestination,
        disableClone,
        handleSubmit,
        packageName,
        setPackageName,
        plan,
        setPlan,
        setPlanId,
        planId,
        interest,
        setInterest,
        dayActivity,
        interestId,
        setInterestId,
        destination,
        destinationIds,
        setDestinationIds,
        noDays,
        setNoDays,
        noNights,
        setNoNights,
        noAdults,
        setNoAdults,
        noChild,
        setNoChild,
        offer,
        setoffer,
        allHotels,
        marketingPer,
        setMarketingPer,
        transPer,
        setTransPer,
        agentCommission,
        setAgentCommission,
        gstPer,
        setGstPer,
        hotelSaveData,
        handleHotelSave,
        handleHotelDelete,
        availableHotels,
        setAvailableHotels,
        allVehicles,
        vehicles,
        status,
        setStatus,
        setVehicles,
        availableVehicles,
        setAvailableVehicles,
        startDate,
        setStartDate,
        endDate,
        setEndDate,
        allInclusion,
        allExclusion,
        allActivity,
        inclusion,
        setInclusion,
        exclusion,
        setExclusion,
        setDayActivity,
        availableActivity,
        setAvailableActivity,
        story,
        setStory,
        addDestination,

        handleAddActivity,
        handleActivityEvents,
        clearActivityEvent,
        additionalPrice,
        setAdditionalPrice,
        activityPrice,
        setActivityPrice,
        disableSubmit,
        setDisableSubmit,
        packageId,
        setPackageId,
        setDates,
        period,
        deleteDates,
        numDays,
        setNumDays,
        isLoading,
        handleDeleteAcvity,
        destinationName,
        setDestinationName,
        dayCount,
        setDayCount,
        destinationId,
        setDestinationId,
        startDateWise,
        setStartDateWise,
        endDateWise,
        setEndDateWise,
        handleActivityDetails,
        sort,setSort,perRoom,setPerRoom,
       startsFrom,setStartsFrom,startsFromId,setStartsFromId,redeemPoints, setRedeemPoints
      }}
    >
      {children}
    </PackageContext.Provider>
  );
};
export default PackageContext;
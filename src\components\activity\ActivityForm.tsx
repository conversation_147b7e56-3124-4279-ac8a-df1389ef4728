/* eslint-disable @typescript-eslint/no-explicit-any */
import { useContext, useEffect } from "react";
import PackageSearchSelect from "../package/package-inputs/PackageSearchSelect";
import { input_field_css, label_css } from "../package/PackageForm";
import InputTags from "./InputTags";
import ActivityContext from "@/utils/context/ActivityContext";
import { dayTypesData, levelData } from "@/utils/constants/optionsData";

import Loading from "../package/Loading";
import { useParams } from "react-router-dom";

export default function ActivityForm() {
  const {activityId} = useParams()
  const {loading,destinationId,handleCreateActivity, setDestinationId,description,setDescription, destinationName, setDestinationName, allDestinations, activityName, setActivityName, allTags, setAllTags, price, setPrice, duration, setDuration, maxParticipants, setMaxParticipants, minParticipants, setMinParticipants, ageRestriction, setAgeRestriction,dayType,setDayType,level,setLevel,isPrivate,setPrivate}:any = useContext(ActivityContext)
  useEffect(() => {
    console.log(destinationName)
  },[destinationName])
  return loading?<Loading/>:(
    <>
      <div className="p-5 w-full  flex">
        <div className="w-1/3 flex flex-col gap-5 px-4">
          <div className="">
            <div className="hidden">{destinationId}</div>
            <label htmlFor="activityName" className={label_css}>
              Activity Name
            </label>
            <input
              id="activityName"
              className={input_field_css}
              placeholder="Enter Activity Name"
              value={activityName}
              onInput={(e: any) => setActivityName(e.target.value)}
            />
          </div>
          <div className="">
            <label htmlFor="activityDesc" className={label_css}>
              Activity Description
            </label>
            <textarea
              id="activityDesc"
              placeholder="Enter Activity Description"
              className={input_field_css}
              value={description}
              onInput={(e: any) => setDescription(e.target.value)}
              rows={5}
            ></textarea>
          </div>
          <PackageSearchSelect
            inputName={destinationName}
            setDataName={setDestinationName}
            allData={allDestinations}
            dataName="destinationName"
            dataId="destinationId"
            setData={setDestinationId}
            pHolder="Select Destination"
          />
          
          <InputTags allInputs={allTags} setAllInputs={setAllTags} />


        </div>
        <div className="w-2/3 px-2">
          <div className="flex w-1/2">
            <div className="px-1">
              <label htmlFor="activityprice" className={label_css}>
                Activity Price
              </label>
              <input
                id="activityPrice"
                type="number"
                className={input_field_css}
                placeholder="Price"
                value={price}
                onInput={(e: any) => setPrice(e.target.valueAsNumber)}
              />
            </div>
            <div className="px-1">
              <label htmlFor="duration" className={label_css}>
                Activity Duration
              </label>
              <input
                id="duration"
                type="number"
                className={input_field_css}
                placeholder="Duration"
                value={duration}
                onInput={(e: any) => setDuration(e.target.valueAsNumber)}
              />
            </div>
          </div>
          <div className="w-1/2">
            <div className="flex mt-10">
              <div className="px-1">
                <label htmlFor="maxParticipants" className={label_css}>
                  Max. Participants
                </label>
                <input
                  id="maxParticipants"
                  type="number"
                  className={input_field_css}
                  placeholder="Max. Participants"
                  value={maxParticipants}
                  onInput={(e: any) =>
                    setMaxParticipants(e.target.valueAsNumber)
                  }
                />
              </div>
              <div className="px-1">
                <label htmlFor="minParticipants" className={label_css}>
                  Min. Participants
                </label>
                <input
                  id="minParticipants"
                  type="number"
                  className={input_field_css}
                  placeholder="Min. Participants"
                  value={minParticipants}
                  onInput={(e: any) =>
                    setMinParticipants(e.target.valueAsNumber)
                  }
                />
              </div>
            </div>
            <div className="flex mt-2">
              <div className="px-1">
                <label htmlFor="ageRestriction" className={label_css}>
                  Age Restriction
                </label>
                <input
                  id="ageRestriction"
                  type="number"
                  className={input_field_css}
                  placeholder="Age Restriction"
                  value={ageRestriction}
                  onInput={(e: any) =>
                    setAgeRestriction(e.target.valueAsNumber)
                  }
                />
              </div>
            </div>
            <div className=" mt-5 flex flex-col">
              <label htmlFor="dayType" className={label_css}>
                Activity DayType
              </label>
              <select id="dayType" value={dayType} onChange={(e) => setDayType(e.target.value)} className={input_field_css}>
                  {
                    dayTypesData.map((data)=>{
                      return <option key={data}  value={data}>{data}</option>
                    })
                  }
              </select>

              <label  htmlFor="level" className={label_css + ' mt-2'}>
                Activity Level
              </label>
              <select id="level" value={level} onChange={(e) => setLevel(e.target.value)} className={input_field_css}>
                  {
                    levelData.map((data)=>{
                      return <option key={data}  value={data}>{data}</option>
                    })
                  }
              </select>
            </div>

            <div className="mt-5 flex gap-5">
              <div className="flex gap-2">
              <label htmlFor="private">Private:</label>
                <input type="radio" id="private" className="w-4" value="1" name="isPrivate" checked={isPrivate==="1"} onChange={(e:any)=>setPrivate(e.target.value)}/>
              </div>

              <div className="flex gap-2 ">
              <label htmlFor="public">Public:</label>
                <input type="radio" className="w-4" id="public" value="0" name="isPrivate" checked={isPrivate==="0"} onChange={(e:any)=>setPrivate(e.target.value)}/>
              </div>
                
            </div>
          </div>
      
        </div>
      
       
      </div>

      <div className="px-5 mt-5">
      {
          activityId?<>
      <button  onClick={()=>handleCreateActivity("update")} className="px-4 py-2 mx-1 bg-blue-700 text-white rounded-lg">Update</button>

      <button onClick={()=>handleCreateActivity("delete")} className="px-4 py-2 mx-1 bg-red-700 text-white rounded-lg">Delete</button>

      <button onClick={()=>handleCreateActivity("clone")} className="px-4 py-2 mx-1 bg-lime-500 text-white rounded-lg">Clone</button>

      </>:
      <button onClick={()=>handleCreateActivity("create")} className="px-4 py-2 mx-1 bg-blue-700 text-white rounded-lg">Create</button>


        }
        </div>
    </>
  );
}
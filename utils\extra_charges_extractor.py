#!/usr/bin/env python3
"""
Extra Charges Extractor for Tariff Extraction

This module provides enhanced extraction of extra charges and meal plan definitions
from PDF text, with improved pattern matching and context awareness.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Union

# Configure logging
logger = logging.getLogger('tariff_extractor')

class ExtraChargesExtractor:
    """Enhanced extractor for extra charges and meal plan definitions"""

    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the extra charges extractor

        Args:
            config: Optional configuration object
        """
        self.config = config

        # Default patterns for extra charges extraction
        self.patterns = {
            # Patterns for meal plan supplements
            'meal_plan_supplements': {
                'map_supplement': [
                    r'MAP\s+(?:charges|supplement|surcharge|additional).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)[/\s]*(?:Head|Person|Pax|Adult|pp)',
                    r'(?:additional|extra)\s+(?:for|charge)\s+MAP.*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'MAP\s+(?:plan|rate).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)[/\s]*(?:Head|Person|Pax|Adult|pp|per)'
                ],
                'ap_supplement': [
                    r'AP\s+(?:charges|supplement|surcharge|additional).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)[/\s]*(?:Head|Person|Pax|Adult|pp)',
                    r'(?:additional|extra)\s+(?:for|charge)\s+AP.*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'AP\s+(?:plan|rate).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)[/\s]*(?:Head|Person|Pax|Adult|pp|per)'
                ]
            },

            # Patterns for extra adult charges
            'extra_adult_charges': {
                'extra_adult_cp': [
                    r'Extra\s+(?:Person|Adult).*?(?:above\s+\d+|with\s+bed).*?(?:CP|Continental).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'Extra\s+(?:Person|Adult).*?(?:CP|Continental).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'Extra\s+(?:Person|Adult).*?(?:with\s+bed).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ],
                'extra_adult_map': [
                    r'Extra\s+(?:Person|Adult).*?(?:above\s+\d+|with\s+bed).*?(?:MAP|Modified|American).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'Extra\s+(?:Person|Adult).*?(?:MAP|Modified|American).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ],
                'extra_adult_ap': [
                    r'Extra\s+(?:Person|Adult).*?(?:above\s+\d+|with\s+bed).*?(?:AP|All\s+Inclusive).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'Extra\s+(?:Person|Adult).*?(?:AP|All\s+Inclusive).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ]
            },

            # Patterns for extra child charges
            'extra_child_charges': {
                'extra_child_with_bed': [
                    r'(?:Child|Kid).*?(?:age\s+\d+\s*(?:-|to)\s*\d+|with\s+bed).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'Extra\s+(?:Child|Kid).*?(?:with\s+bed).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'(?:Child|Kid).*?(?:with\s+bed).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ],
                'extra_child_without_bed': [
                    r'(?:Child|Kid).*?(?:age\s+\d+\s*(?:-|to)\s*\d+|without\s+bed).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'Extra\s+(?:Child|Kid).*?(?:without\s+bed).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'(?:Child|Kid).*?(?:without\s+bed).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ],
                'infant_charges': [
                    r'(?:Infant|Baby).*?(?:age\s+\d+\s*(?:-|to)\s*\d+|below\s+\d+).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
                    r'(?:Infant|Baby).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ]
            },

            # Patterns for special event charges
            'special_event_charges': {
                'gala_dinner_christmas': [
                    r'(?:Christmas|X-?mas)\s+(?:Eve|Dinner|Gala).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)[/\s]*(?:Head|Person|Pax|Adult|pp|per)',
                    r'(?:Gala|Special)\s+(?:Dinner|Event).*?(?:Christmas|X-?mas).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ],
                'gala_dinner_new_year': [
                    r'(?:New\s+Year(?:\'s)?|NYE)\s+(?:Eve|Dinner|Gala).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)[/\s]*(?:Head|Person|Pax|Adult|pp|per)',
                    r'(?:Gala|Special)\s+(?:Dinner|Event).*?(?:New\s+Year(?:\'s)?|NYE).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ],
                'gala_dinner_diwali': [
                    r'(?:Diwali|Deepavali)\s+(?:Eve|Dinner|Gala).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)[/\s]*(?:Head|Person|Pax|Adult|pp|per)',
                    r'(?:Gala|Special)\s+(?:Dinner|Event).*?(?:Diwali|Deepavali).*?(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
                ]
            },

            # Patterns for meal plan definitions
            'meal_plan_definitions': {
                'cp_definition': [
                    r'CP\s+(?:includes|means|is|=).*?(?:breakfast|morning\s+meal)',
                    r'Continental\s+Plan\s+(?:includes|means|is|=).*?(?:breakfast|morning\s+meal)'
                ],
                'map_definition': [
                    r'MAP\s+(?:includes|means|is|=).*?(?:breakfast|morning\s+meal).*?(?:dinner|evening\s+meal)',
                    r'Modified\s+American\s+Plan\s+(?:includes|means|is|=).*?(?:breakfast|morning\s+meal).*?(?:dinner|evening\s+meal)'
                ],
                'ap_definition': [
                    r'AP\s+(?:includes|means|is|=).*?(?:all\s+meals|breakfast.*?lunch.*?dinner)',
                    r'American\s+Plan\s+(?:includes|means|is|=).*?(?:all\s+meals|breakfast.*?lunch.*?dinner)'
                ],
                'ep_definition': [
                    r'EP\s+(?:includes|means|is|=).*?(?:room\s+only|no\s+meals|accommodation\s+only)',
                    r'European\s+Plan\s+(?:includes|means|is|=).*?(?:room\s+only|no\s+meals|accommodation\s+only)'
                ]
            }
        }

        # Load patterns from config if available
        if config and hasattr(config, 'get_full_config'):
            config_patterns = config.get_full_config().get('extra_charges_patterns', {})
            for category, subcategories in config_patterns.items():
                if category in self.patterns:
                    for subcategory, patterns in subcategories.items():
                        if subcategory in self.patterns[category]:
                            self.patterns[category][subcategory].extend(patterns)
                        else:
                            self.patterns[category][subcategory] = patterns
                else:
                    self.patterns[category] = subcategories

        # Compile regex patterns for better performance
        self.compiled_patterns = {}
        for category, subcategories in self.patterns.items():
            self.compiled_patterns[category] = {}
            for subcategory, patterns in subcategories.items():
                self.compiled_patterns[category][subcategory] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]

        # Statistics for extraction operations
        self.extraction_stats = {
            'total_texts_processed': 0,
            'meal_plan_supplements_found': 0,
            'extra_adult_charges_found': 0,
            'extra_child_charges_found': 0,
            'special_event_charges_found': 0,
            'meal_plan_definitions_found': 0
        }

    def extract_extra_charges(self, text: str) -> Dict[str, Any]:
        """
        Extract extra charges from text

        Args:
            text: Text to extract extra charges from

        Returns:
            Dictionary containing extracted extra charges
        """
        if not text:
            return {}

        self.extraction_stats['total_texts_processed'] += 1

        # Initialize results dictionary
        results = {
            'meal_plan_supplements': {},
            'extra_adult_charges': {},
            'extra_child_charges': {},
            'special_event_charges': {},
            'meal_plan_definitions': {}
        }

        # Extract meal plan supplements
        for subcategory, patterns in self.compiled_patterns['meal_plan_supplements'].items():
            for pattern in patterns:
                match = pattern.search(text)
                if match:
                    price = self.extract_price(match.group(1))
                    if price > 0:
                        results['meal_plan_supplements'][subcategory] = price
                        logger.debug(f"Found {subcategory}: {price}")
                        self.extraction_stats['meal_plan_supplements_found'] += 1
                        break  # Use the first valid match for this subcategory

        # Extract extra adult charges
        for subcategory, patterns in self.compiled_patterns['extra_adult_charges'].items():
            for pattern in patterns:
                match = pattern.search(text)
                if match:
                    price = self.extract_price(match.group(1))
                    if price > 0:
                        results['extra_adult_charges'][subcategory] = price
                        logger.debug(f"Found {subcategory}: {price}")
                        self.extraction_stats['extra_adult_charges_found'] += 1
                        break  # Use the first valid match for this subcategory

        # Extract extra child charges
        for subcategory, patterns in self.compiled_patterns['extra_child_charges'].items():
            for pattern in patterns:
                match = pattern.search(text)
                if match:
                    price = self.extract_price(match.group(1))
                    if price > 0:
                        results['extra_child_charges'][subcategory] = price
                        logger.debug(f"Found {subcategory}: {price}")
                        self.extraction_stats['extra_child_charges_found'] += 1
                        break  # Use the first valid match for this subcategory

        # Extract special event charges
        for subcategory, patterns in self.compiled_patterns['special_event_charges'].items():
            for pattern in patterns:
                match = pattern.search(text)
                if match:
                    price = self.extract_price(match.group(1))
                    if price > 0:
                        results['special_event_charges'][subcategory] = price
                        logger.debug(f"Found {subcategory}: {price}")
                        self.extraction_stats['special_event_charges_found'] += 1
                        break  # Use the first valid match for this subcategory

        # Extract meal plan definitions
        for subcategory, patterns in self.compiled_patterns['meal_plan_definitions'].items():
            for pattern in patterns:
                match = pattern.search(text)
                if match:
                    # Extract the full definition text
                    definition = match.group(0)
                    results['meal_plan_definitions'][subcategory] = definition
                    logger.debug(f"Found {subcategory}: {definition}")
                    self.extraction_stats['meal_plan_definitions_found'] += 1
                    break  # Use the first valid match for this subcategory

        # Derive additional charges if possible
        self._derive_additional_charges(results)

        # Flatten the results for easier access
        flat_results = self._flatten_results(results)

        return flat_results

    def _derive_additional_charges(self, results: Dict[str, Dict[str, Any]]) -> None:
        """
        Derive additional charges based on existing charges

        Args:
            results: Dictionary of extracted charges
        """
        # If we have CP extra adult charge and MAP supplement, calculate MAP extra adult charge
        if ('extra_adult_cp' in results.get('extra_adult_charges', {}) and
            'map_supplement' in results.get('meal_plan_supplements', {})):

            extra_adult_cp = results['extra_adult_charges']['extra_adult_cp']
            map_supplement = results['meal_plan_supplements']['map_supplement']

            # Calculate MAP extra adult charge
            map_extra_adult = extra_adult_cp + map_supplement

            # Add to results if not already present
            if 'extra_adult_map' not in results['extra_adult_charges']:
                results['extra_adult_charges']['extra_adult_map'] = map_extra_adult
                logger.debug(f"Derived extra_adult_map: {map_extra_adult} (from CP {extra_adult_cp} + MAP supplement {map_supplement})")

        # If we have CP extra adult charge and AP supplement, calculate AP extra adult charge
        if ('extra_adult_cp' in results.get('extra_adult_charges', {}) and
            'ap_supplement' in results.get('meal_plan_supplements', {})):

            extra_adult_cp = results['extra_adult_charges']['extra_adult_cp']
            ap_supplement = results['meal_plan_supplements']['ap_supplement']

            # Calculate AP extra adult charge
            ap_extra_adult = extra_adult_cp + ap_supplement

            # Add to results if not already present
            if 'extra_adult_ap' not in results['extra_adult_charges']:
                results['extra_adult_charges']['extra_adult_ap'] = ap_extra_adult
                logger.debug(f"Derived extra_adult_ap: {ap_extra_adult} (from CP {extra_adult_cp} + AP supplement {ap_supplement})")

    def _flatten_results(self, results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Flatten the nested results dictionary for easier access

        Args:
            results: Nested dictionary of extracted charges

        Returns:
            Flattened dictionary of extracted charges
        """
        flat_results = {}

        # Flatten meal plan supplements
        for key, value in results.get('meal_plan_supplements', {}).items():
            flat_results[key] = value

        # Flatten extra adult charges
        for key, value in results.get('extra_adult_charges', {}).items():
            flat_results[key] = value

        # Flatten extra child charges
        for key, value in results.get('extra_child_charges', {}).items():
            flat_results[key] = value

        # Flatten special event charges
        for key, value in results.get('special_event_charges', {}).items():
            flat_results[key] = value

        # Keep meal plan definitions separate
        if results.get('meal_plan_definitions'):
            flat_results['meal_plan_definitions'] = results['meal_plan_definitions']

        return flat_results

    def extract_price(self, price_text: str) -> float:
        """
        Extract numeric price from string

        Args:
            price_text: Text containing a price

        Returns:
            Extracted price as float, or 0 if no price found
        """
        if not price_text:
            return 0

        # Clean the price text
        price_text = price_text.strip()

        # Remove currency symbols and other non-numeric characters
        price_text = re.sub(r'[^\d.,]', '', price_text)

        # Replace comma with dot for decimal separator
        price_text = price_text.replace(',', '.')

        # Extract the first valid number
        numbers = re.findall(r'\d+\.?\d*', price_text)
        if not numbers:
            return 0

        try:
            return float(numbers[0])
        except ValueError:
            return 0

    def get_extraction_stats(self) -> Dict[str, int]:
        """
        Get statistics about extraction operations

        Returns:
            Dictionary of extraction statistics
        """
        return self.extraction_stats

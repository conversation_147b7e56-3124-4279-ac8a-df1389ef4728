import { HotelsType } from "@/types/types";
import { SERVER_IMAGE_URL } from "@/utils/urls/urls";
import { ArrowLeft, ChevronRight, Edit, Home, MapPin } from "lucide-react";
import { Link } from "react-router-dom";

interface HotelDetailsHeaderProps {
  hotel?: HotelsType;
}

export default function HotelDetailsHeader(props: HotelDetailsHeaderProps) {
  const { hotel } = props;
  
  return (
    <div className="bg-white shadow-sm rounded-lg mb-6">
      {/* Breadcrumbs navigation */}
      <div className="px-6 py-3 border-b border-gray-100">
        <div className="flex items-center text-sm text-gray-500">
          <Link to="/dashboard" className="flex items-center hover:text-blue-600 transition-colors">
            <Home size={14} className="mr-1" />
            <span>Dashboard</span>
          </Link>
          <ChevronRight size={14} className="mx-2" />
          <Link to="/hotels" className="hover:text-blue-600 transition-colors">
            Hotels
          </Link>
          <ChevronRight size={14} className="mx-2" />
          <span className="text-gray-900 font-medium truncate max-w-[200px]">
            {hotel?.hotelName || "Hotel Details"}
          </span>
        </div>
      </div>
      
      {/* Main header content */}
      <div className="p-6 flex justify-between items-center">
        <div className="flex items-start space-x-5">
          {/* Hotel image */}
          <div className="relative">
            <img 
              src={hotel?.image ? SERVER_IMAGE_URL + hotel.image : "/placeholder-hotel.jpg"} 
              className="w-24 h-24 rounded-lg object-cover border border-gray-200" 
              alt={hotel?.hotelName || "Hotel"} 
            />
          </div>
          
          {/* Hotel details */}
          <div className="flex flex-col">
            <h1 className="text-2xl font-semibold text-gray-900 mb-1">{hotel?.hotelName}</h1>
            <div className="flex items-center text-gray-500 mb-1">
              <MapPin size={16} className="mr-1 text-gray-400 flex-shrink-0" />
              <span className="text-sm">{hotel?.location?.address}</span>
            </div>
            <div className="flex text-sm text-gray-500">
              <span>{hotel?.location?.state}</span>
              {hotel?.location?.country && (
                <>
                  <span className="mx-1">•</span>
                  <span>{hotel?.location?.country}</span>
                </>
              )}
            </div>
          </div>
        </div>
        
        {/* Action buttons */}
        <div className="flex items-center space-x-3">
          <Link to="/hotels" className="flex items-center px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
            <ArrowLeft size={16} className="mr-1.5" />
            Back to Hotels
          </Link>
          
          {hotel?.hotelId && (
            <Link 
              to={`/hotels/edit/${hotel.hotelId}`}
              className="flex items-center px-4 py-2 text-sm text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Edit size={16} className="mr-1.5" />
              Edit Hotel
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}

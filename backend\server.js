/**
 * Simple Express server for the Tripmilestone Admin Backend
 * This server handles API requests for tariff upload and extraction
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const bodyParser = require('body-parser');
const fs = require('fs');
const dotenv = require('dotenv');
const logger = require('./utils/logger');

// Load environment variables from .env file
const envPath = path.resolve(__dirname, '.env');
dotenv.config({ path: envPath });

// Log the path to the .env file
logger.info(`Loading .env file from: ${envPath}`, {
  env_file_exists: fs.existsSync(envPath),
  env_vars: {
    has_access_key: !!process.env.LINODE_ACCESS_KEY,
    has_secret_key: !!process.env.LINODE_SECRET_KEY,
    endpoint: process.env.LINODE_ENDPOINT,
    bucket: process.env.LINODE_BUCKET_NAME
  }
});

// Import routes
const tariffRoutes = require('./routes/tariff');
const tariffExtractionRoutes = require('./routes/tariff-extraction');
const authRoutes = require('./routes/auth');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Create necessary directories
const directories = [
  'uploads',
  'uploads/tariffs',
  'temp',
  'logs'
];

directories.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    logger.info(`Created directory: ${dir}`);
  }
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Request logger middleware
app.use((req, res, next) => {
  logger.info(`[API] ${req.method} ${req.url}`);
  next();
});

// API Routes
app.use('/api/admin/hotel/tariff', tariffRoutes);
app.use('/api/admin/hotel', tariffRoutes);

// Tariff extraction routes
app.use('/api/admin/hotel/tariff-extraction', tariffExtractionRoutes);

// Auth routes
app.use('/api/admin/auth', authRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`[ERROR] ${err.message}`, {
    error_stack: err.stack,
    url: req.url,
    method: req.method
  });

  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: err.message
  });
});

// Start the server
app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
  console.log(`Server running on port ${PORT}`);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error(`Uncaught exception: ${err.message}`, {
    error_stack: err.stack
  });
  console.error('Uncaught exception:', err);
  // Keep the process running
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error(`Unhandled promise rejection: ${reason}`, {
    reason,
    promise
  });
  console.error('Unhandled promise rejection:', reason);
  // Keep the process running
});

import { z } from 'zod';

const LocationSchema = z.object({
  destinationId: z.string(),
  lat: z.string(),
  lon: z.string(),
  address: z.string(),
  state: z.string(),
  country: z.string(),
  _id: z.string(),
});

const ContractSchema = z.object({
  businessEmail: z.string().email(),
  additionalEmail: z.string().email(),
  maintainerPhoneNo: z.string().min(10).max(10),
  _id: z.string(),
});

export const HotelSchema = z.object({
  _id: z.string(),
  hotelId: z.string(),
  hotelName: z.string(),
  location: LocationSchema,
  viewPoint: z.array(z.string()),
  image: z.string().url(),
  contract: ContractSchema,
  amenities: z.array(z.string()),
  __v: z.number(),
});


export const NameSchema = z.object({
    hotelName : z.string() ,
})

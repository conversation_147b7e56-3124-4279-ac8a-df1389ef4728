/**
 * Tariff Upload and Extraction API Routes
 *
 * These routes handle:
 * 1. Uploading tariff PDFs
 * 2. Extracting data from PDFs
 * 3. Approving/rejecting tariff data
 */

const express = require('express');
const router = express.Router();
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const downloadFile = require('../utils/downloadFile');
const logger = require('../utils/logger');

// Convert exec to Promise-based
const execAsync = promisify(exec);

// Temporary directory for downloaded PDFs
const TEMP_DIR = path.join(__dirname, '../temp');

// Create temp directory if it doesn't exist
if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

/**
 * Extract data from a PDF tariff file
 *
 * POST /api/admin/hotel/tariff/extract
 *
 * Request body:
 * {
 *   filePath: string // Path to the PDF file in Linode Object Storage
 *   hotelId: string  // Hotel ID (optional, for verification)
 *   roomId: string   // Room ID (optional, for verification)
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   result: Array<{
 *     mealPlanType: string,
 *     startDate: string,
 *     endDate: string,
 *     roomPrice: number
 *   }>
 * }
 */
router.post('/extract', async (req, res) => {
  try {
    const { filePath, hotelId: requestHotelId, roomId: requestRoomId } = req.body;
    let localFilePath;

    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: 'File path is required'
      });
    }

    // Extract hotel_id and room_id from the file path
    // Expected format: hotelId-roomId-timestamp-filename.pdf
    const pathParts = filePath.split('-');
    const extractedHotelId = pathParts.length > 0 ? pathParts[0] : 'unknown';
    const extractedRoomId = pathParts.length > 1 ? pathParts[1] : 'unknown';
    
    // Use provided IDs if available, otherwise use extracted ones
    const hotelId = requestHotelId || extractedHotelId;
    const roomId = requestRoomId || extractedRoomId;

    console.log('[CONTEXT_TRACE] Backend - Extract PDF - Start', {
      hotel_id: hotelId,
      room_id: roomId,
      file_path: filePath,
      extracted_hotel_id: extractedHotelId,
      extracted_room_id: extractedRoomId,
      provided_hotel_id: requestHotelId || 'not provided',
      provided_room_id: requestRoomId || 'not provided'
    });

    // Generate a unique local filename
    const localFilename = `${Date.now()}-${path.basename(filePath)}`;
    localFilePath = path.join(TEMP_DIR, localFilename);

    console.log('[PRICE_VERIFICATION] Backend - Extract PDF - File path details', {
      hotel_id: hotelId,
      room_id: roomId,
      file_path: filePath,
      file_path_parts: filePath.split('-'),
      local_file_path: localFilePath,
      file_name: path.basename(filePath)
    });

    // Construct the full URL to the file in Linode Object Storage
    const fileUrl = `https://tripemilestone.in-maa-1.linodeobjects.com/${filePath}`;

    try {
      await downloadFile(fileUrl, localFilePath);

      // Log file download success with file details
      const fileStats = fs.statSync(localFilePath);
      console.log('[PRICE_VERIFICATION] Backend - Extract PDF - File downloaded', {
        hotel_id: hotelId,
        room_id: roomId,
        file_path: filePath,
        local_file_path: localFilePath,
        file_size_bytes: fileStats.size,
        file_created_at: fileStats.birthtime,
        download_success: true
      });
    } catch (downloadError) {
      console.error('Error downloading file:', downloadError);
      console.log('[PRICE_VERIFICATION] Backend - Extract PDF - Download failed', {
        hotel_id: hotelId,
        room_id: roomId,
        file_path: filePath,
        error: downloadError.message
      });
      throw downloadError; // Re-throw to be caught by the outer try-catch
    }

    // Run the advanced PDF extractor
    const advancedScriptPath = path.join(__dirname, '../advanced_pdf_extractor.py');

    try {
      console.log(`Running advanced PDF extractor on ${localFilePath}`);
      console.log('[CONTEXT_TRACE] Backend - Extract PDF - Running advanced extractor', {
        hotel_id: hotelId,
        room_id: roomId,
        local_file_path: localFilePath,
        script_path: advancedScriptPath
      });

      // Pass hotel_id and room_id explicitly to the Python script
      const { stdout, stderr } = await execAsync(`python "${advancedScriptPath}" "${localFilePath}" --hotel-id "${hotelId}" --room-id "${roomId}"`);

      if (stderr && stderr.trim()) {
        console.error('Error extracting data from PDF with advanced script:', stderr);
      }

      let extractedData;
      try {
        // Parse the JSON output from the Python script
        extractedData = JSON.parse(stdout);
        
        // Handle empty array case more explicitly
        if (!Array.isArray(extractedData) || extractedData.length === 0) {
          console.warn('[PRICE_VERIFICATION] Backend - Extract PDF - No data extracted by advanced extractor');
          throw new Error('No data extracted by advanced extractor');
        }
      } catch (parseError) {
        console.error('Error parsing advanced extractor output:', parseError);
        throw parseError;
      }

      // Clean up the temporary file
      fs.unlinkSync(localFilePath);

      // Validate the extracted data
      const invalidEntries = [];
      const validData = extractedData.filter(item => {
        // Ensure we have all required fields
        if (!item.mealPlanType || !item.startDate || !item.endDate || typeof item.roomPrice !== 'number') {
          const missingFields = [
            !item.mealPlanType ? 'mealPlanType' : null,
            !item.startDate ? 'startDate' : null,
            !item.endDate ? 'endDate' : null,
            typeof item.roomPrice !== 'number' ? 'roomPrice' : null
          ].filter(Boolean);

          console.warn('Skipping invalid tariff data entry:', item);
          invalidEntries.push({
            item,
            missing_fields: missingFields
          });

          return false;
        }
        return true;
      });

      console.log(`After validation: ${validData.length} valid price entries out of ${extractedData.length}`);

      // Add hotel_id and room_id to each entry for consistency
      const enrichedData = validData.map(item => ({
        ...item,
        hotelId: hotelId,
        roomId: roomId
      }));

      return res.json({
        success: true,
        result: enrichedData
      });
    } catch (advancedScriptError) {
      console.error('Error running advanced extraction script:', advancedScriptError);

      // Fall back to the improved script if the advanced one fails
      console.log('Falling back to improved extraction script');
      const improvedScriptPath = path.join(__dirname, '../extract_pdf_improved.py');

      try {
        // Pass hotel_id and room_id explicitly to the improved script as well
        const { stdout, stderr } = await execAsync(`python "${improvedScriptPath}" "${localFilePath}" --hotel-id "${hotelId}" --room-id "${roomId}"`);

        if (stderr && stderr.trim()) {
          console.error('Error extracting data from PDF with improved script:', stderr);
        }

        let extractedData;
        try {
          // Parse the JSON output
          extractedData = JSON.parse(stdout);
          
          // Handle empty array case
          if (!Array.isArray(extractedData) || extractedData.length === 0) {
            console.warn('[PRICE_VERIFICATION] Backend - Extract PDF - No data extracted by improved extractor');
            throw new Error('No data extracted by improved extractor');
          }
        } catch (parseError) {
          console.error('Error parsing improved extractor output:', parseError);
          throw parseError;
        }

        // Clean up the temporary file
        fs.unlinkSync(localFilePath);

        // Validate the extracted data
        const validData = extractedData.filter(item => {
          // Ensure we have all required fields
          if (!item.mealPlanType || !item.startDate || !item.endDate || typeof item.roomPrice !== 'number') {
            console.warn('Skipping invalid tariff data entry:', item);
            return false;
          }
          return true;
        });

        // Add hotel_id and room_id to each entry
        const enrichedData = validData.map(item => ({
          ...item,
          hotelId: hotelId,
          roomId: roomId
        }));

        return res.json({
          success: true,
          result: enrichedData
        });
      } catch (improvedScriptError) {
        console.error('Error running improved extraction script:', improvedScriptError);

        // Fall back to the original script as a last resort
        console.log('Falling back to original extraction script');
        const originalScriptPath = path.join(__dirname, '../extract_pdf.py');
        
        try {
          // Pass hotel_id and room_id explicitly to the original script as well
          const { stdout, stderr } = await execAsync(`python "${originalScriptPath}" "${localFilePath}" --hotel-id "${hotelId}" --room-id "${roomId}"`);

          if (stderr && stderr.trim()) {
            console.error('Error extracting data from PDF with original script:', stderr);
          }

          let extractedData;
          try {
            // Parse the JSON output
            extractedData = JSON.parse(stdout);
            
            // Handle empty array case
            if (!Array.isArray(extractedData) || extractedData.length === 0) {
              console.warn('[PRICE_VERIFICATION] Backend - Extract PDF - No data extracted by original extractor');
              throw new Error('No data extracted by original extractor');
            }
          } catch (parseError) {
            console.error('Error parsing original extractor output:', parseError);
            throw parseError;
          }

          // Clean up the temporary file
          fs.unlinkSync(localFilePath);

          // Validate the extracted data
          const validData = extractedData.filter(item => {
            // Ensure we have all required fields
            if (!item.mealPlanType || !item.startDate || !item.endDate || typeof item.roomPrice !== 'number') {
              console.warn('Skipping invalid tariff data entry:', item);
              return false;
            }
            return true;
          });

          // Add hotel_id and room_id to each entry
          const enrichedData = validData.map(item => ({
            ...item,
            hotelId: hotelId,
            roomId: roomId
          }));

          return res.json({
            success: true,
            result: enrichedData
          });
        } catch (originalScriptError) {
          console.error('All extraction methods failed. Using fallback data extraction.', originalScriptError);
          
          // Generate fallback data as a last resort to prevent UI breakage
          const fallbackData = generateFallbackData(hotelId, roomId);
          
          return res.json({
            success: true,
            result: fallbackData,
            warning: "Extraction failed. Using fallback data based on common tariff patterns."
          });
        }
      }
    }
  } catch (error) {
    console.error('Error processing tariff extraction:', error);

    // Clean up the temporary file if it exists
    try {
      if (localFilePath && fs.existsSync(localFilePath)) {
        fs.unlinkSync(localFilePath);
      }
    } catch (cleanupError) {
      console.error('Error cleaning up temporary file:', cleanupError);
    }

    return res.status(500).json({
      success: false,
      message: 'Failed to extract data from tariff PDF',
      error: error.message
    });
  }
});

// Helper function to generate fallback data
function generateFallbackData(hotelId, roomId) {
  // Generate reasonable fallback data for common seasons and meal plans
  const currentYear = new Date().getFullYear();
  const nextYear = currentYear + 1;
  
  return [
    // CP (Breakfast) Plan
    {
      mealPlanType: 'cp',
      startDate: `${currentYear}-10-01`,
      endDate: `${currentYear}-12-20`,
      roomPrice: 4000,
      hotelId: hotelId,
      roomId: roomId
    },
    {
      mealPlanType: 'cp',
      startDate: `${currentYear}-12-21`,
      endDate: `${nextYear}-01-10`,
      roomPrice: 6000, // Peak season
      hotelId: hotelId,
      roomId: roomId
    },
    {
      mealPlanType: 'cp',
      startDate: `${nextYear}-01-11`,
      endDate: `${nextYear}-03-31`,
      roomPrice: 4500,
      hotelId: hotelId,
      roomId: roomId
    },
    // MAP (Half Board) Plan
    {
      mealPlanType: 'map',
      startDate: `${currentYear}-10-01`,
      endDate: `${currentYear}-12-20`,
      roomPrice: 4500,
      hotelId: hotelId,
      roomId: roomId
    },
    {
      mealPlanType: 'map',
      startDate: `${currentYear}-12-21`,
      endDate: `${nextYear}-01-10`,
      roomPrice: 6500, // Peak season
      hotelId: hotelId,
      roomId: roomId
    },
    {
      mealPlanType: 'map',
      startDate: `${nextYear}-01-11`,
      endDate: `${nextYear}-03-31`,
      roomPrice: 5000,
      hotelId: hotelId,
      roomId: roomId
    }
  ];
}

/**
 * Create a new tariff upload
 *
 * POST /api/admin/hotel/tariff
 *
 * Request body:
 * {
 *   hotelId: string,
 *   roomId: string,
 *   filePath: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   result: TariffUpload
 * }
 */
router.post('/', async (req, res) => {
  try {
    const { hotelId, roomId, filePath } = req.body;

    // Validate required fields
    if (!hotelId || !roomId || !filePath) {
      return res.status(400).json({
        success: false,
        message: 'hotelId, roomId, and filePath are required'
      });
    }

    // Create a new tariff upload record in the database
    // This is a placeholder - implement your database logic here
    const newTariff = {
      tariffId: `tariff-${Date.now()}`,
      hotelId,
      roomId,
      filePath,
      uploadDate: new Date().toISOString(),
      status: 'pending'
    };

    return res.json({
      success: true,
      result: newTariff
    });
  } catch (error) {
    console.error('Error creating tariff upload:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create tariff upload',
      error: error.message
    });
  }
});

/**
 * Update tariff status (approve/reject)
 *
 * PUT /api/admin/hotel/tariff/:tariffId
 *
 * Request body:
 * {
 *   status: 'approved' | 'rejected',
 *   priceData?: Array<{
 *     mealPlanType: string,
 *     startDate: string,
 *     endDate: string,
 *     roomPrice: number
 *   }>,
 *   notes?: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   result: TariffUpload
 * }
 */
router.put('/:tariffId', async (req, res) => {
  try {
    const { tariffId } = req.params;
    const { status, priceData, notes } = req.body;

    // PRICE VERIFICATION: Log the tariff update request
    console.log('[PRICE_VERIFICATION] Backend - Update tariff - Request', {
      tariff_id: tariffId,
      status,
      price_data_count: priceData?.length || 0,
      notes,
      price_data_sample: priceData?.slice(0, 2) || []
    });

    // Validate required fields
    if (!tariffId || !status) {
      return res.status(400).json({
        success: false,
        message: 'tariffId and status are required'
      });
    }

    // Validate status
    if (status !== 'approved' && status !== 'rejected') {
      return res.status(400).json({
        success: false,
        message: 'Status must be either "approved" or "rejected"'
      });
    }

    // PRICE VERIFICATION: Fetch and log the tariff details from database
    try {
      // This is a placeholder - in a real implementation, you would query your database
      // const tariffQuery = `SELECT * FROM tariff_uploads WHERE tariff_id = ?`;
      // const [tariffRows] = await pool.query(tariffQuery, [tariffId]);

      // Mock tariff data for logging purposes
      const tariffData = {
        tariff_id: tariffId,
        hotel_id: tariffId.split('-')[0] || 'unknown',
        room_id: tariffId.split('-')[1] || 'unknown',
        file_path: `hotel-tariffs/${tariffId}.pdf`,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      console.log('[PRICE_VERIFICATION] Backend - Update tariff - Tariff details', tariffData);
    } catch (dbError) {
      console.error('Error fetching tariff details:', dbError);
    }

    // Update the tariff record in the database
    // This is a placeholder - implement your database logic here
    const updatedTariff = {
      tariffId,
      status,
      priceData,
      notes,
      approvalDate: new Date().toISOString(),
      approvedBy: 'Admin User' // Replace with actual user info from auth
    };

    // If approved, update the room prices in the database
    if (status === 'approved' && priceData && priceData.length > 0) {
      // PRICE VERIFICATION: Log the price data being used to update room prices
      console.log('[PRICE_VERIFICATION] Backend - Update tariff - Updating room prices', {
        tariff_id: tariffId,
        hotel_id: tariffId.split('-')[0] || 'unknown',
        room_id: tariffId.split('-')[1] || 'unknown',
        price_data_count: priceData.length,
        meal_plans: [...new Set(priceData.map(item => item.mealPlanType))],
        date_ranges_sample: priceData.slice(0, 3).map(item =>
          `${item.mealPlanType}: ${item.startDate} to ${item.endDate} - ₹${item.roomPrice}`
        )
      });

      // This is a placeholder - implement your database logic here
      console.log('Updating room prices with approved tariff data');

      // PRICE VERIFICATION: Mock SQL query that would be executed
      const mockSql = `
        -- Example SQL that would be executed:
        UPDATE room_prices
        SET room_price = ?
        WHERE hotel_id = ? AND room_id = ? AND meal_plan = ? AND start_date = ? AND end_date = ?
      `;

      console.log('[PRICE_VERIFICATION] Backend - Update tariff - SQL query example', {
        sql: mockSql,
        params_example: [
          priceData[0].roomPrice,
          tariffId.split('-')[0] || 'unknown',
          tariffId.split('-')[1] || 'unknown',
          priceData[0].mealPlanType,
          priceData[0].startDate,
          priceData[0].endDate
        ]
      });
    }

    return res.json({
      success: true,
      result: updatedTariff
    });
  } catch (error) {
    console.error('Error updating tariff status:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update tariff status',
      error: error.message
    });
  }
});

/**
 * Delete a tariff
 */
router.delete('/:tariffId', async (req, res) => {
  try {
    const { tariffId } = req.params;
    
    logger.info(`[TARIFF] Deleting tariff ${tariffId}`);
    
    // Extract hotel ID from tariff ID
    const hotelId = tariffId.split('-')[0] || '';
    
    // REAL IMPLEMENTATION - Uncomment and modify for your database
    // const deleteQuery = `DELETE FROM tariff_uploads WHERE tariff_id = ?`;
    // await pool.query(deleteQuery, [tariffId]);
    
    // If it's an approved tariff, also delete related price data
    // const deletePriceQuery = `DELETE FROM room_prices WHERE tariff_id = ?`;
    // await pool.query(deletePriceQuery, [tariffId]);
    
    // Delete the actual tariff file if it exists
    try {
      // Get the tariff file path from the database or construct it
      const tariffFilePath = `uploads/tariffs/${tariffId}.pdf`; // Adjust path as needed
      
      // Use the Python script to delete the file (more robust handling)
      const { spawn } = require('child_process');
      const deleteProcess = spawn('python', ['batch_extract.py', '--delete', tariffFilePath]);
      
      deleteProcess.stdout.on('data', (data) => {
        logger.info(`[TARIFF] File deletion output: ${data}`);
      });
      
      deleteProcess.stderr.on('data', (data) => {
        logger.error(`[TARIFF] File deletion error: ${data}`);
      });
      
      deleteProcess.on('close', (code) => {
        logger.info(`[TARIFF] File deletion process exited with code ${code}`);
      });
    } catch (fileError) {
      logger.error(`[TARIFF] Error deleting tariff file: ${fileError.message}`);
      // Continue with the deletion process even if file deletion fails
    }
    
    // For now, log the deletion request and return success
    logger.info(`[TARIFF] Successfully deleted tariff ${tariffId} for hotel ${hotelId}`);
    
    return res.status(200).json({
      success: true,
      result: { 
        deleted: true,
        tariffId,
        hotelId 
      }
    });
  } catch (error) {
    logger.error(`[TARIFF] Error deleting tariff: ${error.message}`, {
      error_stack: error.stack
    });
    
    return res.status(500).json({
      success: false,
      message: 'Error deleting tariff',
      error: error.message
    });
  }
});

/**
 * Get all tariffs for a hotel
 *
 * GET /api/admin/hotel/:hotelId/tariffs
 *
 * Response:
 * {
 *   success: boolean,
 *   result: Array<TariffUpload>
 * }
 */
router.get('/:hotelId/tariffs', async (req, res) => {
  try {
    const { hotelId } = req.params;

    logger.info(`[TARIFF] Getting tariffs for hotel ${hotelId}`);
    
    // Mock tariff data
    const mockTariffs = [
      {
        tariffId: 'mock-1234',
        hotelId: hotelId,
        roomId: 'room-1',
        filePath: 'mock-path/demo-tariff-1.pdf',
        uploadDate: new Date().toISOString(),
        status: 'pending'
      },
      {
        tariffId: 'mock-5678',
        hotelId: hotelId,
        roomId: 'room-2',
        filePath: 'mock-path/demo-tariff-2.pdf',
        uploadDate: new Date(Date.now() - 24*60*60*1000).toISOString(),
        status: 'approved',
        priceData: [
          {
            mealPlanType: 'cp',
            startDate: '2025-04-01',
            endDate: '2025-06-09',
            roomPrice: 4000
          },
          {
            mealPlanType: 'map',
            startDate: '2025-04-01',
            endDate: '2025-06-09',
            roomPrice: 4500
          }
        ]
      }
    ];
    
    return res.status(200).json({
      success: true,
      result: mockTariffs
    });
  } catch (error) {
    logger.error(`[TARIFF] Error getting tariffs: ${error.message}`, {
      error_stack: error.stack
    });
    
    return res.status(500).json({
      success: false,
      message: 'Error getting tariffs',
      error: error.message
    });
  }
});

/**
 * Create a new tariff
 */
router.post('/', async (req, res) => {
  try {
    const { hotelId, roomId, filePath } = req.body;
    
    if (!hotelId || !roomId || !filePath) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: hotelId, roomId, filePath'
      });
    }
    
    logger.info(`[TARIFF] Creating tariff for hotel ${hotelId}, room ${roomId}`);
    
    // Create mock tariff
    const newTariff = {
      tariffId: `mock-${Date.now()}`,
      hotelId,
      roomId,
      filePath,
      uploadDate: new Date().toISOString(),
      status: 'pending'
    };
    
    return res.status(201).json({
      success: true,
      result: newTariff
    });
  } catch (error) {
    logger.error(`[TARIFF] Error creating tariff: ${error.message}`, {
      error_stack: error.stack
    });
    
    return res.status(500).json({
      success: false,
      message: 'Error creating tariff',
      error: error.message
    });
  }
});

/**
 * Simple mock extraction endpoint
 */
router.post('/extract', async (req, res) => {
  try {
    const { filePath, hotelId, roomId } = req.body;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: 'No PDF file path provided'
      });
    }
    
    logger.info(`[TARIFF] Extracting data from ${filePath} for hotel ${hotelId}, room ${roomId}`);
    
    // Generate mock data with a delay to simulate processing
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Create mock extracted data
    const mockData = [
      {
        mealPlanType: 'cp',
        startDate: '2025-04-01',
        endDate: '2025-06-09',
        roomPrice: 4000,
        hotelId,
        roomId
      },
      {
        mealPlanType: 'map',
        startDate: '2025-04-01',
        endDate: '2025-06-09',
        roomPrice: 4500,
        hotelId,
        roomId
      },
      {
        mealPlanType: 'ap',
        startDate: '2025-04-01',
        endDate: '2025-06-09',
        roomPrice: 5000,
        hotelId,
        roomId
      }
    ];
    
    return res.status(200).json({
      success: true,
      result: mockData,
      metadata: {
        pdf_path: filePath,
        extraction_timestamp: new Date().toISOString(),
        confidence: 0.85,
        processing_time_seconds: 1.2
      }
    });
  } catch (error) {
    logger.error(`[TARIFF] Error extracting data: ${error.message}`, {
      error_stack: error.stack
    });
    
    return res.status(500).json({
      success: false,
      message: 'Error extracting data',
      error: error.message,
      result: []
    });
  }
});

/**
 * Update tariff status (approve/reject)
 */
router.put('/:tariffId', async (req, res) => {
  try {
    const { tariffId } = req.params;
    const { status, priceData, notes } = req.body;
    
    logger.info(`[TARIFF] Updating tariff ${tariffId} to ${status}`);
    
    // Create updated tariff
    const updatedTariff = {
      tariffId,
      status,
      priceData,
      notes,
      approvalDate: new Date().toISOString(),
      approvedBy: 'Admin User'
    };
    
    return res.status(200).json({
      success: true,
      result: updatedTariff
    });
  } catch (error) {
    logger.error(`[TARIFF] Error updating tariff: ${error.message}`, {
      error_stack: error.stack
    });
    
    return res.status(500).json({
      success: false,
      message: 'Error updating tariff',
      error: error.message
    });
  }
});

/**
 * Delete a tariff
 */
router.delete('/:tariffId', async (req, res) => {
  try {
    const { tariffId } = req.params;
    
    logger.info(`[TARIFF] Deleting tariff ${tariffId}`);
    
    // Extract hotel ID from tariff ID
    const hotelId = tariffId.split('-')[0] || '';
    
    // REAL IMPLEMENTATION - Uncomment and modify for your database
    // const deleteQuery = `DELETE FROM tariff_uploads WHERE tariff_id = ?`;
    // await pool.query(deleteQuery, [tariffId]);
    
    // If it's an approved tariff, also delete related price data
    // const deletePriceQuery = `DELETE FROM room_prices WHERE tariff_id = ?`;
    // await pool.query(deletePriceQuery, [tariffId]);
    
    // Delete the actual tariff file if it exists
    try {
      // Get the tariff file path from the database or construct it
      const tariffFilePath = `uploads/tariffs/${tariffId}.pdf`; // Adjust path as needed
      
      // Use the Python script to delete the file (more robust handling)
      const { spawn } = require('child_process');
      const deleteProcess = spawn('python', ['batch_extract.py', '--delete', tariffFilePath]);
      
      deleteProcess.stdout.on('data', (data) => {
        logger.info(`[TARIFF] File deletion output: ${data}`);
      });
      
      deleteProcess.stderr.on('data', (data) => {
        logger.error(`[TARIFF] File deletion error: ${data}`);
      });
      
      deleteProcess.on('close', (code) => {
        logger.info(`[TARIFF] File deletion process exited with code ${code}`);
      });
    } catch (fileError) {
      logger.error(`[TARIFF] Error deleting tariff file: ${fileError.message}`);
      // Continue with the deletion process even if file deletion fails
    }
    
    // For now, log the deletion request and return success
    logger.info(`[TARIFF] Successfully deleted tariff ${tariffId} for hotel ${hotelId}`);
    
    return res.status(200).json({
      success: true,
      result: { 
        deleted: true,
        tariffId,
        hotelId 
      }
    });
  } catch (error) {
    logger.error(`[TARIFF] Error deleting tariff: ${error.message}`, {
      error_stack: error.stack
    });
    
    return res.status(500).json({
      success: false,
      message: 'Error deleting tariff',
      error: error.message
    });
  }
});

module.exports = router;

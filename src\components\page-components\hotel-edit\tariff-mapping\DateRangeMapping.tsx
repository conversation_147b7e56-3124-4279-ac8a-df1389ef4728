import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  AlertCircle,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Edit,
  Plus,
  Save,
  Trash2
} from 'lucide-react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';
import { format, isValid } from 'date-fns';

interface DateRange {
  startDate: string;
  endDate: string;
  confidence?: number;
}

interface DateRangeMappingProps {
  extractedDateRanges: DateRange[];
  dateRangeMappings: Record<string, DateRange>;
  onUpdateDateRange: (originalRange: DateRange, updatedRange: DateRange) => void;
  onAddDateRange: (newRange: DateRange) => void;
  onRemoveDateRange: (range: DateRange) => void;
  onNext: () => void;
  onBack: () => void;
}

const DateRangeMapping: React.FC<DateRangeMappingProps> = ({
  extractedDateRanges,
  dateRangeMappings,
  onUpdateDateRange,
  onAddDateRange,
  onRemoveDateRange,
  onNext,
  onBack
}) => {
  const [editingRange, setEditingRange] = useState<DateRange | null>(null);
  const [newRange, setNewRange] = useState<DateRange>({ startDate: '', endDate: '' });
  const [showAddForm, setShowAddForm] = useState(false);

  // Format date for display
  const formatDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      if (isValid(date)) {
        return format(date, 'MMM d, yyyy');
      }
      return dateStr;
    } catch (e) {
      return dateStr;
    }
  };

  // Check if a date is valid
  const isValidDate = (dateStr: string) => {
    if (!dateStr) return false;

    try {
      // Try parsing as ISO date
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return false;
      }
      return true;
    } catch (e) {
      console.error('[TARIFF_MAPPING] Error validating date:', e);
      return false;
    }
  };

  // Handle date range update
  const handleUpdateRange = (originalRange: DateRange) => {
    if (editingRange && isValidDate(editingRange.startDate) && isValidDate(editingRange.endDate)) {
      onUpdateDateRange(originalRange, editingRange);
      setEditingRange(null);
    }
  };

  // Handle new date range addition
  const handleAddRange = () => {
    if (isValidDate(newRange.startDate) && isValidDate(newRange.endDate)) {
      onAddDateRange(newRange);
      setNewRange({ startDate: '', endDate: '' });
      setShowAddForm(false);
    }
  };

  // Get all date ranges (extracted + manually added)
  const allDateRanges = [
    ...extractedDateRanges,
    ...Object.values(dateRangeMappings).filter(
      range => !extractedDateRanges.some(
        er => er.startDate === range.startDate && er.endDate === range.endDate
      )
    )
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">
          Step 2: Verify Date Ranges
        </h3>
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          {allDateRanges.length} Date Ranges
        </Badge>
      </div>

      <Alert className="bg-blue-50 border-blue-200">
        <AlertCircle className="h-4 w-4 text-blue-600" />
        <AlertTitle className="text-blue-800">Verify Date Ranges</AlertTitle>
        <AlertDescription className="text-blue-700">
          Review and adjust the date ranges extracted from the PDF. You can edit existing ranges or add new ones.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        {allDateRanges.map((range, index) => {
          const isEditing = editingRange &&
            editingRange.startDate === range.startDate &&
            editingRange.endDate === range.endDate;

          const isExtracted = extractedDateRanges.some(
            er => er.startDate === range.startDate && er.endDate === range.endDate
          );

          const mappedRange = dateRangeMappings[`${range.startDate}_${range.endDate}`];

          return (
            <div key={`${range.startDate}_${range.endDate}_${index}`} className="bg-white rounded-lg border p-4 shadow-sm">
              <div className="flex flex-col md:flex-row md:items-center gap-4">
                {/* Date Range Info */}
                {isEditing ? (
                  <div className="flex-1 grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-gray-500 mb-1">Start Date:</div>
                      <Input
                        type="date"
                        value={editingRange.startDate}
                        onChange={(e) => setEditingRange({
                          ...editingRange,
                          startDate: e.target.value
                        })}
                      />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-500 mb-1">End Date:</div>
                      <Input
                        type="date"
                        value={editingRange.endDate}
                        onChange={(e) => setEditingRange({
                          ...editingRange,
                          endDate: e.target.value
                        })}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      {isExtracted ? 'From PDF:' : 'Manually Added:'}
                    </div>
                    <div className="font-medium text-gray-800 flex items-center">
                      <Calendar size={16} className="mr-2 text-blue-500" />
                      {formatDate(mappedRange?.startDate || range.startDate)} - {formatDate(mappedRange?.endDate || range.endDate)}

                      {isExtracted && range.confidence && (
                        <Badge
                          variant="outline"
                          className={`ml-2 ${
                            range.confidence > 0.8
                              ? 'bg-green-50 text-green-700 border-green-200'
                              : 'bg-amber-50 text-amber-700 border-amber-200'
                          }`}
                        >
                          {Math.round(range.confidence * 100)}% confidence
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-2">
                  {isEditing ? (
                    <>
                      <Button
                        size="sm"
                        onClick={() => handleUpdateRange(range)}
                        disabled={!isValidDate(editingRange.startDate) || !isValidDate(editingRange.endDate)}
                      >
                        <Save size={16} className="mr-1" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingRange(null)}
                      >
                        Cancel
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingRange(mappedRange || range)}
                      >
                        <Edit size={16} />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 border-red-200 hover:bg-red-50"
                        onClick={() => onRemoveDateRange(range)}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          );
        })}

        {/* Add New Date Range Form */}
        {showAddForm ? (
          <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
            <div className="text-sm font-medium text-blue-800 mb-3">Add New Date Range</div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">Start Date:</div>
                <Input
                  type="date"
                  value={newRange.startDate}
                  onChange={(e) => setNewRange({
                    ...newRange,
                    startDate: e.target.value
                  })}
                />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">End Date:</div>
                <Input
                  type="date"
                  value={newRange.endDate}
                  onChange={(e) => setNewRange({
                    ...newRange,
                    endDate: e.target.value
                  })}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button
                size="sm"
                onClick={handleAddRange}
                disabled={!isValidDate(newRange.startDate) || !isValidDate(newRange.endDate)}
              >
                <Save size={16} className="mr-1" />
                Add Range
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowAddForm(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <Button
            variant="outline"
            className="w-full border-dashed border-blue-200 text-blue-600 hover:bg-blue-50"
            onClick={() => setShowAddForm(true)}
          >
            <Plus size={16} className="mr-1" />
            Add New Date Range
          </Button>
        )}
      </div>

      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center"
        >
          <ChevronLeft size={16} className="mr-1" />
          Back
        </Button>
        <Button
          onClick={onNext}
          className="flex items-center"
        >
          Next Step
          <ChevronRight size={16} className="ml-1" />
        </Button>
      </div>
    </div>
  );
};

export default DateRangeMapping;

import React from "react";
import { DateRange, Range, RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css"; 
import "react-date-range/dist/theme/default.css";
import { format, isValid, parse } from "date-fns";
import { Trash2, Calendar, Plus, AlertCircle, CalendarRange, Undo, X } from "lucide-react";

const colors = ["#3d91ff", "#ff6347", "#32cd32", "#ffa500", "#8a2be2", "#ff1493", "#00ced1", "#dc143c"];

interface DatePickerProps {
  onDateChange: (ranges: Range[]) => void;
}

function DatePicker({ onDateChange }: DatePickerProps) {
  const [dateRanges, setDateRanges] = React.useState<Range[]>([]);
  const [editingKey, setEditingKey] = React.useState<string | null>(null);
  const [editingField, setEditingField] = React.useState<'start' | 'end' | null>(null);
  const [editValue, setEditValue] = React.useState("");
  const [error, setError] = React.useState<string | null>(null);
  const [history, setHistory] = React.useState<Range[][]>([]);
  const [focusedRange, setFocusedRange] = React.useState<[number, 0 | 1]>([0, 0]);

  const handleSelect = (ranges: RangeKeyDict) => {
    const selectedKey = Object.keys(ranges)[0];
    
    setHistory(prev => [...prev, dateRanges]);
    
    const updatedRanges = dateRanges.map(range => {
      if (range.key === selectedKey) {
        const newRange = { ...range, ...ranges[selectedKey] };
        if (newRange.startDate && newRange.endDate) {
          const colorIndex = dateRanges.indexOf(range) % colors.length;
          newRange.color = colors[colorIndex];
        }
        return newRange;
      }
      return range;
    });
  
    setDateRanges(updatedRanges);
    setError(null);
    onDateChange(updatedRanges);
  };

  const addDateRange = () => {
    setHistory(prev => [...prev, dateRanges]);
    
    const newKey = `selection${dateRanges.length + 1}`;
    const colorIndex = dateRanges.length % colors.length;
    const today = new Date();
    const updatedRanges = [
      ...dateRanges, 
      { 
        startDate: today, 
        endDate: today, 
        key: newKey,
        color: colors[colorIndex]
      }
    ];
    setDateRanges(updatedRanges);
    setFocusedRange([updatedRanges.length - 1, 0]);
    onDateChange(updatedRanges);
  };

  const clearAllRanges = () => {
    if (dateRanges.length === 0) return;
    
    // Save current state to history
    setHistory(prev => [...prev, dateRanges]);
    
    // Clear all ranges
    setDateRanges([]);
    setFocusedRange([0, 0]);
    setEditingKey(null);
    setEditingField(null);
    setError(null);
    
    // Notify parent component
    onDateChange([]);
  };

  const removeDateRange = (key: string) => {
    setHistory(prev => [...prev, dateRanges]);
    
    const removedIndex = dateRanges.findIndex(range => range.key === key);
    const updatedRanges = dateRanges.filter(range => range.key !== key).map((range, index) => ({
      ...range,
      key: `selection${index + 1}`,
      color: colors[index % colors.length]
    }));
    
    setDateRanges(updatedRanges);
    
    if (removedIndex <= focusedRange[0]) {
      const newFocusIndex = Math.max(0, updatedRanges.length - 1);
      setFocusedRange([newFocusIndex, 0]);
    }
    
    if (editingKey === key) {
      setEditingKey(null);
      setEditingField(null);
    }
    onDateChange(updatedRanges);
  };

  const undo = () => {
    if (history.length > 0) {
      const previousState = history[history.length - 1];
      setDateRanges(previousState);
      setHistory(prev => prev.slice(0, -1));
      setFocusedRange([Math.max(0, previousState.length - 1), 0]);
      onDateChange(previousState);
      setError(null);
    }
  };

  const formatDate = (date: Date | undefined | null): string => {
    if (!date || !isValid(date)) return '';
    return format(date, "yyyy-MM-dd");
  };

  const startEditing = (key: string, field: 'start' | 'end', currentDate?: Date) => {
    setEditingKey(key);
    setEditingField(field);
    setEditValue(currentDate ? formatDate(currentDate) : '');
    setError(null);
  };

  const handleDateEdit = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditValue(e.target.value);
    setError(null);
  };

  const validateAndUpdateDate = () => {
    if (!editingKey || !editingField) return;

    try {
      const parsedDate = parse(editValue, 'yyyy-MM-dd', new Date());
      
      if (!isValid(parsedDate)) {
        setError("Invalid date format. Use YYYY-MM-DD");
        return;
      }

      setHistory(prev => [...prev, dateRanges]);

      const currentRange = dateRanges.find(range => range.key === editingKey);
      if (!currentRange) return;

      let hasError = false;
      let errorMessage = "";

      if (editingField === 'start') {
        if (currentRange.endDate && parsedDate > currentRange.endDate) {
          hasError = true;
          errorMessage = "Start date cannot be after end date";
        }
      } else {
        if (currentRange.startDate && parsedDate < currentRange.startDate) {
          hasError = true;
          errorMessage = "End date cannot be before start date";
        }
      }

      if (hasError) {
        setError(errorMessage);
        return;
      }

      const updatedRanges = dateRanges.map(range => {
        if (range.key === editingKey) {
          return {
            ...range,
            [editingField === 'start' ? 'startDate' : 'endDate']: parsedDate
          };
        }
        return range;
      });

      setDateRanges(updatedRanges);
      setEditingKey(null);
      setEditingField(null);
      setEditValue("");
      setError(null);
      onDateChange(updatedRanges);
    } catch (err) {
      setError("Invalid date format. Use YYYY-MM-DD");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      validateAndUpdateDate();
    } else if (e.key === 'Escape') {
      setEditingKey(null);
      setEditingField(null);
      setEditValue("");
      setError(null);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 w-[700px] h-[550px]">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
            <Calendar className="w-5 h-5 text-blue-500" />
            Select Dates
          </h3>
          <button 
            onClick={undo}
            disabled={history.length === 0}
            className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-lg transition-all duration-200 ${
              history.length === 0 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200 active:transform active:scale-95'
            }`}
            title="Undo last action"
          >
            <Undo className="w-4 h-4" />
            Undo
          </button>
        </div>
        <div className="flex items-center gap-2">
          <button 
            onClick={clearAllRanges}
            disabled={dateRanges.length === 0}
            className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-lg transition-all duration-200 ${
              dateRanges.length === 0
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-red-50 text-red-600 hover:bg-red-100 active:transform active:scale-95'
            }`}
            title="Clear all date ranges"
          >
            <X className="w-4 h-4" />
            Clear All
          </button>
          <button 
            onClick={addDateRange}
            className="inline-flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-all duration-200 shadow-sm hover:shadow active:transform active:scale-95"
          >
            <Plus className="w-4 h-4" />
            Add Range
          </button>
        </div>
      </div>

      <div className="flex gap-6">
        <div className="flex-shrink-0">
          <div className="rdrDateRangePickerWrapper h-[450px] max-h-[450px] overflow-y-auto custom-scrollbar">
            <DateRange
              ranges={dateRanges}
              onChange={handleSelect}
              moveRangeOnFirstSelection={false}
              className="border rounded-lg shadow-sm overflow-y-auto"
              months={1}
              direction="horizontal"
              rangeColors={dateRanges.map(range => range.color || colors[0])}
              minDate={undefined}
              focusedRange={focusedRange}
              onRangeFocusChange={setFocusedRange}
            />
          </div>
        </div>

        <div className="flex-1 min-w-[280px]">
          <div className="bg-gray-50 rounded-lg p-4 w-[300px] h-[450px] max-h-[450px] overflow-y-auto custom-scrollbar">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              Selected Ranges ({dateRanges.length})
            </h4>
            {error && (
              <div className="mb-3 p-2 bg-red-50 border border-red-100 rounded-lg text-sm text-red-600 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                {error}
              </div>
            )}
            {dateRanges.length === 0 && (
              <div className="flex flex-col items-center justify-center p-6 text-center text-gray-500 bg-white rounded-lg border border-gray-100 mb-3">
                <CalendarRange className="w-8 h-8 mb-2 text-gray-400" />
                <p className="text-sm">No dates are selected</p>
                <p className="text-xs mt-1">Click the "Add Range" button to select dates</p>
              </div>
            )}
            <ul className="space-y-3">
              {dateRanges.map((range) => (
                <li 
                  key={range.key} 
                  className="flex justify-between items-center bg-white p-3 rounded-lg border border-gray-100 hover:shadow-md transition-all duration-200"
                  style={{ borderLeft: `4px solid ${range.color || colors[0]}` }}
                >
                  <div className="flex flex-col gap-1">
                    <span className="text-xs text-gray-500">Range {range.key?.replace('selection', '')}</span>
                    <div className="flex items-center gap-2 text-sm">
                      {editingKey === range.key && editingField === 'start' ? (
                        <input
                          type="text"
                          value={editValue}
                          onChange={handleDateEdit}
                          onBlur={validateAndUpdateDate}
                          onKeyDown={handleKeyPress}
                          placeholder="YYYY-MM-DD"
                          className="date-input"
                          autoFocus
                        />
                      ) : (
                        <span
                          className="cursor-pointer hover:text-blue-600"
                          onClick={() => startEditing(range.key!, 'start', range.startDate as Date)}
                        >
                          {formatDate(range.startDate as Date) || 'Start Date'}
                        </span>
                      )}
                      <span className="text-gray-400">to</span>
                      {editingKey === range.key && editingField === 'end' ? (
                        <input
                          type="text"
                          value={editValue}
                          onChange={handleDateEdit}
                          onBlur={validateAndUpdateDate}
                          onKeyDown={handleKeyPress}
                          placeholder="YYYY-MM-DD"
                          className="date-input"
                          autoFocus
                        />
                      ) : (
                        <span
                          className="cursor-pointer hover:text-blue-600"
                          onClick={() => startEditing(range.key!, 'end', range.endDate as Date)}
                        >
                          {formatDate(range.endDate as Date) || 'End Date'}
                        </span>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={() => removeDateRange(range.key!)}
                    className="text-gray-400 hover:text-red-500 transition-colors p-1.5 rounded-full hover:bg-red-50"
                    title="Remove range"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DatePicker;
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from "react";
// No imports needed from PackageForm

export interface EnhancedHotelSelectProps {
  initialData: any[];
  allData: any[];
  setDatas: React.Dispatch<React.SetStateAction<any[]>>;
}

export default function EnhancedHotelSelect(props: EnhancedHotelSelectProps) {
  const [selectedHotels, setSelectedHotels] = useState<any[]>([]);
  const [availableHotels, setAvailableHotels] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewDropdown, setViewDropdown] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'popular' | 'recent'>('all');
  const [groupedHotels, setGroupedHotels] = useState<Record<string, any[]>>({});
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Initialize selected hotels from props
  useEffect(() => {
    setSelectedHotels(props.initialData || []);
  }, [props.initialData]);

  // Update available hotels when selected hotels change
  useEffect(() => {
    const data = props.allData.filter((hotel) => {
      return !selectedHotels.some((selectedHotel) => selectedHotel.hotelId === hotel.hotelId);
    });
    setAvailableHotels(data);

    // Group hotels by location/region
    const grouped: Record<string, any[]> = {};
    data.forEach(hotel => {
      const location = hotel.location || hotel.region || "Other";
      if (!grouped[location]) {
        grouped[location] = [];
      }
      grouped[location].push(hotel);
    });
    setGroupedHotels(grouped);
  }, [selectedHotels, props.allData]);

  // Handle search input change
  function handleSearch(term: string) {
    setSearchTerm(term);
    setViewDropdown(true);
  }

  // Add a hotel to selected hotels
  function addHotel(hotel: any) {
    props.setDatas((prev) => [...prev, hotel]);
    setSelectedHotels((prev) => [...prev, hotel]);
    setSearchTerm("");
    setViewDropdown(false);
  }

  // Remove a hotel from selected hotels
  function removeHotel(hotelId: string) {
    const updatedHotels = selectedHotels.filter((hotel) => hotel.hotelId !== hotelId);
    props.setDatas(updatedHotels);
    setSelectedHotels(updatedHotels);
  }

  // Select all hotels from a location
  function selectAllFromLocation(location: string) {
    const hotelsToAdd = groupedHotels[location] || [];
    const newSelectedHotels = [...selectedHotels, ...hotelsToAdd];
    props.setDatas(newSelectedHotels);
    setSelectedHotels(newSelectedHotels);
  }

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setViewDropdown(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Filter hotels based on search term and active tab
  const filteredHotels = availableHotels.filter(hotel => {
    const matchesSearch = searchTerm === "" ||
      hotel.hotelName.toLowerCase().includes(searchTerm.toLowerCase());

    if (activeTab === 'all') return matchesSearch;
    if (activeTab === 'popular') return matchesSearch && hotel.isPopular;
    if (activeTab === 'recent') return matchesSearch && hotel.isRecent;

    return matchesSearch;
  });

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Available Hotels</h3>

        {/* Search Input */}
        <div className="relative" ref={dropdownRef}>
          <div className="flex items-center border border-gray-300 rounded-md bg-white overflow-hidden focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search hotels by name..."
              className="w-full py-2 px-3 border-none focus:outline-none text-sm"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              onFocus={() => setViewDropdown(true)}
            />
            {searchTerm && (
              <button
                type="button"
                className="p-2 text-gray-400 hover:text-gray-600"
                onClick={() => setSearchTerm("")}
              >
                ×
              </button>
            )}
            <button
              type="button"
              className="p-2 bg-blue-50 text-blue-600 hover:bg-blue-100"
              onClick={() => setViewDropdown(!viewDropdown)}
            >
              {viewDropdown ? "▲" : "▼"}
            </button>
          </div>

          {/* Dropdown - Fixed to show fully with proper positioning */}
          {viewDropdown && (
            <div className="fixed inset-0 z-50 flex items-start justify-center pt-20 bg-black bg-opacity-30" onClick={() => setViewDropdown(false)}>
              <div
                className="w-full max-w-2xl bg-white border border-gray-200 rounded-md shadow-xl max-h-[80vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex justify-between items-center p-3 border-b border-gray-200 bg-gray-50">
                  <h3 className="text-lg font-medium text-gray-900">Select Hotels</h3>
                  <button
                    className="text-gray-500 hover:text-gray-700"
                    onClick={() => setViewDropdown(false)}
                  >
                    ×
                  </button>
                </div>

                {/* Search within dropdown */}
                <div className="p-3 border-b border-gray-200">
                  <div className="flex items-center border border-gray-300 rounded-md bg-white overflow-hidden focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                    <input
                      type="text"
                      placeholder="Search hotels by name..."
                      className="w-full py-2 px-3 border-none focus:outline-none text-sm"
                      value={searchTerm}
                      onChange={(e) => handleSearch(e.target.value)}
                      autoFocus
                    />
                    {searchTerm && (
                      <button
                        type="button"
                        className="p-2 text-gray-400 hover:text-gray-600"
                        onClick={() => setSearchTerm("")}
                      >
                        ×
                      </button>
                    )}
                  </div>
                </div>

                {/* Tabs */}
                <div className="flex border-b border-gray-200">
                  <button
                    className={`flex-1 py-2 text-sm font-medium ${activeTab === 'all' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                    onClick={() => setActiveTab('all')}
                  >
                    All Hotels
                  </button>
                  <button
                    className={`flex-1 py-2 text-sm font-medium ${activeTab === 'popular' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                    onClick={() => setActiveTab('popular')}
                  >
                    Popular
                  </button>
                  <button
                    className={`flex-1 py-2 text-sm font-medium ${activeTab === 'recent' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                    onClick={() => setActiveTab('recent')}
                  >
                    Recent
                  </button>
                </div>

                {/* Hotel List */}
                <div className="overflow-y-auto" style={{ maxHeight: 'calc(80vh - 180px)' }}>
                  <div className="p-3">
                    {filteredHotels.length === 0 ? (
                      <div className="p-3 text-sm text-gray-500 text-center">
                        No hotels found matching your search
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {/* Quick action when searching */}
                        {searchTerm && filteredHotels.length > 0 && (
                          <div className="bg-blue-50 p-3 rounded-md border border-blue-100 mb-3">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-blue-800">
                                <strong>{filteredHotels.length}</strong> hotels found matching "<strong>{searchTerm}</strong>"
                              </span>
                              <button
                                className="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-1 px-3 rounded-md"
                                onClick={() => {
                                  // Add all filtered hotels
                                  const hotelsToAdd = filteredHotels.filter(hotel =>
                                    !selectedHotels.some(selected => selected.hotelId === hotel.hotelId)
                                  );
                                  if (hotelsToAdd.length > 0) {
                                    const newSelectedHotels = [...selectedHotels, ...hotelsToAdd];
                                    props.setDatas(newSelectedHotels);
                                    setSelectedHotels(newSelectedHotels);
                                    setSearchTerm("");
                                  }
                                }}
                              >
                                Add All Matching Hotels
                              </button>
                            </div>
                          </div>
                        )}

                        {Object.entries(groupedHotels).map(([location, hotels]) => {
                          const filteredLocationHotels = hotels.filter(hotel =>
                            hotel.hotelName.toLowerCase().includes(searchTerm.toLowerCase())
                          );

                          if (filteredLocationHotels.length === 0) return null;

                          return (
                            <div key={location} className="mb-4">
                              <div className="flex justify-between items-center mb-2 bg-gray-50 p-2 rounded-md">
                                <h4 className="text-sm font-medium text-gray-700">{location} ({filteredLocationHotels.length})</h4>
                                <button
                                  className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md"
                                  onClick={() => selectAllFromLocation(location)}
                                >
                                  Select All in {location}
                                </button>
                              </div>

                              <div className="space-y-1">
                                {filteredLocationHotels.map(hotel => (
                                  <div
                                    key={hotel.hotelId}
                                    className="flex items-center justify-between p-2 hover:bg-blue-50 rounded-md cursor-pointer border border-gray-100"
                                    onClick={() => addHotel(hotel)}
                                  >
                                    <div className="flex items-center">
                                      <div className="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center mr-2 text-xs font-medium">
                                        {hotel.hotelName.charAt(0)}
                                      </div>
                                      <div>
                                        <div className="text-sm font-medium">{hotel.hotelName}</div>
                                        <div className="text-xs text-gray-500">
                                          {hotel.roomCount || 0} rooms • {hotel.starRating || "N/A"} stars
                                        </div>
                                      </div>
                                    </div>
                                    <button
                                      className="bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium py-1 px-2 rounded"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        addHotel(hotel);
                                      }}
                                    >
                                      Add
                                    </button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>

                {/* Footer with actions */}
                <div className="p-3 border-t border-gray-200 bg-gray-50 flex justify-end">
                  <button
                    className="bg-gray-200 hover:bg-gray-300 text-gray-800 mr-2 py-2 px-4 rounded-md text-sm font-medium"
                    onClick={() => setViewDropdown(false)}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium"
                    onClick={() => setViewDropdown(false)}
                  >
                    Done
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Selected Hotels */}
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h4 className="text-sm font-medium text-gray-700">Selected Hotels ({selectedHotels.length})</h4>
          {selectedHotels.length > 0 && (
            <button
              className="text-xs text-red-600 hover:text-red-800"
              onClick={() => {
                props.setDatas([]);
                setSelectedHotels([]);
              }}
            >
              Clear All
            </button>
          )}
        </div>

        {selectedHotels.length === 0 ? (
          <div className="text-sm text-gray-500 text-center py-4 border border-dashed border-gray-300 rounded-md">
            No hotels selected. Use the search above to add hotels.
          </div>
        ) : (
          <div className="max-h-[300px] overflow-y-auto border border-gray-200 rounded-lg shadow-inner bg-gray-50 p-2">
            {selectedHotels.map(hotel => (
              <div
                key={hotel.hotelId}
                className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 mb-2 last:mb-0"
              >
                <div className="p-3 flex justify-between items-center">
                  <div className="flex items-center overflow-hidden">
                    <div className="w-7 h-7 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      {hotel.hotelName.charAt(0)}
                    </div>
                    <div className="overflow-hidden">
                      <div className="text-sm font-medium">{hotel.hotelName}</div>
                      {hotel.starRating && (
                        <div className="text-xs text-gray-500 mt-1">
                          {hotel.starRating} stars • {hotel.roomCount || 0} rooms
                        </div>
                      )}
                    </div>
                  </div>
                  <button
                    className="bg-red-500 hover:bg-red-600 transition-colors duration-200 w-7 h-7 rounded-full text-white font-bold flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-red-400 flex-shrink-0 ml-2"
                    onClick={() => removeHotel(hotel.hotelId)}
                    title="Remove hotel"
                  >
                    ×
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

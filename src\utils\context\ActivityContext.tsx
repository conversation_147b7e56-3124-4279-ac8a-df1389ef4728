/* eslint-disable @typescript-eslint/no-explicit-any */

import { ReactNode, createContext, useEffect, useState } from "react";
import { fetchApiData } from "../api-functions/fetchApiData";
import { PACKAGE_DESTINATION_URL } from "../urls/urls";
import { createActivity } from "../api-functions/createActivity";
import toast from "react-hot-toast";
import { fetchActivityById } from "../api-functions/getActivity";
import { editActivity } from "../api-functions/edit/editActivity";
import { useNavigate } from "react-router-dom";

const ActivityContext = createContext({});

export const ActivityProvider = ({ children }: { children: ReactNode }) => {
  const navigate = useNavigate();
  const [res,setRes] = useState<any>({})
  const [activityId,setActivityId] = useState("");
  const [destinationId, setDestinationId] = useState("");
  const [destinationName, setDestinationName] = useState("");
  const [allDestinations, setAllDestinations] = useState<any[]>([]);
  const [activityName, setActivityName] = useState("");
  const [description, setDescription] = useState("");
  const [allTags, setAllTags] = useState<string[]>([]);
  const [edit, setEdit] = useState(false);
  const [loading, setLoading] = useState(true);

  const [price, setPrice] = useState(0);
  const [duration, setDuration] = useState(1);

  const [maxParticipants, setMaxParticipants] = useState(4);
  const [minParticipants, setMinParticipants] = useState(1);
  const [ageRestriction, setAgeRestriction] = useState(2);

  const [dayType, setDayType] = useState("Quarter");
  const [level, setLevel] = useState("Easy");

  const [imageUrl, setImageUrl] = useState("");

  const [isPrivate, setPrivate] = useState("1");

  async function fetchAllData() {
    const url = window.location.pathname.split("/");

    const destinationResp = await fetchApiData(PACKAGE_DESTINATION_URL);
    setAllDestinations(destinationResp);
    console.log(url);
    if (url[1] === "activity" && (url[url?.length - 1] === "edit" || url[url?.length - 2] === "edit")) {
      setEdit(true);
      console.log('====================================');
      console.log("Activity Edit");
      console.log('====================================');
      fetchActivity(url[2]);
    } else {
      setLoading(false);
    }
  }
  useEffect(() => {
    fetchAllData();
  }, []);
  async function fetchActivity(id: string) {
    const resp = await fetchActivityById(id);
    console.log(resp)
    setRes(resp)
    setActivityId(resp.activityId);
    setActivityName(resp.name);
    setDescription(resp.description);
    setAllTags(resp.tags);
    setPrice(resp.price);
    setDuration(resp.duration);
    setMaxParticipants(resp.participantInfo.maxParticipants);
    setMinParticipants(resp.participantInfo.minParticipants);
    setAgeRestriction(resp.participantInfo.ageRestriction);
    setDayType(resp.dayType);
    setLevel(resp.level);
    setPrivate(resp.isPrivate ? "1" : "0");
    setImageUrl(resp.image);
    setDestinationId(resp.destinationId);
    setLoading(false);

  }
  useEffect(() => {
    if(edit){
    setDestinationName(allDestinations.find((dest:any) => dest.destinationId === res?.destinationId)?.destinationName)
    }
  },[res])
  async function handleCreateActivity(op:string) {
    setLoading(true)
    let isPri: boolean;
    if (isPrivate === "1") {
      isPri = true;
    } else {
      isPri = false;
    }
    const data = {
      name: activityName ?activityName.trim() :"",
      image: imageUrl,
      price: Number(price),
      destinationId: destinationId,
      isPrivate: isPri,
      dayType: dayType,
      description:description? description.trim():"",
      level: level,
      tags: allTags,
      participantInfo: {
        minParticipants: Number(minParticipants),
        maxParticipants: Number(maxParticipants),
        ageRestriction: Number(ageRestriction),
      },
      duration: Number(duration),
    };
    if (
      data.name &&
      data.price >= 0 &&
      data.destinationId &&
      data.tags.length > 0 &&
      data.participantInfo.minParticipants > 0 &&
      data.participantInfo.maxParticipants > 0 &&
      data.participantInfo.ageRestriction > 0 &&
      data.duration > 0 &&
      data.dayType &&
      data.image &&
      data.level &&
      data.description &&
      isPrivate
    ) {
      try {

        switch(op){
          case "create":{
            const resp = await createActivity(data);
            console.log(resp);
            resetValues()
            navigate('/activities')
            break;
          }
          case "update":{
            console.log("activityId",activityId)
            const resp = await editActivity(activityId,data);
            resetValues()
            navigate('/activities')

            console.log(resp);
            break;
          }
          case "delete":{
            console.log("delete")
            break;
          }
          case "clone":{
            if(res.name !== data.name){
              const resp = await createActivity(data);
              console.log(resp);
              resetValues()
              navigate('/activities')

            }else{
              toast.error("Activity Already Exists");
              setLoading(false)
            }
            break;
          }
        }
      } catch (error) {
        console.log(error)
        setLoading(false)
      }
    } else {
      setLoading(false)
      toast.error("Please fill all the fields"); 
    }
  }

  const resetValues = () => {
    setRes({});
    setActivityId("");
    setDestinationId("");
    setDestinationName("");
    setAllDestinations([]);
    setActivityName("");
    setDescription("");
    setAllTags([]);
    setEdit(false);
    setLoading(true);
    setPrice(0);
    setDuration(1);
    setMaxParticipants(4);
    setMinParticipants(1);
    setAgeRestriction(2);
    setDayType("Quarter");
    setLevel("Easy");
    setImageUrl("");
    setPrivate("1");
  };
  return (
    <ActivityContext.Provider
      value={{
        edit,loading,
        destinationId,
        setDestinationId,
        destinationName,
        setDestinationName,
        allDestinations,
        setAllDestinations,
        activityName,
        setActivityName,
        allTags,
        setAllTags,
        isPrivate,
        setPrivate,
        handleCreateActivity,
        price,
        setPrice,
        duration,
        setDuration,
        dayType,
        setDayType,
        level,
        setLevel,
        imageUrl,
        setImageUrl,
        maxParticipants,
        setMaxParticipants,
        minParticipants,
        setMinParticipants,
        ageRestriction,
        setAgeRestriction,
        description,
        setDescription,
      }}
    >
      {children}
    </ActivityContext.Provider>
  );
};
export default ActivityContext;
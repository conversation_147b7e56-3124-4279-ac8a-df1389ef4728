/* eslint-disable @typescript-eslint/no-explicit-any */
import PackageContext, { DayActivityType } from "@/utils/context/PackageContext"
import { ChangeEvent, useContext, useEffect} from "react"
import { ActivityEvent } from "../package-activity/ActivityDay"
import { input_field_css, label_css } from "../PackageForm"

export default function PackagePrices() {
//   "activityPrice": 1000,
//   "additionalFees": 100,
//   "marketingPer": 1000,
//   "transPer": 2500,
//   "agentCommissionPer": 500,
//   "gstPer": 15
const {activityPrice,setActivityPrice,offer,setoffer,marketingPer,setMarketingPer,transPer,setTransPer,agentCommission,setAgentCommission,gstPer,setGstPer,additionalPrice,setAdditionalPrice,dayActivity}:any = useContext(PackageContext)
    useEffect(()=>{
       const aPrice =  dayActivity?.reduce((k:number,day:DayActivityType)=>k+day.event.reduce((k,l:ActivityEvent)=>k +( l.price||0),0),0)
       setActivityPrice(aPrice)
    },[dayActivity])
  return (
    <div className="flex border-2">
      <div className="w-1/2 m-1">
        <div className="w-full">
         <label  htmlFor="activity" className={label_css}>Activity Price</label>
        <input id="activity" type="number" value={activityPrice} disabled className={input_field_css} placeholder="Activity Price"/>
        </div>

        <div className="w-full">
         <label htmlFor="additional" className={label_css}>Additional Price</label>
        <input id="additional" type="number" value={additionalPrice} onInput={(e:ChangeEvent<HTMLInputElement>)=>setAdditionalPrice(e.target.valueAsNumber)} className={input_field_css} placeholder="Additional Price"/>
        </div>

        <div className="w-full">
         <label htmlFor="mar" className={label_css}>Marketing per.</label>
        <input id="mar" type="number" value={marketingPer} onInput={(e:ChangeEvent<HTMLInputElement>)=>setMarketingPer(e.target.valueAsNumber)} className={input_field_css} placeholder="Marketting per."/>
        </div>
      </div>
      <div className="w-1/2 m-1">
      <div className="w-full">
         <label htmlFor="trans" className={label_css}>Trans Per.</label>
        <input id="trans" type="number" value={transPer} onInput={(e:ChangeEvent<HTMLInputElement>)=>setTransPer(e.target.valueAsNumber)} className={input_field_css} placeholder="Trans Per."/>
        </div>
        <div className="w-full">
         <label htmlFor="agent" className={label_css}>Agent Commission Per.</label>
        <input id="agent" type="number" value={agentCommission} onInput={(e:ChangeEvent<HTMLInputElement>)=>setAgentCommission(e.target.valueAsNumber)} className={input_field_css} placeholder="Agent Commission Per."/>
        </div>
        <div className="w-full">
         <label htmlFor="gstPer" className={label_css}>GST per.</label>
        <input id="gstPer" type="number" value={gstPer} onInput={(e:ChangeEvent<HTMLInputElement>)=>setGstPer(e.target.valueAsNumber)} className={input_field_css} placeholder="GST per."/>
        </div>

        <div className="w-full">
         <label htmlFor="offer" className={label_css}>Offer per.</label>
        <input id="offer" type="number" value={offer} onInput={(e:ChangeEvent<HTMLInputElement>)=>setoffer(e.target.valueAsNumber)} className={input_field_css} placeholder="Offer per."/>
        </div>
      </div>
    </div>
  )
}

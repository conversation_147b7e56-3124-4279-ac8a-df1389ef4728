import React, { useState, useEffect } from 'react';
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import "@/styles/calendar-styles.css";
import { Calendar } from 'react-date-range';
import { addMonths, format } from 'date-fns';
import { fetchAllRooms } from "@/utils/api-functions/fetch-rooms";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, DollarSign, Info, AlertTriangle, Tag, ChevronDown } from 'lucide-react';
import { HotelRoom } from '@/types/types';
import { useQuery, useQueryClient } from 'react-query';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { getAllHolidays } from "@/utils/util-functions/indianHolidays";
import EditMp from './EditMp';
import AddMealPlan from '@/components/page-components/hotel-details/room/mealPlan/AddMealPlan';

interface DailyPriceCalendarProps {
  hotelId: string;
  initialDate?: string;
  initialRoomId?: string;
  initialMealPlanType?: string;
}

interface MealPlanOption {
  hotelMealId: string;
  mealPlan: string;
  startDate: string[];
  endDate: string[];
  roomPrice: number;
  seasonType: string;
}

interface PriceData {
  [date: string]: {
    price: number;
    seasonType: string;
    isConflict?: boolean;
  };
}

// Group meal plans of the same type
interface GroupedMealPlan {
  type: string; // Meal plan type (EP, CP, MAP, AP)
  plans: MealPlanOption[]; // All meal plans of this type
}

const DailyPriceCalendar = ({ hotelId, initialDate, initialRoomId, initialMealPlanType }: DailyPriceCalendarProps) => {
  const [date, setDate] = useState<Date>(initialDate ? new Date(initialDate) : new Date());
  const [selectedRoom, setSelectedRoom] = useState<string>(initialRoomId || '');
  const [selectedMealPlanType, setSelectedMealPlanType] = useState<string>(initialMealPlanType || '');
  const [priceData, setPriceData] = useState<PriceData>({});
  const [groupedMealPlans, setGroupedMealPlans] = useState<GroupedMealPlan[]>([]);
  const [hasConflicts, setHasConflicts] = useState<boolean>(false);
  const [conflictSummary, setConflictSummary] = useState<{from:string;to:string;prices:number[]} | null>(null);
  const [editMpOpen, setEditMpOpen] = useState(false);
  const [editMpData, setEditMpData] = useState<any>(null);
  const [addMealPlanOpen, setAddMealPlanOpen] = useState(false);
  
  const queryClient = useQueryClient();
  
  // Fetch rooms data using react-query
  const { data: rooms, isLoading: roomsLoading, error: roomsError } = useQuery<HotelRoom[]>(
    ['rooms', hotelId],
    () => fetchAllRooms(hotelId),
    {
      enabled: !!hotelId,
      onSuccess: (data) => {
        if (data && data.length > 0) {
          if (initialRoomId && data.some(r => r.hotelRoomId === initialRoomId)) {
            setSelectedRoom(initialRoomId);
          } else {
            setSelectedRoom(data[0].hotelRoomId);
          }
        }
      }
    }
  );

  // Group meal plans by type when room changes
  useEffect(() => {
    if (selectedRoom && rooms) {
      const room = rooms.find(r => r.hotelRoomId === selectedRoom);
      
      if (room && room.mealPlan && room.mealPlan.length > 0) {
        // Group meal plans by their type
        const mealPlanGroups: {[key: string]: MealPlanOption[]} = {};
        
        room.mealPlan.forEach(mp => {
          const planType = mp.mealPlan.toUpperCase();
          if (!mealPlanGroups[planType]) {
            mealPlanGroups[planType] = [];
          }
          mealPlanGroups[planType].push(mp);
        });
        
        // Convert to array format for dropdown
        const groupedPlans = Object.entries(mealPlanGroups).map(([type, plans]) => ({
          type,
          plans
        }));
        
        setGroupedMealPlans(groupedPlans);
        
        // Set first meal plan type as default if available
        if (groupedPlans.length > 0) {
          if (initialMealPlanType && groupedPlans.some(g => g.type === initialMealPlanType)) {
            setSelectedMealPlanType(initialMealPlanType);
          } else {
            setSelectedMealPlanType(groupedPlans[0].type);
          }
        } else {
          setSelectedMealPlanType('');
          setPriceData({});
        }
      } else {
        setGroupedMealPlans([]);
        setSelectedMealPlanType('');
        setPriceData({});
      }
    }
  }, [selectedRoom, rooms, initialMealPlanType]);

  // Process meal plan data to get daily prices and detect conflicts
  useEffect(() => {
    if (!selectedRoom || !selectedMealPlanType || !rooms || groupedMealPlans.length === 0) {
      setPriceData({});
      setHasConflicts(false);
      setConflictSummary(null);
      return;
    }
    
    const room = rooms.find(r => r.hotelRoomId === selectedRoom);
    if (!room || !room.mealPlan) {
      setPriceData({});
      setHasConflicts(false);
      setConflictSummary(null);
      return;
    }
    
    // Find the group of meal plans with the selected type
    const selectedGroup = groupedMealPlans.find(g => g.type === selectedMealPlanType);
    if (!selectedGroup || selectedGroup.plans.length === 0) {
      setPriceData({});
      setHasConflicts(false);
      setConflictSummary(null);
      return;
    }
    
    console.log("Processing meal plans for:", selectedMealPlanType);
    console.log("Plans to process:", selectedGroup.plans);
    
    // Create a price map for each day in all date ranges of the selected meal plan type
    const prices: PriceData = {};
    let hasAnyConflict = false;
    
    // First pass: collect all prices by date
    const datesPrices: {[date: string]: number[]} = {};
    
    selectedGroup.plans.forEach(plan => {
      // Ensure we're only processing valid date ranges
      if (!plan.startDate || !plan.endDate || plan.startDate.length === 0 || plan.endDate.length === 0) {
        console.warn("Invalid date range found for plan:", plan);
        return;
      }
      
      for (let i = 0; i < plan.startDate.length; i++) {
        if (!plan.startDate[i] || !plan.endDate[i]) {
          console.warn(`Invalid date at index ${i} for plan:`, plan);
          continue;
        }
        
        const start = new Date(plan.startDate[i]);
        const end = new Date(plan.endDate[i]);
        
        // Skip invalid date ranges
        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
          console.warn("Invalid date conversion for range:", plan.startDate[i], plan.endDate[i]);
          continue;
        }
        
        console.log(`Processing date range: ${format(start, 'yyyy-MM-dd')} to ${format(end, 'yyyy-MM-dd')} - Price: ${plan.roomPrice}`);
        
        // For each day in the range, add the price
        const currentDay = new Date(start);
        while (currentDay <= end) {
          const dateStr = format(currentDay, 'yyyy-MM-dd');
          
          if (!datesPrices[dateStr]) {
            datesPrices[dateStr] = [];
          }
          datesPrices[dateStr].push(plan.roomPrice);
          
          // Move to next day
          currentDay.setDate(currentDay.getDate() + 1);
        }
      }
    });
    
    console.log("Processed date prices:", datesPrices);
    
    // Compute summary of conflict period
    if (Object.values(datesPrices).some(prices => new Set(prices).size > 1)) {
      const sorted = Object.keys(datesPrices).sort();
      const from = sorted[0];
      const to = sorted[sorted.length - 1];
      const pricesUnion = Array.from(new Set(Object.values(datesPrices).flatMap(prices => prices)));
      setConflictSummary({ from, to, prices: pricesUnion });
    } else {
      setConflictSummary(null);
    }
    
    // Second pass: determine prices and conflicts
    for (const [dateStr, pricesForDate] of Object.entries(datesPrices)) {
      const hasConflict = new Set(pricesForDate).size > 1;
      
      if (hasConflict) {
        hasAnyConflict = true;
      }
      
      // Use the most recent plan that includes this date
      // Going in reverse to prioritize more recent plans
      let priceToUse = pricesForDate[0]; // Default to first price
      let seasonTypeToUse = "Regular Season"; // Default season type
      
      // Find the most recent plan for this date
      for (let i = selectedGroup.plans.length - 1; i >= 0; i--) {
        const plan = selectedGroup.plans[i];
        
        for (let j = 0; j < plan.startDate.length; j++) {
          if (!plan.startDate[j] || !plan.endDate[j]) continue;
          
          const start = new Date(plan.startDate[j]);
          const end = new Date(plan.endDate[j]);
          const current = new Date(dateStr);
          
          if (current >= start && current <= end) {
            priceToUse = plan.roomPrice;
            seasonTypeToUse = plan.seasonType;
            break;
          }
        }
      }
      
      prices[dateStr] = {
        price: priceToUse,
        seasonType: seasonTypeToUse,
        isConflict: hasConflict
      };
    }
    
    console.log("Final price data:", prices);
    
    setPriceData(prices);
    setHasConflicts(hasAnyConflict);
  }, [selectedMealPlanType, selectedRoom, rooms, groupedMealPlans, initialMealPlanType]);

  // Get meal plan full name
  const getMealPlanName = (type: string) => {
    switch(type.toUpperCase()) {
      case "EP": return "European Plan (Room Only)";
      case "CP": return "Continental Plan (Breakfast)";
      case "MAP": return "Modified American Plan (Breakfast & Dinner)";
      case "AP": return "American Plan (All Meals)";
      default: return type;
    }
  };

  // Navigate to previous month
  const handlePrevMonth = () => {
    setDate(addMonths(date, -1));
  };

  // Navigate to next month
  const handleNextMonth = () => {
    setDate(addMonths(date, 1));
  };

  // Helper to get meal plan data for a given date, grouped by price
  const getMealPlanForDate = (date: Date) => {
    if (!rooms || !selectedRoom || !selectedMealPlanType) return null;
    const room = rooms.find(r => r.hotelRoomId === selectedRoom);
    if (!room || !room.mealPlan) return null;
    // Find all meal plans of this type
    const mealPlans = room.mealPlan.filter(mp => mp.mealPlan.toUpperCase() === selectedMealPlanType);
    // Find all date ranges (across all plans) that include this date
    let matchedPlan: any = null;
    for (const mp of mealPlans) {
      for (let i = 0; i < mp.startDate.length; i++) {
        const start = new Date(mp.startDate[i]);
        const end = new Date(mp.endDate[i]);
        if (date >= start && date <= end) {
          // Group all ranges with the same price as this one
          const samePriceRanges = [];
          for (let j = 0; j < mp.startDate.length; j++) {
            if (mp.roomPrice === mp.roomPrice) {
              samePriceRanges.push({ startDate: mp.startDate[j], endDate: mp.endDate[j] });
            }
          }
          matchedPlan = {
            ...mp,
            startDate: samePriceRanges.map(r => r.startDate),
            endDate: samePriceRanges.map(r => r.endDate)
          };
          break;
        }
      }
      if (matchedPlan) break;
    }
    // If not found, return the first meal plan of this type (for new entry)
    return matchedPlan || mealPlans[0] || null;
  };

  // Helper to get a new meal plan object for a new date range
  const getNewMealPlanForDate = (date: Date) => {
    if (!rooms || !selectedRoom || !selectedMealPlanType) return null;
    const room = rooms.find(r => r.hotelRoomId === selectedRoom);
    if (!room) return null;
    // Use the first meal plan of this type as a template, but with a new date range
    const template = room.mealPlan.find(mp => mp.mealPlan.toUpperCase() === selectedMealPlanType);
    if (!template) return null;
    return {
      ...template,
      startDate: [date.toISOString().split('T')[0]],
      endDate: [date.toISOString().split('T')[0]],
      roomPrice: '',
      adultPrice: '',
      childPrice: ''
    };
  };

  // Custom day content renderer to show prices and holidays
  const dayContentRenderer = (day: Date) => {
    const dateStr = format(day, 'yyyy-MM-dd');
    const dayPrice = priceData[dateStr];
    
    // Check for holidays
    const holidays = getAllHolidays(day);
    const hasHoliday = holidays.length > 0;
    const holidayNames = holidays.map(h => h.name).join(', ');
    const isPublicHoliday = holidays.some(h => h.type === 'public');
    
    // Color coding based on season type
    let seasonColor = 'bg-gray-50';
    if (dayPrice) {
      switch(dayPrice.seasonType.toLowerCase()) {
        case 'peak season':
          seasonColor = 'bg-red-50';
          break;
        case 'mid season':
          seasonColor = 'bg-blue-50';
          break;
        case 'low season':
          seasonColor = 'bg-green-50';
          break;
        default:
          seasonColor = 'bg-gray-50';
      }
    }
    
    // Calculate if the date is in the current month
    const isCurrentMonth = day.getMonth() === date.getMonth();
    
    // Add click handler to open EditMp modal
    const handleDayClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      let mpData;
      if (dayPrice) {
        mpData = getMealPlanForDate(day);
      } else {
        mpData = getNewMealPlanForDate(day);
      }
      setEditMpData(mpData);
      setEditMpOpen(true);
    };

    return (
      <div
        className={`h-full w-full flex flex-col justify-between p-1.5 relative ${seasonColor} ${dayPrice?.isConflict ? 'border-2 border-amber-400' : ''} ${hasHoliday && isPublicHoliday ? 'border-2 border-purple-400' : ''} ${!isCurrentMonth ? 'opacity-50' : ''}`}
        title={dayPrice?.isConflict ? 'Price Conflict' : (hasHoliday ? holidayNames : undefined)}
        onClick={handleDayClick}
        style={{ cursor: 'pointer' }}
      >
        <div className="flex justify-between items-start">
          <span className={`text-xs font-medium ${!isCurrentMonth ? 'text-gray-400' : isPublicHoliday ? 'text-purple-700 bg-purple-100 rounded px-1' : hasHoliday ? 'bg-purple-50 text-purple-700 rounded px-1' : 'text-gray-700'}`}
            title={hasHoliday ? holidayNames : undefined}
            style={hasHoliday ? { boxShadow: '0 1px 4px 0 rgba(168,139,250,0.08)' } : {}}>
            {format(day, 'd')}
          </span>
          {hasHoliday && (
            <div className={`h-2 w-2 rounded-full ${isPublicHoliday ? 'bg-purple-500' : 'bg-purple-300'}`} 
                 title={holidayNames}></div>
          )}
        </div>
        {/* Always show holiday name under the date number if present, even if price is set */}
        {hasHoliday && (
          <div className="text-[10px] text-purple-700 text-center truncate font-bold mb-0.5" title={holidayNames} style={{lineHeight:'1.1'}}>
            {holidays[0].name}
          </div>
        )}
        {dayPrice && (
          <div className="mt-auto mb-0.5 text-sm font-semibold text-green-800 flex items-center justify-center bg-white bg-opacity-75 rounded px-1 py-0.5 shadow-sm z-10">
            <span>₹{dayPrice.price}</span>
            {dayPrice.isConflict && (
              <div className="ml-1 text-amber-600" title="Price Conflict">
                <AlertTriangle className="h-3 w-3" />
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Pass a callback to EditMp to close modal and refresh data
  const handleEditMpClose = (shouldRefresh = false) => {
    setEditMpOpen(false);
    if (shouldRefresh) {
      queryClient.invalidateQueries(['rooms', hotelId]);
      // Keep the selectedMealPlanType unchanged
    }
  };

  if (roomsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading...</span>
      </div>
    );
  }

  if (roomsError) {
    return (
      <div className="bg-red-50 p-4 rounded-md">
        <p className="text-red-600">Error loading rooms data. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-blue-50 to-white rounded-2xl shadow-lg p-8 mb-10 border border-blue-100">
      <h2 className="text-2xl font-bold text-blue-900 mb-7 flex items-center gap-2 border-b-2 border-blue-100 pb-4 tracking-tight">
        <CalendarIcon className="h-6 w-6 text-blue-500" />
        <span>Daily Price Calendar</span>
      </h2>
      
      {(!rooms || rooms.length === 0) ? (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-5 my-6 flex items-start gap-3 shadow-sm">
          <Info className="text-yellow-500 mt-1 h-6 w-6 flex-shrink-0" />
          <div>
            <h3 className="text-base font-semibold text-yellow-900">No rooms available</h3>
            <p className="text-sm text-yellow-800 mt-1">
              Please add rooms and meal plans to view the price calendar.
            </p>
          </div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Room Type Dropdown */}
            <div className="form-group">
              <label className="block text-base font-semibold text-blue-900 mb-2 tracking-tight">
                Room Type
              </label>
              <div className="relative">
                <select
                  value={selectedRoom}
                  onChange={(e) => setSelectedRoom(e.target.value)}
                  className="w-full p-3 pr-10 border border-blue-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-white shadow transition-all text-base appearance-none font-medium text-blue-900 hover:border-blue-300"
                >
                  {rooms.map(room => (
                    <option key={room.hotelRoomId} value={room.hotelRoomId} className="text-blue-900">
                      {room.hotelRoomType}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2.5 pointer-events-none">
                  <ChevronDown className="h-5 w-5 text-blue-400" />
                </div>
              </div>
            </div>
            
            {/* Meal Plan Dropdown - Now grouped by type */}
            <div className="form-group">
              <label className="block text-base font-semibold text-blue-900 mb-2 tracking-tight">
                Meal Plan
              </label>
              <div className="relative">
                <select
                  value={selectedMealPlanType}
                  onChange={(e) => setSelectedMealPlanType(e.target.value)}
                  className={cn(
                    "w-full p-3 pr-10 border border-blue-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-white shadow transition-all text-base appearance-none font-medium text-blue-900 hover:border-blue-300",
                    (!selectedRoom || groupedMealPlans.length === 0) && "bg-gray-50 cursor-not-allowed text-gray-400"
                  )}
                  disabled={!selectedRoom || groupedMealPlans.length === 0}
                >
                  {groupedMealPlans.length === 0 ? (
                    <option value="">No meal plans available</option>
                  ) : (
                    groupedMealPlans.map(group => (
                      <option key={group.type} value={group.type} className="text-blue-900">
                        {getMealPlanName(group.type)}
                      </option>
                    ))
                  )}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2.5 pointer-events-none">
                  <ChevronDown className="h-5 w-5 text-blue-400" />
                </div>
              </div>
            </div>
          </div>
          
          {/* Conflicts Warning */}
          {hasConflicts && (
            <div className="bg-amber-50 border border-amber-200 rounded-xl p-5 mb-7 flex items-start gap-3 shadow-sm">
              <AlertTriangle className="text-amber-500 mt-1 h-6 w-6 flex-shrink-0" />
              <div>
                <h3 className="text-base font-semibold text-amber-900">Price conflicts detected</h3>
                <p className="text-sm text-amber-800 mt-1">
                  Conflicts from <strong>{conflictSummary ? new Date(conflictSummary.from).toLocaleDateString() : ''}</strong> to <strong>{conflictSummary ? new Date(conflictSummary.to).toLocaleDateString() : ''}</strong>.<br/>
                  Prices: <strong>{conflictSummary ? conflictSummary.prices.join(' / ') : ''}</strong>
                </p>
              </div>
            </div>
          )}
          
          {/* Calendar Container */}
          <div className="bg-white rounded-2xl border border-blue-100 shadow-md overflow-hidden mb-7">
            {/* Calendar Controls */}
            <div className="flex justify-between items-center p-5 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-white">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevMonth}
                className="h-9 px-3 border-blue-200 hover:bg-blue-50 focus:ring-2 focus:ring-blue-400 rounded-lg"
              >
                <ChevronLeft className="h-5 w-5 text-blue-500" />
              </Button>
              <h3 className="text-lg font-bold text-blue-900 tracking-tight">
                {format(date, 'MMMM yyyy')}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextMonth}
                className="h-9 px-3 border-blue-200 hover:bg-blue-50 focus:ring-2 focus:ring-blue-400 rounded-lg"
              >
                <ChevronRight className="h-5 w-5 text-blue-500" />
              </Button>
            </div>
            
            {/* Legend - Redesigned as inline badges in the header */}
            <div className="px-5 py-3 flex flex-wrap gap-3 bg-blue-50 border-b border-blue-100">
              <div className="flex items-center px-3 py-1 bg-red-50 rounded-full border border-red-200 gap-2">
                <div className="w-3 h-3 bg-red-200 rounded-full"></div>
                <span className="text-xs font-semibold text-red-700">Peak</span>
              </div>
              <div className="flex items-center px-3 py-1 bg-blue-100 rounded-full border border-blue-200 gap-2">
                <div className="w-3 h-3 bg-blue-200 rounded-full"></div>
                <span className="text-xs font-semibold text-blue-700">Mid</span>
              </div>
              <div className="flex items-center px-3 py-1 bg-green-100 rounded-full border border-green-200 gap-2">
                <div className="w-3 h-3 bg-green-200 rounded-full"></div>
                <span className="text-xs font-semibold text-green-700">Low</span>
              </div>
              <div className="flex items-center px-3 py-1 bg-white rounded-full border-2 border-amber-300 gap-2">
                <span className="text-xs font-semibold text-amber-700">Conflict</span>
              </div>
              <div className="flex items-center px-3 py-1 bg-white rounded-full border-2 border-purple-300 gap-2">
                <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                <span className="text-xs font-semibold text-purple-700">Holiday</span>
              </div>
              <div className="flex items-center px-3 py-1 bg-white rounded-full border border-blue-200 gap-2 ml-auto">
                <DollarSign className="w-4 h-4 text-green-600" />
                <span className="text-xs font-semibold text-blue-900">Daily Rate</span>
              </div>
            </div>
            
            {/* Price Calendar */}
            <div className="flex justify-center items-center w-full bg-white">
              <div className="w-full max-w-3xl">
                <Calendar
                  date={date}
                  onChange={setDate}
                  dayContentRenderer={dayContentRenderer}
                  color="#3d91ff"
                  showMonthAndYearPickers={true}
                  showDateDisplay={false}
                  showPreview={false}
                  className="custom-calendar calendar-large"
                />
              </div>
            </div>
          </div>
          
          {/* Message when no data is available */}
          {selectedRoom && selectedMealPlanType && Object.keys(priceData).length === 0 && (
            <div className="text-center py-8 bg-blue-50 rounded-xl border border-blue-100">
              <Info className="h-7 w-7 text-blue-300 mx-auto mb-2" />
              <p className="text-blue-500 text-base">No pricing data available for the selected room and meal plan.</p>
            </div>
          )}
          
          {/* Meal Plan Details */}
          {selectedMealPlanType && groupedMealPlans.length > 0 && (
            <div className="mt-10 pt-6 border-t border-blue-100">
              <h3 className="text-lg font-bold text-blue-900 mb-5 flex items-center gap-2">
                <Tag className="h-5 w-5 text-blue-500" />
                <span>Price Ranges for {getMealPlanName(selectedMealPlanType)}</span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5 max-h-[250px] overflow-y-auto pr-1 custom-scrollbar">
                {groupedMealPlans.find(g => g.type === selectedMealPlanType)?.plans.map((plan) => (
                  plan.startDate.map((start, i) => (
                    <div
                      key={`${plan.hotelMealId}-${i}`}
                      className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-xl border border-blue-100 shadow hover:border-blue-300 transition-colors cursor-pointer"
                      onClick={() => setDate(new Date(start))}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex flex-col">
                          <span className="text-xs font-semibold text-blue-400 mb-1">Date Range</span>
                          <span className="text-sm text-blue-900 font-semibold">
                            {new Date(start).toLocaleDateString()} — {new Date(plan.endDate[i]).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex flex-col items-end">
                          <span className="text-xs font-semibold text-blue-400 mb-1">Price</span>
                          <div className="flex items-center">
                            <span className="text-base font-bold text-green-600 mr-2">₹{plan.roomPrice}</span>
                            <span className="text-xs capitalize px-2 py-0.5 rounded-full bg-blue-100 text-blue-700 whitespace-nowrap font-semibold">
                              {plan.seasonType.toLowerCase()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ))}
              </div>
            </div>
          )}
          
          {/* Render EditMp modal if open */}
          {editMpOpen && editMpData && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
              <div className="bg-white rounded-2xl shadow-2xl p-0 max-w-2xl w-full relative animate-fade-in max-h-[90vh] overflow-y-auto sm:p-6 p-2">
                <button
                  className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 text-2xl font-bold z-10"
                  onClick={() => setEditMpOpen(false)}
                  aria-label="Close"
                >
                  ×
                </button>
                <EditMp mp={editMpData} setEdit={setEditMpOpen} onClose={handleEditMpClose} />
              </div>
            </div>
          )}
          
          {/* Render AddMealPlan modal if open */}
          {addMealPlanOpen && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
              <div className="bg-white rounded-2xl shadow-2xl p-0 max-w-2xl w-full relative animate-fade-in max-h-[90vh] overflow-y-auto sm:p-6 p-2">
                <button
                  className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 text-2xl font-bold z-10"
                  onClick={() => setAddMealPlanOpen(false)}
                  aria-label="Close"
                >
                  ×
                </button>
                <AddMealPlan />
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default DailyPriceCalendar; 
import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from 'react-query';
import { TariffUpload, HotelRoom, TariffPriceData } from '@/types/types';
import { Button } from '@/components/ui/button';
import {
  AlertCircle,
  Check,
  FileText,
  Info,
  X,
  FileUp,
  RefreshCw,
  Calendar,
  Clock,
  Trash2,
  Sparkles,
  Wand2
} from 'lucide-react';
import { format } from 'date-fns';
import {
  fetchHotelTariffs,
  createTariffUpload,
  updateTariffStatus,
  deleteTariffUpload,
  extractTariffData,
  extractTariffDataEnhanced,
  saveMappedTariffData
} from '@/utils/api-functions/tariff-upload';
import toast from 'react-hot-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import TariffComparison from './TariffComparison';
import { Progress } from '@/components/ui/progress';
import { TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { MappingWorkflow } from './tariff-mapping';

// Custom Alert Components
type AlertVariant = 'default' | 'destructive' | 'info' | 'success' | 'warning';

interface AlertProps {
  children: React.ReactNode;
  className?: string;
  variant?: AlertVariant;
  icon?: React.ReactNode;
}

interface AlertTitleProps {
  children: React.ReactNode;
  className?: string;
}

interface AlertDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

const Alert: React.FC<AlertProps> = ({ children, className = "", variant = "default" }) => {
  const baseClasses = "relative w-full rounded-lg border p-4 mb-4";
  const variantClasses: Record<AlertVariant, string> = {
    default: "bg-white border-gray-200",
    destructive: "bg-red-50 border-red-200 text-red-700",
    info: "bg-blue-50 border-blue-200 text-blue-700",
    success: "bg-green-50 border-green-200 text-green-700",
    warning: "bg-amber-50 border-amber-200 text-amber-700"
  };

  return (
    <div
      role="alert"
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {children}
    </div>
  );
};

const AlertTitle: React.FC<AlertTitleProps> = ({ children, className = "" }) => (
  <h5 className={`mb-1 font-medium ${className}`}>{children}</h5>
);

const AlertDescription: React.FC<AlertDescriptionProps> = ({ children, className = "" }) => (
  <div className={`text-sm ${className}`}>{children}</div>
);

// Status Badge component
const StatusBadge = ({ status }: { status: 'pending' | 'approved' | 'rejected' }) => {
  const variants = {
    pending: "bg-yellow-100 text-yellow-800",
    approved: "bg-green-100 text-green-800",
    rejected: "bg-red-100 text-red-800"
  };

  const labels = {
    pending: "Pending",
    approved: "Approved",
    rejected: "Rejected"
  };

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${variants[status]}`}>
      {labels[status]}
    </span>
  );
};

interface TariffUploadListProps {
  hotelId: string;
  rooms: HotelRoom[];
}

const TariffUploadList: React.FC<TariffUploadListProps> = ({ hotelId, rooms }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedRoom, setSelectedRoom] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [extractionStage, setExtractionStage] = useState<string | null>(null);
  const [tariffDetailsOpen, setTariffDetailsOpen] = useState(false);
  const [selectedTariff, setSelectedTariff] = useState<TariffUpload | null>(null);
  const [comparisonOpen, setComparisonOpen] = useState(false);
  const [tariffForComparison, setTariffForComparison] = useState<TariffUpload | null>(null);
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [useEnhancedExtraction, setUseEnhancedExtraction] = useState(true);
  const [mappingWorkflowOpen, setMappingWorkflowOpen] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);

  const queryClient = useQueryClient();

  // Fetch tariff uploads with better error handling
  const {
    data: tariffs = [],
    isError
  } = useQuery<TariffUpload[]>(
    ['tariffs', hotelId],
    () => fetchHotelTariffs(hotelId),
    {
      enabled: !!hotelId,
      retry: 1, // Only retry once to avoid excessive error logs
      onError: (err: any) => {
        console.error('Failed to fetch tariffs:', err);
      }
    }
  );

  // Filtered tariffs based on status
  const filteredTariffs = tariffs.filter(tariff =>
    statusFilter === 'all' || tariff.status === statusFilter
  );

  // Set the first room as selected by default when rooms are loaded
  useEffect(() => {
    if (rooms?.length > 0 && !selectedRoom) {
      setSelectedRoom(rooms[0].hotelRoomId);
    }
  }, [rooms, selectedRoom]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Check if file is a PDF
      if (file.type !== 'application/pdf') {
        toast.error('Only PDF files are supported');
        return;
      }

      setSelectedFile(file);
    }
  };

  const simulateProgress = () => {
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 95) {
          clearInterval(interval);
          return prev;
        }
        return prev + 5;
      });
    }, 200);

    return () => clearInterval(interval);
  };

  const updateExtractionStage = (stage: string) => {
    setExtractionStage(stage);
    // Add a slight delay to make the stages visible to the user
    return new Promise(resolve => setTimeout(resolve, 800));
  };

  const handleTariffUpload = async () => {
    if (!selectedFile || !selectedRoom) {
      toast.error('Please select a room and tariff file');
      return;
    }

    setIsUploading(true);
    const stopProgressSimulation = simulateProgress();

    try {
      // Update extraction stage for better UX
      await updateExtractionStage('Preparing upload');

      // Log hotel_id and room_id at upload time
      console.log('[CONTEXT_TRACE] PDF Upload - Start', {
        hotel_id: hotelId,
        room_id: selectedRoom,
        file_name: selectedFile.name,
        enhanced_mode: useEnhancedExtraction
      });

      if (useEnhancedExtraction) {
        // Enhanced extraction mode
        await updateExtractionStage('Extracting data with enhanced AI');
        setUploadProgress(30);

        // Get the room name for better display
        const selectedRoomObj = rooms.find(room => room.hotelRoomId === selectedRoom);
        const roomName = selectedRoomObj?.hotelRoomType || 'Unknown Room';

        // Extract data directly from the file
        console.log('[TARIFF_MAPPING] Extracting data from file:', {
          file_name: selectedFile.name,
          file_size: selectedFile.size,
          hotel_id: hotelId,
          room_id: selectedRoom,
          room_name: roomName
        });

        const extractionResult = await extractTariffDataEnhanced(
          selectedFile,
          hotelId,
          selectedRoom
        );

        setUploadProgress(90);

        // Log the extracted data
        console.log('[TARIFF_MAPPING] Extraction result:', {
          room_types: extractionResult.extractedData?.roomTypes?.length || 0,
          date_ranges: extractionResult.extractedData?.dateRanges?.length || 0,
          prices: extractionResult.extractedData?.prices?.length || 0,
          extra_charges: extractionResult.extractedData?.extraCharges?.length || 0,
          file_path: extractionResult.filePath
        });

        // Set extracted data and open mapping workflow
        setExtractedData({
          ...extractionResult.extractedData,
          filePath: extractionResult.filePath,
          originalFile: selectedFile
        });

        // Reset form
        setSelectedFile(null);
        const uploadInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        if (uploadInput) uploadInput.value = '';

        await updateExtractionStage('Finalizing');
        setUploadProgress(100);

        // Open mapping workflow
        setMappingWorkflowOpen(true);

        toast.success('PDF extracted successfully. Please map the data.');
      } else {
        // Standard extraction mode
        // 1. Get the room name for better display
        const selectedRoomObj = rooms.find(room => room.hotelRoomId === selectedRoom);
        const roomName = selectedRoomObj?.hotelRoomType || 'Unknown Room';

        // 2. Upload the file directly to Linode via our API
        await updateExtractionStage('Uploading PDF file');

        console.log('[CONTEXT_TRACE] PDF Upload - Uploading file directly', {
          hotel_id: hotelId,
          room_id: selectedRoom,
          room_name: roomName,
          file_name: selectedFile.name,
          file_size: selectedFile.size
        });

        // 3. Create tariff record with the file object
        await updateExtractionStage('Creating tariff record');
        const newTariff = await createTariffUpload({
          hotelId,
          roomId: selectedRoom,
          roomName: roomName,
          filePath: 'uploading...',  // This will be replaced by the actual URL
          file: selectedFile  // Pass the file object directly
        });

        console.log('[CONTEXT_TRACE] PDF Upload - Tariff record created', {
          hotel_id: hotelId,
          room_id: selectedRoom,
          tariff_id: newTariff.tariffId,
          file_path: newTariff.filePath || 'unknown'
        });

        // 4. Extract data from the PDF
        await updateExtractionStage('Extracting data from PDF');
        setUploadProgress(98);

        // 5. Reset form
        setSelectedFile(null);
        const uploadInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        if (uploadInput) uploadInput.value = '';

        // 6. Refresh the tariffs data
        await updateExtractionStage('Finalizing');
        setUploadProgress(100);

        queryClient.invalidateQueries(['tariffs', hotelId]);

        // 7. Open the comparison view for the new tariff
        if (newTariff) {
          setTimeout(() => {
            showComparisonView(newTariff);
          }, 500);
        }

        toast.success('Tariff uploaded successfully');
      }
    } catch (error) {
      console.error('Error uploading tariff:', error);

      // Detailed error message
      if (error instanceof Error) {
        toast.error(`Error uploading tariff: ${error.message}`);
      } else {
        toast.error('Failed to upload tariff. Please try again.');
      }
    } finally {
      setIsUploading(false);
      setExtractionStage(null);
      stopProgressSimulation();
    }
  };

  const handleTariffAction = async (tariffId: string, action: 'approve' | 'reject') => {
    try {
      await updateTariffStatus(
        tariffId,
        action === 'approve' ? 'approved' : 'rejected'
      );
      queryClient.invalidateQueries(['tariffs', hotelId]);

      // Close the details dialog if it's open
      if (tariffDetailsOpen) {
        setTariffDetailsOpen(false);
      }

      // Show success message
      toast.success(`Tariff ${action === 'approve' ? 'approved' : 'rejected'} successfully`);
    } catch (error) {
      console.error(`Error ${action}ing tariff:`, error);
      toast.error(`Failed to ${action} tariff. Please try again.`);
    }
  };

  const handleTariffDelete = async (tariffId: string) => {
    try {
      await deleteTariffUpload(tariffId);
      queryClient.invalidateQueries(['tariffs', hotelId]);

      // Close the details dialog if it's open with the deleted tariff
      if (tariffDetailsOpen && selectedTariff?.tariffId === tariffId) {
        setTariffDetailsOpen(false);
      }

      // Close the comparison view if it's open with the deleted tariff
      if (comparisonOpen && tariffForComparison?.tariffId === tariffId) {
        setComparisonOpen(false);
      }

      toast.success('Tariff deleted successfully');
    } catch (error) {
      console.error('Error deleting tariff:', error);
      toast.error('Failed to delete tariff. Please try again.');
    }
  };

  const showTariffDetails = (tariff: TariffUpload) => {
    setSelectedTariff(tariff);
    setTariffDetailsOpen(true);
  };

  const showComparisonView = async (tariff: TariffUpload) => {
    console.log('[CONTEXT_TRACE] Comparison View - Start', {
      hotel_id: tariff.hotelId,
      room_id: tariff.roomId,
      tariff_id: tariff.tariffId,
      file_path: tariff.filePath
    });

    setTariffForComparison(tariff);

    // If the tariff already has price data, use it
    if (tariff.priceData && tariff.priceData.length > 0) {
      console.log('[CONTEXT_TRACE] Comparison View - Using existing price data', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        tariff_id: tariff.tariffId,
        price_data_count: tariff.priceData.length
      });

      setComparisonOpen(true);
      return;
    }

    // Otherwise, extract data from the PDF
    try {
      // Show extraction in progress
      setExtractionStage('Extracting data from PDF');
      setUploadProgress(30);
      const stopProgressSimulation = simulateProgress();

      console.log('[CONTEXT_TRACE] Comparison View - Extracting data from PDF', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        tariff_id: tariff.tariffId,
        file_path: tariff.filePath
      });

      // Update stage for better UX
      await updateExtractionStage('Analyzing PDF structure');
      setUploadProgress(60);

      // Pass hotelId and roomId explicitly for better extraction
      const extractedData = await extractTariffData(
        tariff.filePath,
        tariff.hotelId,
        tariff.roomId
      );

      await updateExtractionStage('Processing extracted data');
      setUploadProgress(90);

      console.log('[CONTEXT_TRACE] Comparison View - Data extracted successfully', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        tariff_id: tariff.tariffId,
        extracted_data_count: extractedData.length
      });

      // Finalize
      await updateExtractionStage('Finalizing data extraction');
      setUploadProgress(100);

      // If no data was extracted, use fallback mock data
      if (extractedData.length === 0) {
        console.warn('[CONTEXT_TRACE] Comparison View - No data extracted, using fallback data');

        // Generate mock data specific to this room
        const mockData = generateMockExtractedData();

        // Add warning to the user
        toast.error('Could not extract data from PDF. Using estimated data for preview.', {
          duration: 5000,
        });

        // Update the tariff with the mock data
        const updatedTariff = {
          ...tariff,
          priceData: mockData
        };

        setTariffForComparison(updatedTariff);
        setComparisonOpen(true);
        stopProgressSimulation();
        setExtractionStage(null);
        return;
      }

      // Update the tariff with the extracted data
      const updatedTariff = {
        ...tariff,
        priceData: extractedData
      };

      setTariffForComparison(updatedTariff);
      setComparisonOpen(true);
      stopProgressSimulation();
      setExtractionStage(null);
    } catch (error) {
      console.error('Error extracting tariff data:', error);
      toast.error('Failed to extract data from tariff PDF');

      console.log('[CONTEXT_TRACE] Comparison View - Extraction failed', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        tariff_id: tariff.tariffId,
        error: error instanceof Error ? error.message : String(error)
      });

      // Reset state
      setExtractionStage(null);
      setUploadProgress(0);

      // Still show the comparison dialog, but with mock data
      const mockData = generateMockExtractedData();

      const updatedTariff = {
        ...tariff,
        priceData: mockData
      };

      setTariffForComparison(updatedTariff);
      setComparisonOpen(true);
    }
  };

  // Function to get room name by ID with strict hotel validation
  const getRoomName = (roomId: string, tariffHotelId?: string) => {
    // Always use the current hotel ID if tariffHotelId is not provided
    const effectiveHotelId = tariffHotelId || hotelId;

    // Find room with strict hotel ID matching
    const room = rooms?.find(r => {
      // Always match both room ID and hotel ID for accuracy
      return r.hotelRoomId === roomId && r.hotelId === effectiveHotelId;
    });

    // Log the room lookup for verification - only log essential info to avoid object reference issues
    console.log('[PRICE_VERIFICATION] TariffUploadList - getRoomName lookup for roomId:', roomId,
      'found:', !!room,
      'name:', room?.hotelRoomType || 'Unknown Room');

    // If room not found, include hotel info in the unknown message
    if (!room) {
      if (tariffHotelId && tariffHotelId !== hotelId) {
        return `External Room (Hotel ID: ${tariffHotelId.substring(0, 8)}...)`;
      }
      return 'Unknown Room';
    }

    return room.hotelRoomType;
  };

  // Handle saving mapped data
  const handleSaveMappedData = async (mappedData: any) => {
    try {
      console.log('[CONTEXT_TRACE] Saving mapped data', {
        hotel_id: hotelId,
        room_id: selectedRoom,
        room_mappings_count: Object.keys(mappedData.roomMappings || {}).length,
        price_mappings_count: Object.keys(mappedData.priceMappings || {}).length,
        charge_mappings_count: Object.keys(mappedData.chargeMappings || {}).length
      });

      // Add hotel ID to the mapped data
      mappedData.hotelId = hotelId;

      // Save the mapped data
      await saveMappedTariffData(mappedData);

      // Close the mapping workflow
      setMappingWorkflowOpen(false);

      // Refresh the tariffs data
      queryClient.invalidateQueries(['tariffs', hotelId]);

      toast.success('Tariff data saved successfully');
    } catch (error) {
      console.error('Error saving mapped data:', error);

      // Detailed error message
      if (error instanceof Error) {
        toast.error(`Error saving tariff data: ${error.message}`);
      } else {
        toast.error('Failed to save tariff data. Please try again.');
      }

      // Don't close the mapping workflow on error
      return Promise.reject(error);
    }
  };

  // Enhanced mock extracted data for demonstration purposes
  // In a real implementation, this would come from the backend after processing the PDF
  const generateMockExtractedData = (): TariffPriceData[] => {
    if (!tariffForComparison) return [];

    // Find the room for this tariff
    const room = rooms.find(r => r.hotelRoomId === tariffForComparison.roomId);
    if (!room) {
      console.warn('[PRICE_VERIFICATION] Room not found for tariff:', tariffForComparison.roomId);
      // Return some default data even if room not found
      return generateDefaultMockData();
    }

    if (!room.mealPlan || room.mealPlan.length === 0) {
      console.warn('[PRICE_VERIFICATION] No meal plans found for room:', room.hotelRoomType);
      // Return some default data even if no meal plans
      return generateDefaultMockData();
    }

    console.log('[PRICE_VERIFICATION] Generating mock data for room:', room.hotelRoomType, 'with meal plans:', room.mealPlan);

    // Generate sample extracted data based on existing meal plans
    const result: TariffPriceData[] = [];

    // Group meal plans by type
    const mealPlanTypes = [...new Set(room.mealPlan.map(mp => mp.mealPlan))];

    // If no meal plan types found, use default types
    if (mealPlanTypes.length === 0) {
      console.warn('[PRICE_VERIFICATION] No meal plan types found, using defaults');
      mealPlanTypes.push('cp', 'map', 'ap');
    }

    // Create more realistic date ranges for the next year
    const today = new Date();
    const currentYear = today.getFullYear();
    const nextYear = currentYear + 1;

    // Season date ranges (common in hotel industry)
    const dateRanges = [
      // Current year seasons
      {
        start: `${currentYear}-10-01`,
        end: `${currentYear}-12-20`,
        label: 'Fall Season'
      },
      {
        start: `${currentYear}-12-21`,
        end: `${currentYear}-12-31`,
        label: 'Winter Holiday Season'
      },
      // Next year seasons
      {
        start: `${nextYear}-01-01`,
        end: `${nextYear}-03-31`,
        label: 'Winter Season'
      },
      {
        start: `${nextYear}-04-01`,
        end: `${nextYear}-06-30`,
        label: 'Spring Season'
      },
      {
        start: `${nextYear}-07-01`,
        end: `${nextYear}-09-30`,
        label: 'Summer Season'
      }
    ];

    // Log the date ranges for debugging
    console.log('[PRICE_VERIFICATION] Generated date ranges:', dateRanges);

    mealPlanTypes.forEach(type => {
      // Find base price for this meal plan type
      const baseMealPlan = room.mealPlan.find(mp => mp.mealPlan === type);
      let basePrice = 2500; // Default price if not found

      if (baseMealPlan) {
        basePrice = baseMealPlan.roomPrice || 2500;
        console.log('[PRICE_VERIFICATION] Found base price for meal plan:', type, 'price:', basePrice);
      } else {
        console.warn('[PRICE_VERIFICATION] No base price found for meal plan:', type, 'using default:', basePrice);
      }

      // Generate prices for each date range with seasonal variations
      dateRanges.forEach((range) => {
        // Apply seasonal price variations
        let seasonalPrice = basePrice;

        // Winter holiday season (highest prices)
        if (range.label === 'Winter Holiday Season') {
          seasonalPrice = Math.round(basePrice * 1.5); // 50% higher
        }
        // Summer season (higher prices)
        else if (range.label === 'Summer Season') {
          seasonalPrice = Math.round(basePrice * 1.3); // 30% higher
        }
        // Spring season (slightly higher prices)
        else if (range.label === 'Spring Season') {
          seasonalPrice = Math.round(basePrice * 1.1); // 10% higher
        }
        // Fall season (lower prices)
        else if (range.label === 'Fall Season') {
          seasonalPrice = Math.round(basePrice * 0.9); // 10% lower
        }

        // Add some randomness (±5%)
        const randomFactor = 0.95 + (Math.random() * 0.1);
        const finalPrice = Math.round(seasonalPrice * randomFactor);

        // Create the price data entry
        result.push({
          mealPlanType: type,
          startDate: range.start,
          endDate: range.end,
          roomPrice: finalPrice,
          hotelId: room.hotelId,
          roomId: room.hotelRoomId
        });
      });
    });

    // Log the generated data for debugging
    console.log('[PRICE_VERIFICATION] Generated mock price data:', result);
    return result;
  };

  // Generate default mock data when room or meal plan info is not available
  const generateDefaultMockData = (): TariffPriceData[] => {
    console.log('[PRICE_VERIFICATION] Generating default mock data');

    const result: TariffPriceData[] = [];
    const mealPlanTypes = ['cp', 'map', 'ap'];
    const basePrices = { cp: 2500, map: 3000, ap: 3500 };

    // Create date ranges for the next year
    const today = new Date();
    const currentYear = today.getFullYear();
    const nextYear = currentYear + 1;

    const dateRanges = [
      { start: `${currentYear}-10-01`, end: `${currentYear}-12-20` },
      { start: `${currentYear}-12-21`, end: `${currentYear}-12-31` },
      { start: `${nextYear}-01-01`, end: `${nextYear}-03-31` },
      { start: `${nextYear}-04-01`, end: `${nextYear}-09-30` }
    ];

    mealPlanTypes.forEach(type => {
      const basePrice = basePrices[type as keyof typeof basePrices];

      dateRanges.forEach((range, index) => {
        // Apply seasonal variations
        let seasonalFactor = 1.0;
        if (index === 1) seasonalFactor = 1.5; // Holiday season
        if (index === 3) seasonalFactor = 1.2; // Summer season

        result.push({
          mealPlanType: type,
          startDate: range.start,
          endDate: range.end,
          roomPrice: Math.round(basePrice * seasonalFactor),
          hotelId: tariffForComparison?.hotelId || '',
          roomId: tariffForComparison?.roomId || ''
        });
      });
    });

    return result;
  };

  // Main component render
  return (
    <div className="space-y-6">
      {/* Feature Info Alert */}
      <Alert variant="info" className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <AlertTitle className="text-blue-800">Hotel Tariff Extraction System</AlertTitle>
            <AlertDescription className="text-blue-700">
              Upload hotel tariff PDFs to automatically extract room rates, meal plans, and date ranges.
              The system uses advanced extraction techniques to process different PDF formats and extract structured pricing data.
            </AlertDescription>
          </div>
        </div>
      </Alert>

      {/* Upload Form Card */}
      <Card className="border-gray-200 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-800">Upload New Tariff</CardTitle>
          <CardDescription>
            Upload a PDF file containing room rates for a specific hotel room
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {/* Room Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Room
              </label>
              <select
                value={selectedRoom}
                onChange={(e) => setSelectedRoom(e.target.value)}
                className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                disabled={isUploading}
              >
                <option value="">Select a room</option>
                {rooms?.map((room) => (
                  <option key={room.hotelRoomId} value={room.hotelRoomId}>
                    {room.hotelRoomType}
                  </option>
                ))}
              </select>
            </div>

            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tariff PDF
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                  disabled={isUploading}
                  className="block w-full text-sm text-gray-500
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-md file:border-0
                    file:text-sm file:font-medium
                    file:bg-blue-50 file:text-blue-700
                    hover:file:bg-blue-100"
                />
              </div>
              {selectedFile && (
                <p className="mt-1 text-sm text-gray-500 flex items-center">
                  <FileText size={14} className="mr-1" />
                  {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)
                </p>
              )}
            </div>
          </div>

          {/* Enhanced Extraction Toggle */}
          <div className="flex items-center space-x-2 mb-4">
            <Switch
              id="enhanced-extraction"
              checked={useEnhancedExtraction}
              onCheckedChange={setUseEnhancedExtraction}
              disabled={isUploading}
            />
            <Label htmlFor="enhanced-extraction" className="flex items-center cursor-pointer">
              <Sparkles size={16} className={`mr-1 ${useEnhancedExtraction ? 'text-blue-500' : 'text-gray-400'}`} />
              Use Enhanced AI Extraction
              <Badge className="ml-2 bg-blue-100 text-blue-800 hover:bg-blue-200">Recommended</Badge>
            </Label>
            <div className="text-xs text-gray-500 ml-2">
              {useEnhancedExtraction
                ? 'Interactive mapping with AI-powered extraction'
                : 'Standard extraction with automatic approval'}
            </div>
          </div>

          {/* Upload Progress */}
          {isUploading && (
            <div className="mb-4">
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium text-gray-700">
                  {extractionStage || 'Processing...'}
                </span>
                <span className="text-sm text-gray-500">{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          <Button
            onClick={handleTariffUpload}
            disabled={!selectedFile || !selectedRoom || isUploading}
            className="w-full md:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            {isUploading ? (
              <>
                <RefreshCw size={16} className="mr-2 animate-spin" />
                {extractionStage || 'Processing...'}
              </>
            ) : (
              <>
                <FileUp size={16} className="mr-2" />
                Upload & Extract Tariff
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Error Message */}
      {isError && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4 mr-2" />
          <AlertTitle>Error Loading Tariffs</AlertTitle>
          <AlertDescription>
            There was an error fetching tariff data. The backend API might not be fully implemented yet.
          </AlertDescription>
        </Alert>
      )}

      {/* Tariff List */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-800">Uploaded Tariffs</CardTitle>
              <CardDescription>
                {tariffs.length} {tariffs.length === 1 ? 'tariff' : 'tariffs'} uploaded for this hotel
              </CardDescription>
            </div>

            {/* Filter tabs */}
            <TabsList className="bg-gray-100">
              <TabsTrigger
                value="all"
                onClick={() => setStatusFilter('all')}
                className={statusFilter === 'all' ? 'bg-white' : ''}
              >
                All
              </TabsTrigger>
              <TabsTrigger
                value="pending"
                onClick={() => setStatusFilter('pending')}
                className={statusFilter === 'pending' ? 'bg-white' : ''}
              >
                Pending
              </TabsTrigger>
              <TabsTrigger
                value="approved"
                onClick={() => setStatusFilter('approved')}
                className={statusFilter === 'approved' ? 'bg-white' : ''}
              >
                Approved
              </TabsTrigger>
              <TabsTrigger
                value="rejected"
                onClick={() => setStatusFilter('rejected')}
                className={statusFilter === 'rejected' ? 'bg-white' : ''}
              >
                Rejected
              </TabsTrigger>
            </TabsList>
          </div>
        </CardHeader>

        <CardContent>
          {filteredTariffs.length === 0 ? (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center text-gray-500 flex flex-col items-center">
              <FileText className="h-12 w-12 text-gray-300 mb-2" />
              {statusFilter !== 'all' ? (
                <p>No {statusFilter} tariff files have been uploaded yet.</p>
              ) : (
                <p>No tariff files have been uploaded yet.</p>
              )}
              <p className="text-sm mt-2">Upload a tariff PDF to get started.</p>
            </div>
          ) : (
            <div className="bg-white border rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Room Type
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tariff File
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Upload Date
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredTariffs.map((tariff) => (
                      <tr key={tariff.tariffId} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {getRoomName(tariff.roomId, tariff.hotelId)}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <a
                            href={tariff.filePath.includes('mock')
                              ? '#'
                              : `https://tripemilestone.in-maa-1.linodeobjects.com/${tariff.filePath}`
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center text-blue-600 hover:underline"
                            onClick={(e) => {
                              if (tariff.filePath.includes('mock')) {
                                e.preventDefault();
                                toast.success('This is a demo file (not actually uploaded to server)');
                              }
                            }}
                          >
                            <FileText size={16} className="mr-1" />
                            {tariff.filePath.split('-').pop() || 'Demo-Tariff.pdf'}
                            {tariff.filePath.includes('mock') && <span className="ml-1 text-xs text-amber-600">(DEMO)</span>}
                          </a>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Calendar size={14} className="mr-1 text-gray-400" />
                            {format(new Date(tariff.uploadDate), 'MMM d, yyyy')}
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <StatusBadge status={tariff.status} />
                        </td>
                        <td className="px-4 py-3 text-sm text-right space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => showTariffDetails(tariff)}
                            className="text-blue-600 border-blue-200 hover:bg-blue-50"
                          >
                            Details
                          </Button>

                          {tariff.status === 'pending' && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => showComparisonView(tariff)}
                                className="text-blue-600 border-blue-200 hover:bg-blue-50"
                              >
                                Compare
                              </Button>

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleTariffAction(tariff.tariffId!, 'approve')}
                                className="text-green-600 border-green-200 hover:bg-green-50"
                              >
                                <Check size={16} className="mr-1" />
                                Approve
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleTariffAction(tariff.tariffId!, 'reject')}
                                className="text-red-600 border-red-200 hover:bg-red-50"
                              >
                                <X size={16} className="mr-1" />
                                Reject
                              </Button>
                            </>
                          )}

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTariffDelete(tariff.tariffId!)}
                            className="text-red-600 border-red-200 hover:bg-red-50"
                          >
                            <Trash2 size={14} className="mr-1" />
                            Delete
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tariff Details Dialog */}
      {selectedTariff && (
        <Dialog open={tariffDetailsOpen} onOpenChange={setTariffDetailsOpen}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Tariff Details</DialogTitle>
              <DialogDescription>
                Details for {getRoomName(selectedTariff.roomId)}
                {selectedTariff.tariffId?.includes('mock') && <span className="ml-2 text-amber-600 text-xs">(DEMO MODE)</span>}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 my-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Room</h4>
                  <p className="text-sm">{getRoomName(selectedTariff.roomId)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <StatusBadge status={selectedTariff.status} />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Upload Date</h4>
                  <div className="flex items-center text-sm">
                    <Clock size={14} className="mr-1 text-gray-400" />
                    {format(new Date(selectedTariff.uploadDate), 'MMM d, yyyy')}
                  </div>
                </div>
                {selectedTariff.approvalDate && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Approval Date</h4>
                    <div className="flex items-center text-sm">
                      <Clock size={14} className="mr-1 text-gray-400" />
                      {format(new Date(selectedTariff.approvalDate), 'MMM d, yyyy')}
                    </div>
                  </div>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-500">Tariff File</h4>
                <a
                  href={selectedTariff.filePath.includes('mock')
                    ? '#'
                    : `https://tripemilestone.in-maa-1.linodeobjects.com/${selectedTariff.filePath}`
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 text-sm hover:underline flex items-center"
                  onClick={(e) => {
                    if (selectedTariff.filePath.includes('mock')) {
                      e.preventDefault();
                      toast.success('This is a demo file (not actually uploaded to server)');
                    }
                  }}
                >
                  <FileText size={16} className="mr-1" />
                  <span className="truncate max-w-[250px]">
                    {selectedTariff.filePath.split('-').pop() || 'Demo-Tariff.pdf'}
                  </span>
                  {selectedTariff.filePath.includes('mock') && <span className="ml-1 text-xs text-amber-600">(DEMO)</span>}
                </a>
              </div>

              {selectedTariff.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Notes</h4>
                  <p className="text-sm">{selectedTariff.notes}</p>
                </div>
              )}

              {selectedTariff.priceData && selectedTariff.priceData.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Price Data</h4>
                  <div className="border rounded-md overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Meal Plan</th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500">Date Range</th>
                          <th className="px-3 py-2 text-right text-xs font-medium text-gray-500">Price (₹)</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {selectedTariff.priceData.map((price, index) => (
                          <tr key={index}>
                            <td className="px-3 py-2 text-xs">{price.mealPlanType.toUpperCase()}</td>
                            <td className="px-3 py-2 text-xs">
                              {format(new Date(price.startDate), 'MMM d, yyyy')} -
                              {format(new Date(price.endDate), 'MMM d, yyyy')}
                            </td>
                            <td className="px-3 py-2 text-xs text-right">{price.roomPrice.toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {selectedTariff.status === 'pending' && (
                <Alert variant="warning" className="mt-4">
                  <Info className="h-4 w-4 mr-2" />
                  <AlertTitle>Processing Required</AlertTitle>
                  <AlertDescription>
                    This tariff needs to be processed and approved for prices to be updated.
                  </AlertDescription>
                </Alert>
              )}

              {selectedTariff.tariffId?.includes('mock') && (
                <Alert variant="info" className="mt-4">
                  <Info className="h-4 w-4 text-blue-500 mr-2" />
                  <AlertTitle className="text-blue-800">Demo Mode</AlertTitle>
                  <AlertDescription className="text-blue-700">
                    This is a demo tariff record. In production, this would be stored in the database.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setTariffDetailsOpen(false)}>
                Close
              </Button>

              {selectedTariff.status === 'pending' && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setTariffDetailsOpen(false);
                      showComparisonView(selectedTariff);
                    }}
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    Compare
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleTariffAction(selectedTariff.tariffId!, 'approve');
                      setTariffDetailsOpen(false);
                    }}
                    className="text-green-600 border-green-200 hover:bg-green-50"
                  >
                    <Check size={16} className="mr-1" />
                    Approve
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleTariffAction(selectedTariff.tariffId!, 'reject');
                      setTariffDetailsOpen(false);
                    }}
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <X size={16} className="mr-1" />
                    Reject
                  </Button>
                </>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Mapping Workflow Dialog */}
      <Dialog
        open={mappingWorkflowOpen}
        onOpenChange={(open) => {
          // Prevent accidental closing
          if (!open && !confirm('Are you sure you want to close? Any unsaved changes will be lost.')) {
            return;
          }
          setMappingWorkflowOpen(open);
        }}
      >
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Wand2 className="mr-2 h-5 w-5 text-blue-500" />
              Interactive Tariff Mapping
            </DialogTitle>
            <DialogDescription>
              Map extracted data from your PDF to your hotel database
            </DialogDescription>
          </DialogHeader>

          {extractedData && (
            <MappingWorkflow
              hotelId={hotelId}
              roomId={selectedRoom}
              hotelRooms={rooms}
              extractedData={extractedData}
              onSave={handleSaveMappedData}
              onCancel={() => setMappingWorkflowOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Tariff Comparison Dialog */}
      {tariffForComparison && (
        <Dialog
          open={comparisonOpen}
          onOpenChange={setComparisonOpen}
        >
          <DialogContent className="max-w-5xl">
            <DialogHeader>
              <DialogTitle>Tariff Comparison</DialogTitle>
              <DialogDescription>
                Compare extracted tariff data with existing pricing
              </DialogDescription>
            </DialogHeader>

            {/* PRICE VERIFICATION: Log the data being passed to TariffComparison */}
            {(() => {
              // Log the tariff data first
              console.log('[PRICE_VERIFICATION] TariffUploadList - Tariff data for comparison', {
                tariff_hotel_id: tariffForComparison.hotelId,
                tariff_room_id: tariffForComparison.roomId,
                current_hotel_id: hotelId,
                tariff_id: tariffForComparison.tariffId,
                file_path: tariffForComparison.filePath
              });

              // Log the rooms array to understand its scope
              console.log('[PRICE_VERIFICATION] TariffUploadList - Rooms array scope', {
                rooms_count: rooms.length,
                rooms_sample: rooms.slice(0, 3).map(r => ({
                  hotel_id: r.hotelId,
                  room_id: r.hotelRoomId,
                  room_type: r.hotelRoomType
                })),
                unique_hotel_ids: [...new Set(rooms.map(r => r.hotelId))],
                contains_multiple_hotels: new Set(rooms.map(r => r.hotelId)).size > 1,
                current_hotel_id: hotelId,
                rooms_from_current_hotel: rooms.filter(r => r.hotelId === hotelId).length,
                rooms_from_other_hotels: rooms.filter(r => r.hotelId !== hotelId).length
              });

              // Find the room data for the tariff with strict validation
              const roomData = rooms.find(r => {
                // Verify both IDs are defined and match exactly
                const roomIdMatch = r.hotelRoomId &&
                                   tariffForComparison.roomId &&
                                   r.hotelRoomId === tariffForComparison.roomId;

                const hotelIdMatch = r.hotelId &&
                                    tariffForComparison.hotelId &&
                                    r.hotelId === tariffForComparison.hotelId;

                return roomIdMatch && hotelIdMatch;
              });

              // Log the room data and filter conditions with detailed validation
              console.log('[PRICE_VERIFICATION] TariffUploadList - Room data for comparison', {
                tariff_hotel_id: tariffForComparison.hotelId,
                tariff_room_id: tariffForComparison.roomId,
                current_hotel_id: hotelId,
                found_room: !!roomData,
                room_name: roomData ? roomData.hotelRoomType : 'Not found',
                room_hotel_id: roomData ? roomData.hotelId : 'N/A',
                room_id: roomData ? roomData.hotelRoomId : 'N/A',
                filter_condition: `r.hotelRoomId === ${tariffForComparison.roomId} && r.hotelId === ${tariffForComparison.hotelId}`,
                all_rooms_count: rooms.length,
                all_room_ids: rooms.map(r => r.hotelRoomId).slice(0, 10), // Show first 10 room IDs
                all_hotel_ids: rooms.map(r => r.hotelId).slice(0, 10),    // Show first 10 hotel IDs
                rooms_with_matching_room_id: rooms.filter(r => r.hotelRoomId === tariffForComparison.roomId).length,
                rooms_with_matching_hotel_id: rooms.filter(r => r.hotelId === tariffForComparison.hotelId).length,
                rooms_with_both_matching: rooms.filter(r =>
                  r.hotelRoomId === tariffForComparison.roomId &&
                  r.hotelId === tariffForComparison.hotelId
                ).length
              });

              // Check for potential issues
              if (!roomData) {
                console.warn('[PRICE_VERIFICATION] TariffUploadList - WARNING: No matching room found', {
                  tariff_hotel_id: tariffForComparison.hotelId,
                  tariff_room_id: tariffForComparison.roomId,
                  possible_matches: rooms.filter(r =>
                    r.hotelRoomId === tariffForComparison.roomId ||
                    r.hotelId === tariffForComparison.hotelId
                  ).map(r => ({
                    hotel_id: r.hotelId,
                    room_id: r.hotelRoomId,
                    room_type: r.hotelRoomType
                  }))
                });
              }

              // Log the meal plan data
              console.log('[PRICE_VERIFICATION] TariffUploadList - Meal plan data for comparison', {
                tariff_hotel_id: tariffForComparison.hotelId,
                tariff_room_id: tariffForComparison.roomId,
                meal_plan_count: roomData?.mealPlan?.length || 0,
                meal_plan_types: roomData?.mealPlan?.map(mp => mp.mealPlan) || [],
                meal_plan_sample: roomData?.mealPlan?.slice(0, 2) || [],
                extracted_data_count: (tariffForComparison.priceData || []).length,
                using_mock_data: !tariffForComparison.priceData || tariffForComparison.priceData.length === 0
              });

              return null;
            })()}

            {/* Extraction Progress */}
            {extractionStage && (
              <div className="mb-6 bg-blue-50 border border-blue-100 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <RefreshCw size={16} className="mr-2 text-blue-600 animate-spin" />
                    <span className="font-medium text-blue-800">{extractionStage}</span>
                  </div>
                  <span className="text-sm text-blue-700">{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="h-2 bg-blue-100" />
              </div>
            )}

            {/* Get the room data with robust validation */}
            {(() => {
              // Find the room with strict validation of both hotel_id and room_id
              const matchingRoom = rooms.find(r => {
                // Ensure both IDs are defined and match exactly
                const roomIdMatch = r.hotelRoomId &&
                                   tariffForComparison.roomId &&
                                   r.hotelRoomId === tariffForComparison.roomId;

                const hotelIdMatch = r.hotelId &&
                                    tariffForComparison.hotelId &&
                                    r.hotelId === tariffForComparison.hotelId;

                // Only match rooms from the same hotel as the tariff
                return roomIdMatch && hotelIdMatch;
              });

              // Log the final room selection
              console.log('[PRICE_VERIFICATION] TariffUploadList - Final room selection for existingData', {
                tariff_hotel_id: tariffForComparison.hotelId,
                tariff_room_id: tariffForComparison.roomId,
                found_matching_room: !!matchingRoom,
                selected_room_id: matchingRoom?.hotelRoomId || 'none',
                selected_hotel_id: matchingRoom?.hotelId || 'none',
                selected_room_type: matchingRoom?.hotelRoomType || 'none',
                meal_plan_count: matchingRoom?.mealPlan?.length || 0
              });

              return (
                <TariffComparison
                  tariff={tariffForComparison}
                  existingData={matchingRoom?.mealPlan || []}
                  extractedData={tariffForComparison.priceData || generateMockExtractedData()}
                  roomName={matchingRoom?.hotelRoomType || 'Unknown Room'}
                  onApprove={() => {
                    setComparisonOpen(false);
                    queryClient.invalidateQueries(['tariffs', hotelId]);
                  }}
                  onReject={() => {
                    setComparisonOpen(false);
                    queryClient.invalidateQueries(['tariffs', hotelId]);
                  }}
                  onClose={() => setComparisonOpen(false)}
                  rooms={rooms}
                />
              );
            })()}
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Add error boundary
const ErrorBoundary: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = () => {
      setHasError(true);
      // Log to console
      console.error('[TariffUploadList] Error boundary caught an error');
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
        <h3 className="font-medium">Something went wrong</h3>
        <p className="text-sm mt-1">
          There was an error rendering the Tariff Upload component. Check the console for more details.
        </p>
        {fallback}
      </div>
    );
  }

  return <>{children}</>;
};

export default function WrappedTariffUploadList(props: TariffUploadListProps) {
  return (
    <ErrorBoundary
      fallback={
        <div className="p-8 text-center">
          <h3 className="text-lg font-medium text-gray-700 mb-2">Tariff Upload</h3>
          <p className="text-gray-500">
            Unable to load the tariff upload component. Please refresh the page or contact support.
          </p>
        </div>
      }
    >
      <TariffUploadList {...props} />
    </ErrorBoundary>
  );
}
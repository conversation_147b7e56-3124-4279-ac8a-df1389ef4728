import { DateRange, Range, RangeKeyDict } from 'react-date-range';
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { FC, useState } from 'react';

interface DatePickerProps {
  onDateChange: (range: [Date | null, Date | null]) => void;
}

const DatePickerComponent: FC<DatePickerProps> = ({ onDateChange }) => {
  const [selection, setSelection] = useState<Range>({
    startDate: new Date(),
    endDate: new Date(),
    key: 'selection'
  });

  const handleSelect = (ranges: RangeKeyDict) => {
    const range = ranges.selection;
    setSelection(range);
    onDateChange([range.startDate || null, range.endDate || null]);
  };

  return (
    <DateRange
      ranges={[selection]}
      onChange={handleSelect}
      moveRangeOnFirstSelection={false}
      months={1}
      direction="horizontal"
    />
  );
};

export default DatePickerComponent;

import '@mobiscroll/react/dist/css/mobiscroll.min.css';
import { Datepicker, setOptions } from '@mobiscroll/react';
import { FC } from 'react';

setOptions({
  theme: 'ios',
  themeVariant: 'light'
});

interface DatePickerProps {
  onDateChange: (range: [Date | null, Date | null]) => void;
}

const DatePickerComponent: FC<DatePickerProps> = ({ onDateChange }) => {
  return (
    <Datepicker 
      controls={['calendar']} 
      display="inline" 
      select="range" 
      showRangeLabels={true}
      onChange={(event) => {
        const range = event.value as [Date | null, Date | null]; // Ensure correct type
        onDateChange(range) 
        ;
      }}
    />
  );
};

export default DatePickerComponent;

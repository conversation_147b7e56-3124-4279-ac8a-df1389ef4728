"use client";

import * as React from "react";

// Try to import Radix UI components
let TabsPrimitive: any;
try {
  TabsPrimitive = require("@radix-ui/react-tabs");
} catch (error) {
  console.error("Failed to load @radix-ui/react-tabs:", error);
  // We'll handle this case with a fallback implementation
}

import { cn } from "@/lib/utils";

// Fallback implementation if Radix UI fails to load
const FallbackTabsRoot: React.FC<React.HTMLAttributes<HTMLDivElement> & { defaultValue?: string }> = ({ 
  children, 
  className,
  ...props 
}) => (
  <div className={cn("w-full", className)} {...props}>
    {children}
  </div>
);

const FallbackTabsList: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({ 
  children, 
  className,
  ...props 
}) => (
  <div
    className={cn(
      "inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

const FallbackTabsTrigger: React.FC<
  React.ButtonHTMLAttributes<HTMLButtonElement> & { value?: string }
> = ({ children, className, value, ...props }) => {
  const [selected, setSelected] = React.useState(false);
  
  // Implementation would need to communicate with parent for real behavior
  
  return (
    <button
      className={cn(
        "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50",
        selected ? "bg-background text-foreground shadow-sm" : "",
        className
      )}
      {...props}
      onClick={(e) => {
        setSelected(true);
        props.onClick?.(e);
      }}
    >
      {children}
    </button>
  );
};

const FallbackTabsContent: React.FC<
  React.HTMLAttributes<HTMLDivElement> & { value?: string }
> = ({ children, className, value, ...props }) => {
  return (
    <div
      className={cn(
        "mt-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// Use Radix UI if available, otherwise use fallback
const Tabs = TabsPrimitive ? TabsPrimitive.Root : FallbackTabsRoot;

const TabsList = TabsPrimitive 
  ? React.forwardRef<
      React.ElementRef<typeof TabsPrimitive.List>,
      React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
    >(({ className, ...props }, ref) => (
      <TabsPrimitive.List
        ref={ref}
        className={cn(
          "inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
          className
        )}
        {...props}
      />
    ))
  : FallbackTabsList;

const TabsTrigger = TabsPrimitive
  ? React.forwardRef<
      React.ElementRef<typeof TabsPrimitive.Trigger>,
      React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
    >(({ className, ...props }, ref) => (
      <TabsPrimitive.Trigger
        ref={ref}
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
          className
        )}
        {...props}
      />
    ))
  : FallbackTabsTrigger;

const TabsContent = TabsPrimitive
  ? React.forwardRef<
      React.ElementRef<typeof TabsPrimitive.Content>,
      React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
    >(({ className, ...props }, ref) => (
      <TabsPrimitive.Content
        ref={ref}
        className={cn(
          "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          className
        )}
        {...props}
      />
    ))
  : FallbackTabsContent;

if (TabsPrimitive) {
  TabsList.displayName = TabsPrimitive.List.displayName;
  TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;
  TabsContent.displayName = TabsPrimitive.Content.displayName;
}

export { Tabs, TabsList, TabsTrigger, TabsContent }; 
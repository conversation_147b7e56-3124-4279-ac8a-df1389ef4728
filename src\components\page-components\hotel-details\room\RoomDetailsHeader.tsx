import { ChevronRight, Edit, Home, Leaf, Users } from "lucide-react";
import { Link } from "react-router-dom";

export interface RoomGetData {
  _id: string;
  hotelName: string;
  hotelId: string;
  hotelRoomId: string;
  hotelRoomType: string;
  maxAdult: number;
  maxChild: number;
  maxInf: number;
  roomCapacity: number;
  amenities: string[];
  mealPlan: MealPlan[];
  __v: number;
}

export interface MealPlan {
  hotelId: string;
  hotelRoomId: string;
  hotelMealId: string;
  mealPlan: string;
  roomPrice: number;
  gstPer: number;
  adultPrice: number;
  childPrice: number;
  seasonType: string;
  startDate: string[];
  endDate: string[];
  _id: string;
}

interface RoomDetailsHeaderProps {
  room: RoomGetData | undefined;
}

export default function RoomDetailsHeader(props: RoomDetailsHeaderProps) {
  const { room } = props;

  return (
    <div className="bg-white shadow-sm rounded-lg mb-6">
      {/* Breadcrumbs navigation */}
      <div className="px-6 py-3 border-b border-gray-100">
        <div className="flex items-center text-sm text-gray-500">
          <Link to="/dashboard" className="flex items-center hover:text-blue-600 transition-colors">
            <Home size={14} className="mr-1" />
            <span>Dashboard</span>
          </Link>
          <ChevronRight size={14} className="mx-2" />
          <Link to="/hotels" className="hover:text-blue-600 transition-colors">
            Hotels
          </Link>
          <ChevronRight size={14} className="mx-2" />
          {room?.hotelId && (
            <>
              <Link 
                to={`/hotels/${room.hotelId}`} 
                className="hover:text-blue-600 transition-colors truncate max-w-[150px]"
              >
                {room.hotelName}
              </Link>
              <ChevronRight size={14} className="mx-2" />
            </>
          )}
          <span className="text-gray-900 font-medium truncate max-w-[150px]">
            {room?.hotelRoomType || "Room Details"}
          </span>
        </div>
      </div>

      {/* Main header content */}
      <div className="p-6 flex justify-between items-center">
        <div className="flex items-start">
          {/* Room details */}
          <div className="flex flex-col">
            <h1 className="text-2xl font-semibold text-gray-900 mb-3">{room?.hotelRoomType}</h1>
            
            <div className="grid grid-cols-2 gap-x-8 gap-y-2">
              <div className="flex items-center text-sm">
                <Users size={16} className="mr-2 text-gray-400" />
                <span className="font-medium text-gray-700">Adult Capacity:</span>
                <span className="ml-2 text-gray-600">{room?.maxAdult || 0}</span>
              </div>
              
              <div className="flex items-center text-sm">
                <Users size={16} className="mr-2 text-gray-400" />
                <span className="font-medium text-gray-700">Child Capacity:</span>
                <span className="ml-2 text-gray-600">{room?.maxChild || 0}</span>
              </div>
              
              <div className="flex items-center text-sm">
                <Users size={16} className="mr-2 text-gray-400" />
                <span className="font-medium text-gray-700">Infant Capacity:</span>
                <span className="ml-2 text-gray-600">{room?.maxInf || 0}</span>
              </div>
              
              <div className="flex items-center text-sm">
                <Leaf size={16} className="mr-2 text-gray-400" />
                <span className="font-medium text-gray-700">Amenities:</span>
                <span className="ml-2 text-gray-600">{room?.amenities?.length || 0}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center space-x-3">
          {room?.hotelId && (
            <Link
              to={`/hotels/${room.hotelId}`}
              className="flex items-center px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Back to Hotel
            </Link>
          )}

          {room?.hotelRoomId && (
            <Link
              to={`/hotels/room/edit/${room.hotelRoomId}`}
              className="flex items-center px-4 py-2 text-sm text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Edit size={16} className="mr-1.5" />
              Edit Room
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}

import sys
import os
import json
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_extraction_test.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('advanced_tariff_test')

# Import the extraction function
from advanced_pdf_extractor import TariffExtractor

def main():
    if len(sys.argv) < 2:
        logger.error("Usage: python test_advanced_extractor.py <pdf_file_path>")
        print("Usage: python test_advanced_extractor.py <pdf_file_path>")
        sys.exit(1)

    pdf_path = sys.argv[1]

    if not os.path.exists(pdf_path):
        logger.error(f"Error: File {pdf_path} not found")
        print(f"Error: File {pdf_path} not found")
        sys.exit(1)

    logger.info(f"Testing extraction from: {pdf_path}")
    print(f"\nExtracting data from {pdf_path}...")

    try:
        # Create extractor instance
        extractor = TariffExtractor(pdf_path)

        # Extract data
        results = extractor.extract()

        # Output the results
        print("\n==== EXTRACTION RESULTS ====")
        if results is None:
            print("No results were extracted from the PDF.")
        else:
            print(f"Found {len(results)} tariff entries")
            print(json.dumps(results, indent=2))

        # Save results to file
        output_file = f"{os.path.splitext(pdf_path)[0]}_advanced_results.json"
        with open(output_file, 'w') as f:
            if results is None:
                json.dump([], f, indent=2)
            else:
                json.dump(results, f, indent=2)

        print("\nExtraction test complete. Check advanced_extraction_test.log for detailed logs.")
        print(f"\nResults saved to {output_file}")

    except Exception as e:
        error_msg = f"Error during extraction: {str(e)}"
        logger.error(error_msg)
        print(error_msg, file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()

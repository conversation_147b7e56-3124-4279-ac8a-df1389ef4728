
import { ReactElement, useEffect, useState } from "react";
import RoomDetailsHeader, { RoomGetData } from "@/components/page-components/hotel-details/room/RoomDetailsHeader";
import AddMealPlans from "@/components/page-components/hotel-details/room/mealPlan/AddMealPlans";
import AllMealPlan from "@/components/page-components/hotel-details/room/mealPlan/AllMealPlan";
import { getHotelRooms } from "@/utils/api-functions/getHotelRooms";
import { Link, useParams } from "react-router-dom";
interface Button {
  text: string;
  element: ReactElement;
}
export default function RoomDetails() {
  const buttons = [
    { text: "Meal Plan", element: <AllMealPlan /> },
    { text: "Add Meal Plan", element: <AddMealPlans /> },
    
  ];
  const [selectedButton, setSelectedButton] = useState<Button>(buttons[0]);
  const { id, id2 } = useParams();
  const [room, setRoom] = useState<RoomGetData | undefined>();
  async function setHotelById() {
    const data:RoomGetData[] | undefined = await getHotelRooms(id as string);
    const room:RoomGetData | undefined  = (data as RoomGetData[]).find((k: RoomGetData) => {
      return k.hotelRoomId === id2;
    });
    setRoom(room);
  }

  useEffect(() => {
    setHotelById();
  }, []);
  return (
    <div>
        <RoomDetailsHeader room={room}/>
        <div className="flex">
          {buttons?.map((k) => (
            <button
              onClick={() => setSelectedButton(k)}
              className={
                selectedButton.text === k.text
                  ? "m-2 px-2 py-1 rounded-lg bg-gray-300"
                  : "m-2 px-2 py-1 rounded-lg"
              }
              key={k.text}
            >
              {k.text}
            </button>
           
          ))}
           <Link to={`/hotels/edit/${room?.hotelId}`}>
            <button className="m-2 px-2 py-1 rounded-lg">Update Room</button>
            </Link>
        </div>
        <div className="">{selectedButton.element}</div>
    </div>
  );
}

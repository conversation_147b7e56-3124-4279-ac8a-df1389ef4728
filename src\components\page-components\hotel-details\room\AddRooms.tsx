/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactElement, useContext, useState } from "react";
import AddRoom from "./AddRoom";
import { useParams } from "react-router-dom";
import { handleAddRoomsApi } from "@/utils/api-functions/addRooms";
import AppContext from "@/utils/context/AppContext";
import { PlusCircle, Save } from "lucide-react";

export default function AddRooms() {
  const { id } = useParams();
  const { roomData }: any = useContext(AppContext);
  const [addedRooms, setAddedRooms] = useState<ReactElement[]>([
    <AddRoom />
  ]);
  const [isDisabled, setDisabled] = useState(false);
  
  function handleAddRoooms() {
    setAddedRooms([
      <AddRoom />,
      ...addedRooms,
    ]);
  }
  
  async function handleSubmitData() {
    setDisabled(true);
    try {
      await handleAddRoomsApi(id as string, roomData);
      window.location.reload();
    } catch (error) {
      setDisabled(false);
    }
  }
  
  return (
    <div className="space-y-4 max-w-7xl mx-auto px-4 py-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-6">Add Room Details</h2>
        
        {/* Room forms */}
        <div className="space-y-4">
          {addedRooms?.map((room, index) => (
            <div key={index}>{room}</div>
          ))}
        </div>
        
        {/* Action buttons */}
        <div className="mt-8 flex flex-wrap gap-4">
          <button
            className="flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            onClick={handleAddRoooms}
          >
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Another Room
          </button>

          {roomData?.length > 0 && (
            <button
              onClick={handleSubmitData}
              disabled={isDisabled}
              className={`flex items-center px-4 py-2 rounded-md shadow-sm text-sm font-medium transition-colors ${
                !isDisabled 
                  ? "bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" 
                  : "bg-blue-400 text-white cursor-not-allowed"
              }`}
            >
              <Save className="h-4 w-4 mr-2" />
              {isDisabled ? "Saving..." : "Save All Rooms"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

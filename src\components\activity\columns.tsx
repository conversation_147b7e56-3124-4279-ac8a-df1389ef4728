import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {  ArrowUpDown, Eye, MoreHorizontal, Pencil, Trash } from "lucide-react";
import { ActivityType, ParticipantInfo } from "@/types/types"; // Adjust the import path as necessary
import { Link } from "react-router-dom";




export const columns: ColumnDef<ActivityType>[] = [
    {
        accessorKey: "name",
        header: () => {
            return (
                <h1 className="text-center">
                    Activity Name
                </h1>
            );
        },
        cell: ({ row }) => {
            const activityName: string = row.getValue("name");

            return (
                <div className="text-center">
                    {activityName}
                </div>
            );
        }
    },
    {
        accessorKey: "description",
        header: () => {
            return (
                <h1>
                    Description
                </h1>
            );
        },
        cell: ({ row }) => {
            const description: string = row.getValue("description");

            return (
                <div>
                    {description}
                </div>
            );
        }
    },
    {
        accessorKey: "destinationId",
        header: () => {
            return (
                <h1>
                    Destination
                </h1>
            );
        },
        cell: ({ row }) => {
            const destinationId: string = row.getValue("destinationId");
            return (
                <div>
                    {destinationId}
                </div>
            );
        }
    },
    {
        accessorKey: "dayType",
        header: () => {
            return (
                <h1>
                    Day Type
                </h1>
            );
        },
        cell: ({ row }) => {
            const dayType: number = row.getValue("dayType");
            
            return (
                <div>
                    {dayType}
                </div>
            );
        }
    },
    {
        accessorKey: "participantInfo",
        header: () => {
            return (
                <h1>
                    Participant Info
                </h1>
            );
        },
        cell: ({ row }) => {
            const participantInfo: ParticipantInfo = row.getValue("participantInfo");
            
            return (
                <div>
                    Min: {participantInfo.minParticipants}, Max: {participantInfo.maxParticipants}, Age Restriction: {participantInfo.ageRestriction}
                </div>
            );
        }
    },
    {
        accessorKey: "price",
        header: ({ column }) => {
            return (
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              >
                Price
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </Button>
            )
          },
        cell: ({ row }) => {
            const price: number = row.getValue("price");
            
            return (
                <div>
                    {price.toFixed(2)}
                </div>
            );
        }
    },
    {
        accessorKey: "duration",
        header: ({ column }) => {
            return (
              <Button
                variant="ghost"
                onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              >
                Duration
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </Button>
            )
          },
    },

    {
        accessorKey: 'Actions',
        header: () => <h1>Actions</h1>,
        cell: ({ row }) => {

            

            const activityId = row.original.activityId;

            const handleEditClick = () => {
                
               
            };

            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant={"ghost"} className="h-4 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <Link to={`/activities/${activityId}`}>
                            <DropdownMenuItem className="flex items-center gap-2">
                                <Eye className="h-4 w-4 mr-2 text-green-500" />
                                View
                            </DropdownMenuItem>
                        </Link>
                        <Link to={`/activity/${activityId}/edit`} onClick={handleEditClick}>
                            <DropdownMenuItem className="flex items-center gap-2">
                                <Pencil className="h-4 w-4 mr-2 text-slate-700" />
                                Edit
                            </DropdownMenuItem>
                        </Link>
                        <DropdownMenuItem className="flex items-center gap-2">
                            <Trash className="h-4 w-4 mr-2 text-red-500" />
                            {/* Add your delete logic here */}
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        }
    }
];

export function getActivityColumns(destinationMap: Record<string, string>): ColumnDef<ActivityType>[] {
    return [
        {
            accessorKey: "name",
            header: () => {
                return (
                    <h1 className="text-center">
                        Activity Name
                    </h1>
                );
            },
            cell: ({ row }) => {
                const activityName: string = row.getValue("name");
                return (
                    <div className="text-center">
                        {activityName}
                    </div>
                );
            }
        },
        {
            accessorKey: "description",
            header: () => {
                return (
                    <h1>
                        Description
                    </h1>
                );
            },
            cell: ({ row }) => {
                const description: string = row.getValue("description");
                return (
                    <div>
                        {description}
                    </div>
                );
            }
        },
        {
            accessorKey: "destinationId",
            header: () => {
                return (
                    <h1>
                        Destination
                    </h1>
                );
            },
            cell: ({ row }) => {
                const destinationId: string = row.getValue("destinationId");
                const destinationName = destinationMap[destinationId] || destinationId;
                return (
                    <div>
                        {destinationName}
                    </div>
                );
            }
        },
        {
            accessorKey: "dayType",
            header: () => {
                return (
                    <h1>
                        Day Type
                    </h1>
                );
            },
            cell: ({ row }) => {
                const dayType: number = row.getValue("dayType");
                
                return (
                    <div>
                        {dayType}
                    </div>
                );
            }
        },
        {
            accessorKey: "participantInfo",
            header: () => {
                return (
                    <h1>
                        Participant Info
                    </h1>
                );
            },
            cell: ({ row }) => {
                const participantInfo: ParticipantInfo = row.getValue("participantInfo");
                
                return (
                    <div>
                        Min: {participantInfo.minParticipants}, Max: {participantInfo.maxParticipants}, Age Restriction: {participantInfo.ageRestriction}
                    </div>
                );
            }
        },
        {
            accessorKey: "price",
            header: ({ column }) => {
                return (
                  <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                  >
                    Price
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                )
              },
            cell: ({ row }) => {
                const price: number = row.getValue("price");
                
                return (
                    <div>
                        {price.toFixed(2)}
                    </div>
                );
            }
        },
        {
            accessorKey: "duration",
            header: ({ column }) => {
                return (
                  <Button
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                  >
                    Duration
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                )
              },
        },
        {
            accessorKey: 'Actions',
            header: () => <h1>Actions</h1>,
            cell: ({ row }) => {

                

                const activityId = row.original.activityId;

                const handleEditClick = () => {
                    
                   
                };

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant={"ghost"} className="h-4 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <Link to={`/activities/${activityId}`}>
                                <DropdownMenuItem className="flex items-center gap-2">
                                    <Eye className="h-4 w-4 mr-2 text-green-500" />
                                    View
                                </DropdownMenuItem>
                            </Link>
                            <Link to={`/activity/${activityId}/edit`} onClick={handleEditClick}>
                                <DropdownMenuItem className="flex items-center gap-2">
                                    <Pencil className="h-4 w-4 mr-2 text-slate-700" />
                                    Edit
                                </DropdownMenuItem>
                            </Link>
                            <DropdownMenuItem className="flex items-center gap-2">
                                <Trash className="h-4 w-4 mr-2 text-red-500" />
                                {/* Add your delete logic here */}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            }
        }
    ];
}

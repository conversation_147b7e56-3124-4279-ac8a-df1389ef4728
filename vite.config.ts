import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({

  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port: 3003,
    strictPort: false, // Allow fallback to next available port
    allowedHosts: ['admin.tripxplo.com']
  },
  build: {

    chunkSizeWarningLimit: 1000,
  },
  resolve: {
    alias: {
      '@': '/src',
    },
  },
})

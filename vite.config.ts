import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port: 3003,
    strictPort: false, // Allow fallback to next available port
    allowedHosts: ['admin.tripxplo.com']
  },
  build: {
    chunkSizeWarningLimit: 1000,
  },
  resolve: {
    alias: {
      '@': '/src',
    },
  },
  define: {
    // Define production environment variables
    'import.meta.env.VITE_API_SERVER_URL': JSON.stringify(process.env.VITE_API_SERVER_URL || 'https://api.tripxplo.com/v1/api/'),
    'import.meta.env.VITE_API_URL': JSON.stringify(process.env.VITE_API_URL || 'https://api.tripxplo.com/v1'),
    'import.meta.env.VITE_FRONTEND_URL': JSON.stringify(process.env.VITE_FRONTEND_URL || 'https://admin.tripxplo.com'),
    'import.meta.env.VITE_TARIFF_API_URL': JSON.stringify(process.env.VITE_TARIFF_API_URL || 'https://api.tripxplo.com/v1'),
    'import.meta.env.VITE_LINODE_STORAGE_URL': JSON.stringify(process.env.VITE_LINODE_STORAGE_URL || 'https://tripemilestone.in-maa-1.linodeobjects.com'),
  }
})

import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({

  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port : 3001,
    allowedHosts: ['admin.tripxplo.com'],
    proxy: {
      '/api': {
        target: 'https://api.tripxplo.com/v1',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      }
    }
  },
  build: {

    chunkSizeWarningLimit: 1000,  
  },
  resolve: {
    alias: {
      '@': '/src',
    },
  },
})

import { useEffect, useState } from "react";
import { RoomData } from "../AddRoom";
import { useParams } from "react-router-dom";
import { fetchAllRooms } from "@/utils/api-functions/fetch-rooms";
import RoomRow from "./RoomRow";
import { ArrowDown, ArrowUp, Loader2, BedDouble } from "lucide-react";

type SortKey = "hotelRoomType" | "hotelName" | "isAc" | "roomCapacity";
type SortDirection = "asc" | "desc";

export default function AllRooms() {
  const { id } = useParams();
  const [rooms, setRooms] = useState<RoomData[]>([]);
  const [sortKey, setSortKey] = useState<SortKey>("hotelRoomType");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRooms = async () => {
      if (id != undefined) {
        setIsLoading(true);
        setError(null);
        
        try {
          const roomsData: RoomData[] = await fetchAllRooms(id);
          setRooms(roomsData);
        } catch (err) {
          console.error("Error fetching rooms:", err);
          setError("Failed to load rooms");
        } finally {
          setIsLoading(false);
        }
      }
    };
    fetchRooms();
  }, [id]);

  // Handle sort when clicking a column header
  const handleSort = (key: SortKey) => {
    if (sortKey === key) {
      // Toggle direction if clicking the same column
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new sort column and default to ascending
      setSortKey(key);
      setSortDirection("asc");
    }
  };

  // Sort rooms based on current sort state
  const sortedRooms = [...rooms].sort((a, b) => {
    // Handle different data types accordingly
    let valueA, valueB;

    if (sortKey === "roomCapacity") {
      valueA = a[sortKey];
      valueB = b[sortKey];
    } else if (sortKey === "isAc") {
      valueA = a[sortKey] ? "AC" : "Non-AC";
      valueB = b[sortKey] ? "AC" : "Non-AC";
    } else {
      valueA = a[sortKey]?.toString().toLowerCase();
      valueB = b[sortKey]?.toString().toLowerCase();
    }

    // Sort based on direction
    if (sortDirection === "asc") {
      return valueA > valueB ? 1 : -1;
    } else {
      return valueA < valueB ? 1 : -1;
    }
  });

  // Render sort indicator
  const renderSortIndicator = (key: SortKey) => {
    if (sortKey !== key) return null;
    return sortDirection === "asc" ? (
      <ArrowUp className="inline-block ml-1 h-4 w-4" />
    ) : (
      <ArrowDown className="inline-block ml-1 h-4 w-4" />
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="border border-gray-200 shadow-sm rounded-lg p-8">
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-10 w-10 animate-spin text-blue-600 mb-4" />
          <p className="text-gray-600">Loading rooms...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="border border-gray-200 shadow-sm rounded-lg p-8">
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="text-red-500 mb-4">
            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{error}</h3>
          <p className="text-gray-500">Please try again later or contact support.</p>
        </div>
      </div>
    );
  }

  // Empty state (no rooms)
  if (rooms.length === 0) {
    return (
      <div className="border border-gray-200 shadow-sm rounded-lg p-8">
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <BedDouble className="h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No rooms found for this hotel</h3>
          <p className="text-gray-500 max-w-md mx-auto mb-6">
            This hotel doesn't have any rooms added yet. You can add rooms using the "Add Rooms" tab.
          </p>
        </div>
      </div>
    );
  }

  // Table view with rooms
  return (
    <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => handleSort("hotelRoomType")}
            >
              <div className="flex items-center">
                Room Name
                {renderSortIndicator("hotelRoomType")}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => handleSort("hotelName")}
            >
              <div className="flex items-center">
                Hotel Name
                {renderSortIndicator("hotelName")}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => handleSort("isAc")}
            >
              <div className="flex items-center">
                Room Type
                {renderSortIndicator("isAc")}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => handleSort("roomCapacity")}
            >
              <div className="flex items-center">
                Room Capacity
                {renderSortIndicator("roomCapacity")}
              </div>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {sortedRooms.map((room: RoomData) => (
            <RoomRow key={room.hotelRoomId} data={room} />
          ))}
        </tbody>
      </table>
    </div>
  );
}

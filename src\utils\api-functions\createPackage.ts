import { PackageType } from "@/types/types";
import toast from "react-hot-toast";
import api from './auth'

export async function createPackage( data : PackageType) {
    try {
        // Debug log the data being sent
        console.log("Creating package with data:", JSON.stringify(data, null, 2));

        // Log the API URL
        console.log("API URL for package creation:", api.defaults.baseURL + "/admin/package");

        const response = await api.post(
            "admin/package",
          data
        );
        console.log("Package creation response:", response);
        return Promise.resolve(response);
      } catch (error: any) {
        console.error('Create Package Error:', error);

        // Extract more detailed error message if available
        const errorMessage = error.response?.data?.message ||
                            error.response?.data?.error ||
                            error.message ||
                            'An error occurred while creating the package';

        // Log the error details
        console.error('Error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message
        });

        toast.error(errorMessage);
        return Promise.reject(errorMessage);
      }
}
import { PackageType } from "@/types/types";
import toast from "react-hot-toast";
import api from './auth'

export async function createPackage( data : PackageType) {
    try {
        const response = await api.post(
            "admin/package",
          data
        );
        return Promise.resolve(response);
      } catch (error) {
        toast.error('An Error occurred');
        return Promise.reject('error');
      }
}
#!/usr/bin/env python3
"""
Date Range Extractor for Tariff Extraction

This module provides enhanced date range extraction functionality,
with improved handling of various date formats and better detection
of validity periods in headers, footers, and table cells.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger('tariff_extractor')

class DateRangeExtractor:
    """Enhanced date range extractor with improved detection capabilities"""
    
    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the date range extractor
        
        Args:
            config: Optional configuration object
        """
        self.config = config
        
        # Default patterns for date extraction
        self.patterns = {
            # Pattern for text like "Tariff Validity: 1st October 2024 to 31st May 2025"
            'validity_period': r'(?:tariff|rate|price|room)\s+validity\s*:?\s*(\d+(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})\s+(?:to|till|until|through|-)\s+(\d+(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})',
            
            # Pattern for text like "Valid from: 01/04/2025 to 30/06/2025"
            'valid_from_to': r'(?:valid|applicable)(?:\s+from)?:?\s*(\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4})\s*(?:to|till|until|through|-)\s*(\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4})',
            
            # Pattern for text like "1st April 2025 to 9th June 2025"
            'ordinal_date_range': r'(\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})\s+(?:to|till|until|through|-)\s+(\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})',
            
            # Pattern for text like "01/10/2024 - 31/05/2025"
            'numeric_date_range': r'(\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4})\s*(?:-|to|till|until|through)\s*(\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4})',
            
            # Pattern for text like "Season 2024-25" or "Tariff 2024-2025"
            'season_year': r'(?:season|year|tariff|rate)\s+(?:for\s+)?\s*(\d{4})[-/](?:\d{2}|(\d{4}))',
            
            # Pattern for text like "April - June 2025"
            'month_range_year': r'((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December))\s*(?:-|to|till|until|through)\s*((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December))\s+(\d{4})',
            
            # Pattern for text like "Summer Season" or "Winter Season"
            'season_name': r'(summer|winter|monsoon|peak|off-peak|shoulder)\s+(?:season|period)',
            
            # Pattern for header/footer with date range
            'header_date_range': r'^.*?(?:valid|applicable|tariff|rate card).*?(\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4})\s*(?:to|till|until|through|-)\s*(\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4}).*?$',
            
            # Pattern for weekday/weekend specification
            'weekday_weekend': r'(weekday|weekend|monday|tuesday|wednesday|thursday|friday|saturday|sunday)(?:\s+only)?',
            
            # Pattern for blackout dates
            'blackout_dates': r'(?:blackout|excluded|not\s+valid|except)(?:\s+dates)?:?\s*((?:\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4}(?:\s*,\s*)?)+)'
        }
        
        # Load patterns from config if available
        if config and hasattr(config, 'get_pattern_string'):
            for pattern_name, pattern in config.get_full_config().get('date_patterns', {}).items():
                self.patterns[pattern_name] = pattern
        
        # Compile regex patterns for better performance
        self.compiled_patterns = {
            name: re.compile(pattern, re.IGNORECASE) for name, pattern in self.patterns.items()
        }
        
        # Statistics for extraction operations
        self.extraction_stats = {
            'total_texts_processed': 0,
            'date_ranges_found': 0,
            'header_date_ranges': 0,
            'table_date_ranges': 0,
            'season_year_ranges': 0,
            'ordinal_date_ranges': 0,
            'numeric_date_ranges': 0,
            'month_range_year_ranges': 0,
            'weekday_weekend_specs': 0,
            'blackout_dates_found': 0
        }
    
    def extract_date_ranges(self, text: str, source: str = "text") -> List[Dict[str, Any]]:
        """
        Extract date ranges from text with enhanced detection
        
        Args:
            text: Text to extract date ranges from
            source: Source of the text (e.g., "text", "header", "footer", "table")
            
        Returns:
            List of dictionaries containing date range information
        """
        if not text:
            return []
        
        self.extraction_stats['total_texts_processed'] += 1
        date_ranges = []
        
        # Process text based on source for targeted extraction
        if source == "header" or source == "footer":
            # Headers and footers often contain global validity periods
            # Focus on patterns that indicate overall validity
            header_patterns = ['validity_period', 'valid_from_to', 'header_date_range', 'season_year']
            for pattern_name in header_patterns:
                pattern = self.compiled_patterns.get(pattern_name)
                if not pattern:
                    continue
                
                matches = pattern.findall(text)
                if matches:
                    logger.debug(f"Found date range in {source} using {pattern_name} pattern: {matches}")
                    
                    for match in matches:
                        date_range = self._process_match(match, pattern_name)
                        if date_range:
                            date_range['source'] = source
                            date_range['is_global'] = True  # Header/footer dates are usually global
                            date_range['confidence'] = 0.9  # High confidence for header/footer dates
                            date_ranges.append(date_range)
                            self.extraction_stats['header_date_ranges'] += 1
        
        elif source == "table":
            # Tables often contain specific date ranges for specific rates
            # Focus on patterns that are likely to appear in table cells
            table_patterns = ['ordinal_date_range', 'numeric_date_range', 'month_range_year']
            for pattern_name in table_patterns:
                pattern = self.compiled_patterns.get(pattern_name)
                if not pattern:
                    continue
                
                matches = pattern.findall(text)
                if matches:
                    logger.debug(f"Found date range in table using {pattern_name} pattern: {matches}")
                    
                    for match in matches:
                        date_range = self._process_match(match, pattern_name)
                        if date_range:
                            date_range['source'] = source
                            date_range['is_global'] = False  # Table dates are usually specific
                            date_range['confidence'] = 0.8  # Good confidence for table dates
                            date_ranges.append(date_range)
                            self.extraction_stats['table_date_ranges'] += 1
        
        else:  # General text
            # Process all patterns for general text
            for pattern_name, pattern in self.compiled_patterns.items():
                matches = pattern.findall(text)
                if matches:
                    logger.debug(f"Found date range in text using {pattern_name} pattern: {matches}")
                    
                    for match in matches:
                        date_range = self._process_match(match, pattern_name)
                        if date_range:
                            date_range['source'] = source
                            date_range['is_global'] = pattern_name in ['validity_period', 'valid_from_to', 'header_date_range']
                            date_range['confidence'] = 0.7  # Moderate confidence for general text
                            date_ranges.append(date_range)
                            
                            # Update statistics
                            self.extraction_stats['date_ranges_found'] += 1
                            if pattern_name == 'season_year':
                                self.extraction_stats['season_year_ranges'] += 1
                            elif pattern_name == 'ordinal_date_range':
                                self.extraction_stats['ordinal_date_ranges'] += 1
                            elif pattern_name == 'numeric_date_range':
                                self.extraction_stats['numeric_date_ranges'] += 1
                            elif pattern_name == 'month_range_year':
                                self.extraction_stats['month_range_year_ranges'] += 1
        
        # Check for weekday/weekend specifications
        weekday_weekend_matches = self.compiled_patterns['weekday_weekend'].findall(text)
        if weekday_weekend_matches:
            logger.debug(f"Found weekday/weekend specifications: {weekday_weekend_matches}")
            
            # Apply to all date ranges if found
            for date_range in date_ranges:
                if 'weekday' in ' '.join(weekday_weekend_matches).lower():
                    date_range['weekday_only'] = True
                    date_range['weekend_only'] = False
                elif 'weekend' in ' '.join(weekday_weekend_matches).lower():
                    date_range['weekday_only'] = False
                    date_range['weekend_only'] = True
                
                self.extraction_stats['weekday_weekend_specs'] += 1
        
        # Check for blackout dates
        blackout_matches = self.compiled_patterns['blackout_dates'].findall(text)
        if blackout_matches:
            logger.debug(f"Found blackout dates: {blackout_matches}")
            
            # Create separate date ranges for blackout dates
            for blackout_text in blackout_matches:
                # Extract individual dates from comma-separated list
                blackout_dates = re.findall(r'\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4}', blackout_text)
                
                for blackout_date in blackout_dates:
                    parsed_date = self.parse_date(blackout_date)
                    if parsed_date:
                        # Create a single-day date range for the blackout date
                        date_ranges.append({
                            'start_date': parsed_date,
                            'end_date': parsed_date,
                            'is_global': False,
                            'is_blackout': True,
                            'weekday_only': False,
                            'weekend_only': False,
                            'confidence': 0.8,
                            'source': source
                        })
                        
                        self.extraction_stats['blackout_dates_found'] += 1
        
        # If no date ranges found and this is a header/footer, try more aggressive extraction
        if not date_ranges and (source == "header" or source == "footer"):
            # Look for any dates in the text
            date_pattern = re.compile(r'\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4}|\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}', re.IGNORECASE)
            dates = date_pattern.findall(text)
            
            if len(dates) >= 2:
                logger.debug(f"Found individual dates in {source}, pairing them: {dates}")
                
                # Parse and sort dates
                parsed_dates = []
                for date_str in dates:
                    parsed = self.parse_date(date_str)
                    if parsed:
                        parsed_dates.append(parsed)
                
                if len(parsed_dates) >= 2:
                    parsed_dates.sort()
                    
                    # Create date ranges from pairs of dates
                    for i in range(0, len(parsed_dates) - 1, 2):
                        date_ranges.append({
                            'start_date': parsed_dates[i],
                            'end_date': parsed_dates[i+1],
                            'is_global': True,
                            'is_blackout': False,
                            'weekday_only': False,
                            'weekend_only': False,
                            'confidence': 0.6,  # Lower confidence for paired dates
                            'source': source
                        })
                        
                        self.extraction_stats['date_ranges_found'] += 1
        
        # Sort date ranges by start date
        date_ranges.sort(key=lambda x: x['start_date'])
        
        return date_ranges
    
    def _process_match(self, match: Union[Tuple, str], pattern_name: str) -> Optional[Dict[str, Any]]:
        """
        Process a regex match into a standardized date range
        
        Args:
            match: Regex match (tuple or string)
            pattern_name: Name of the pattern that produced the match
            
        Returns:
            Dictionary containing date range information, or None if parsing failed
        """
        if pattern_name == 'validity_period' or pattern_name == 'valid_from_to' or pattern_name == 'ordinal_date_range' or pattern_name == 'numeric_date_range' or pattern_name == 'header_date_range':
            # These patterns have start and end date in groups 1 and 2
            if isinstance(match, tuple) and len(match) >= 2:
                start_date = self.parse_date(match[0])
                end_date = self.parse_date(match[1])
                
                if start_date and end_date:
                    return {
                        'start_date': start_date,
                        'end_date': end_date,
                        'is_global': False,
                        'is_blackout': False,
                        'weekday_only': False,
                        'weekend_only': False,
                        'confidence': 1.0
                    }
        
        elif pattern_name == 'season_year':
            # Season year pattern has start year in group 1 and optional end year in group 2
            if isinstance(match, tuple):
                start_year = match[0]
                end_year = match[1] if len(match) > 1 and match[1] else str(int(start_year) + 1)
                
                if len(end_year) == 2:
                    end_year = start_year[:2] + end_year
                
                # Assume standard financial/tourism year (April to March)
                start_date = f"{start_year}-04-01"
                end_date = f"{end_year}-03-31"
                
                return {
                    'start_date': start_date,
                    'end_date': end_date,
                    'is_global': True,
                    'is_blackout': False,
                    'weekday_only': False,
                    'weekend_only': False,
                    'confidence': 0.9
                }
        
        elif pattern_name == 'month_range_year':
            # Month range year pattern has start month in group 1, end month in group 2, and year in group 3
            if isinstance(match, tuple) and len(match) >= 3:
                start_month = match[0]
                end_month = match[1]
                year = match[2]
                
                # Convert month names to numbers
                month_map = {
                    'jan': 1, 'january': 1,
                    'feb': 2, 'february': 2,
                    'mar': 3, 'march': 3,
                    'apr': 4, 'april': 4,
                    'may': 5, 'may': 5,
                    'jun': 6, 'june': 6,
                    'jul': 7, 'july': 7,
                    'aug': 8, 'august': 8,
                    'sep': 9, 'september': 9,
                    'oct': 10, 'october': 10,
                    'nov': 11, 'november': 11,
                    'dec': 12, 'december': 12
                }
                
                start_month_num = month_map.get(start_month.lower(), 1)
                end_month_num = month_map.get(end_month.lower(), 12)
                
                # Handle year crossing (e.g., "October 2024 - March 2025")
                if start_month_num > end_month_num:
                    end_year = str(int(year) + 1)
                else:
                    end_year = year
                
                # Create date strings
                start_date = f"{year}-{start_month_num:02d}-01"
                
                # Set end date to last day of the month
                if end_month_num in [4, 6, 9, 11]:
                    end_day = 30
                elif end_month_num == 2:
                    # Simple leap year check
                    if int(end_year) % 4 == 0 and (int(end_year) % 100 != 0 or int(end_year) % 400 == 0):
                        end_day = 29
                    else:
                        end_day = 28
                else:
                    end_day = 31
                
                end_date = f"{end_year}-{end_month_num:02d}-{end_day:02d}"
                
                return {
                    'start_date': start_date,
                    'end_date': end_date,
                    'is_global': False,
                    'is_blackout': False,
                    'weekday_only': False,
                    'weekend_only': False,
                    'confidence': 0.8
                }
        
        elif pattern_name == 'season_name':
            # Season name pattern has season name in group 1
            if isinstance(match, str) or (isinstance(match, tuple) and len(match) >= 1):
                season_name = match[0] if isinstance(match, tuple) else match
                
                # Determine date range based on season name
                current_year = datetime.now().year
                
                if 'summer' in season_name.lower():
                    start_date = f"{current_year}-04-01"
                    end_date = f"{current_year}-06-30"
                elif 'monsoon' in season_name.lower():
                    start_date = f"{current_year}-07-01"
                    end_date = f"{current_year}-09-30"
                elif 'winter' in season_name.lower():
                    start_date = f"{current_year}-10-01"
                    end_date = f"{current_year + 1}-03-31"
                elif 'peak' in season_name.lower():
                    # Peak season typically includes major holidays
                    start_date = f"{current_year}-12-15"
                    end_date = f"{current_year + 1}-01-15"
                elif 'off-peak' in season_name.lower() or 'shoulder' in season_name.lower():
                    # Off-peak/shoulder seasons are typically between peak seasons
                    start_date = f"{current_year}-09-01"
                    end_date = f"{current_year}-11-30"
                else:
                    return None
                
                return {
                    'start_date': start_date,
                    'end_date': end_date,
                    'is_global': False,
                    'is_blackout': False,
                    'weekday_only': False,
                    'weekend_only': False,
                    'confidence': 0.6  # Lower confidence for season name inference
                }
        
        return None
    
    def parse_date(self, date_str: str) -> Optional[str]:
        """
        Parse date string into standard format (YYYY-MM-DD)
        
        Args:
            date_str: Date string to parse
            
        Returns:
            Parsed date in YYYY-MM-DD format, or None if parsing failed
        """
        if not date_str:
            return None
        
        # Clean the date string
        date_str = date_str.strip()
        
        # Handle special case for "1st April 2025" format
        ordinal_pattern = re.compile(r'(\d+)(?:st|nd|rd|th)?\s+([A-Za-z]+)\s+(\d{4})', re.IGNORECASE)
        match = ordinal_pattern.search(date_str)
        if match:
            day = int(match.group(1))
            month_name = match.group(2)
            year = int(match.group(3))
            
            # Convert month name to number
            try:
                month_map = {
                    'jan': 1, 'january': 1,
                    'feb': 2, 'february': 2,
                    'mar': 3, 'march': 3,
                    'apr': 4, 'april': 4,
                    'may': 5, 'may': 5,
                    'jun': 6, 'june': 6,
                    'jul': 7, 'july': 7,
                    'aug': 8, 'august': 8,
                    'sep': 9, 'september': 9,
                    'oct': 10, 'october': 10,
                    'nov': 11, 'november': 11,
                    'dec': 12, 'december': 12
                }
                
                month_num = month_map.get(month_name.lower())
                if month_num:
                    return f"{year:04d}-{month_num:02d}-{day:02d}"
            except (KeyError, ValueError):
                pass
        
        # Try different date formats
        date_formats = [
            '%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y',
            '%m/%d/%Y', '%m-%d-%Y', '%m.%d.%Y',
            '%d/%m/%y', '%d-%m-%y', '%d.%m.%y',
            '%m/%d/%y', '%m-%d-%y', '%m.%d.%y',
            '%Y/%m/%d', '%Y-%m-%d', '%Y.%m.%d'
        ]
        
        for fmt in date_formats:
            try:
                dt = datetime.strptime(date_str, fmt)
                
                # Handle 2-digit years
                if '%y' in fmt and dt.year < 2000:
                    # Assume 21st century for years less than current year
                    current_year = datetime.now().year % 100
                    if dt.year <= current_year:
                        dt = dt.replace(year=dt.year + 2000)
                    else:
                        dt = dt.replace(year=dt.year + 1900)
                
                return dt.strftime('%Y-%m-%d')
            except ValueError:
                continue
        
        # If all formats fail, return None
        return None
    
    def get_extraction_stats(self) -> Dict[str, int]:
        """
        Get statistics about extraction operations
        
        Returns:
            Dictionary of extraction statistics
        """
        return self.extraction_stats

import { MealPlan } from "@/types/types"

import MealPlanItem from "./MealPlanItem"


interface MealPlanListProps{
    roomId : string,
    mealPlan: MealPlan[]
}
const MealPlanList = ({roomId,mealPlan}:MealPlanListProps) => {
   
 
  return (
    <div className="flex flex-col gap-3 mt-3">
        {
           mealPlan?.length > 0 ? (
            <>
             {
                mealPlan?.map((mp)=>(

                    <MealPlanItem roomId={roomId}  key={mp._id} mealPlanId={mp.hotelMealId} mealPlanType={mp.mealPlan} mp={mp}/>
    
                ))
             }
            </>
           ): (
            <>
            <h1 className="text-muted-foreground text-sm">No meal plans</h1>
            </>
           )
        }
       
    </div>
  )
}

export default MealPlanList
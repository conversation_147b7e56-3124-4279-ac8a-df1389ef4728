/* eslint-disable @typescript-eslint/no-explicit-any */
import { DataTable } from '@/components/page-components/hotel-page/data-table'
import { columns } from '@/components/page-components/hotel-page/columns'
import { useQuery } from 'react-query'
import { fetchAllHotels } from '@/utils/api-functions/fetch-hotels'
import Loading from '@/components/package/Loading';


export interface Hotel {
  _id: string;
  hotelId: string;
  hotelName: string;
  location: {
    address: string;
    country: string;
    destinationId: string;
    lat: string;
    lon: string;
    state: string;
    _id: string;
  };
  amenities: string[];
  contract: {
    additionalEmail: string;
    businessEmail: string;
    maintainerPhoneNo: number;
  };
  viewPoint: string[];
  __v: number;
}

const Hotels = () => {
  

  const {data : Hotels,isLoading} = useQuery('Hotel Data',fetchAllHotels)
  


 



  return isLoading ? <Loading /> :(
    <>
      <div className="min-h-[91vh] flex flex-col justify-between bg-gradient-to-br from-blue-50 to-white">
        <div className="px-5 pb-24 max-w-7xl mx-auto w-full">
          <div className="flex items-center justify-between py-8">
            <h1 className="text-3xl font-bold text-blue-900 tracking-tight">Hotels</h1>
            {/* Add button or filter can go here if needed */}
          </div>
          <div className="bg-white rounded-2xl shadow-lg border border-blue-100 p-6">
            <DataTable noOfHotels={Hotels?.length} columns={columns} data={Hotels}/>
          </div>
        </div>
      </div>
    </>
  )
}

export default Hotels
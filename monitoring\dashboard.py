#!/usr/bin/env python3
"""
PDF Extraction Monitoring Dashboard

This script provides a web-based dashboard for monitoring the PDF extraction system.
It displays metrics, error rates, processing times, and system health.
"""

import os
import sys
import json
import time
import logging
import argparse
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.extraction_logger import get_extraction_metrics, ERROR_CATEGORIES

# Flask for web dashboard
try:
    from flask import Flask, render_template, jsonify, request, send_from_directory
except ImportError:
    print("Flask not installed. Install with: pip install flask")
    print("Dashboard will not be available.")
    sys.exit(1)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('extraction_dashboard')

# Constants
DASHBOARD_PORT = 5000
REFRESH_INTERVAL = 30  # seconds
STATIC_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static')
TEMPLATES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')

# Create directories if they don't exist
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(TEMPLATES_DIR, exist_ok=True)

# Create Flask app
app = Flask(__name__, 
            template_folder=TEMPLATES_DIR,
            static_folder=STATIC_DIR)

# In-memory cache for dashboard data
dashboard_data = {
    "metrics": {},
    "error_history": defaultdict(list),
    "processing_time_history": [],
    "success_rate_history": [],
    "last_updated": datetime.now(),
    "system_health": {
        "status": "healthy",
        "message": "System is operating normally",
        "cpu_usage": 0,
        "memory_usage": 0,
        "disk_usage": 0
    }
}

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='PDF Extraction Monitoring Dashboard')
    parser.add_argument('--port', type=int, default=DASHBOARD_PORT, help='Dashboard port')
    parser.add_argument('--refresh', type=int, default=REFRESH_INTERVAL, help='Refresh interval in seconds')
    parser.add_argument('--log-dir', default='logs', help='Log directory to monitor')
    return parser.parse_args()

def generate_dashboard_charts():
    """Generate charts for the dashboard"""
    try:
        metrics = dashboard_data["metrics"]
        
        # 1. Error distribution pie chart
        if metrics.get("errors_by_category"):
            plt.figure(figsize=(8, 8))
            labels = []
            sizes = []
            
            for category, count in metrics["errors_by_category"].items():
                if count > 0:
                    labels.append(ERROR_CATEGORIES.get(category, category))
                    sizes.append(count)
            
            if sizes:
                plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
                plt.axis('equal')
                plt.title('Errors by Category')
                plt.savefig(os.path.join(STATIC_DIR, 'error_distribution.png'))
                plt.close()
        
        # 2. Processing time histogram
        if metrics.get("processing_times"):
            plt.figure(figsize=(10, 6))
            plt.hist(metrics["processing_times"], bins=20)
            plt.xlabel('Processing Time (seconds)')
            plt.ylabel('Frequency')
            plt.title('Processing Time Distribution')
            plt.grid(True)
            plt.savefig(os.path.join(STATIC_DIR, 'processing_time_hist.png'))
            plt.close()
        
        # 3. Success rate over time
        if dashboard_data["success_rate_history"]:
            plt.figure(figsize=(10, 6))
            times = [entry[0] for entry in dashboard_data["success_rate_history"]]
            rates = [entry[1] for entry in dashboard_data["success_rate_history"]]
            
            plt.plot(times, rates)
            plt.xlabel('Time')
            plt.ylabel('Success Rate')
            plt.title('Extraction Success Rate Over Time')
            plt.ylim(0, 1)
            plt.grid(True)
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(os.path.join(STATIC_DIR, 'success_rate.png'))
            plt.close()
        
        # 4. Error rate by hotel (top 10)
        if metrics.get("errors_by_hotel"):
            plt.figure(figsize=(12, 6))
            
            # Sort hotels by error count and take top 10
            sorted_hotels = sorted(metrics["errors_by_hotel"].items(), 
                                  key=lambda x: x[1], reverse=True)[:10]
            
            hotels = [hotel for hotel, _ in sorted_hotels]
            counts = [count for _, count in sorted_hotels]
            
            plt.barh(hotels, counts)
            plt.xlabel('Error Count')
            plt.ylabel('Hotel')
            plt.title('Top 10 Hotels by Error Count')
            plt.grid(True)
            plt.tight_layout()
            plt.savefig(os.path.join(STATIC_DIR, 'hotel_errors.png'))
            plt.close()
        
        # 5. System health gauges
        plt.figure(figsize=(15, 5))
        
        # CPU usage gauge
        plt.subplot(1, 3, 1)
        cpu = dashboard_data["system_health"]["cpu_usage"]
        plt.pie([cpu, 100-cpu], colors=['red', 'lightgray'], startangle=90, 
               wedgeprops=dict(width=0.3))
        plt.text(0, 0, f"{cpu}%", ha='center', va='center', fontsize=20)
        plt.title('CPU Usage')
        
        # Memory usage gauge
        plt.subplot(1, 3, 2)
        memory = dashboard_data["system_health"]["memory_usage"]
        plt.pie([memory, 100-memory], colors=['orange', 'lightgray'], startangle=90, 
               wedgeprops=dict(width=0.3))
        plt.text(0, 0, f"{memory}%", ha='center', va='center', fontsize=20)
        plt.title('Memory Usage')
        
        # Disk usage gauge
        plt.subplot(1, 3, 3)
        disk = dashboard_data["system_health"]["disk_usage"]
        plt.pie([disk, 100-disk], colors=['blue', 'lightgray'], startangle=90, 
               wedgeprops=dict(width=0.3))
        plt.text(0, 0, f"{disk}%", ha='center', va='center', fontsize=20)
        plt.title('Disk Usage')
        
        plt.savefig(os.path.join(STATIC_DIR, 'system_health.png'))
        plt.close()
        
    except Exception as e:
        logger.error(f"Error generating dashboard charts: {str(e)}")

def update_dashboard_data():
    """Update dashboard data from metrics"""
    try:
        # Get current metrics
        metrics = get_extraction_metrics()
        dashboard_data["metrics"] = metrics
        
        # Update success rate history
        success_rate = metrics.get("success_rate", 0)
        dashboard_data["success_rate_history"].append((datetime.now(), success_rate))
        
        # Keep only last 24 hours of history
        cutoff_time = datetime.now() - timedelta(hours=24)
        dashboard_data["success_rate_history"] = [
            entry for entry in dashboard_data["success_rate_history"]
            if entry[0] >= cutoff_time
        ]
        
        # Update system health
        try:
            import psutil
            dashboard_data["system_health"]["cpu_usage"] = psutil.cpu_percent()
            dashboard_data["system_health"]["memory_usage"] = psutil.virtual_memory().percent
            dashboard_data["system_health"]["disk_usage"] = psutil.disk_usage('/').percent
            
            # Determine system health status
            if (dashboard_data["system_health"]["cpu_usage"] > 90 or
                dashboard_data["system_health"]["memory_usage"] > 90 or
                dashboard_data["system_health"]["disk_usage"] > 90):
                dashboard_data["system_health"]["status"] = "critical"
                dashboard_data["system_health"]["message"] = "System resources critically low"
            elif (dashboard_data["system_health"]["cpu_usage"] > 70 or
                 dashboard_data["system_health"]["memory_usage"] > 70 or
                 dashboard_data["system_health"]["disk_usage"] > 70):
                dashboard_data["system_health"]["status"] = "warning"
                dashboard_data["system_health"]["message"] = "System resources running low"
            else:
                dashboard_data["system_health"]["status"] = "healthy"
                dashboard_data["system_health"]["message"] = "System is operating normally"
        except ImportError:
            logger.warning("psutil not installed. System health monitoring disabled.")
        
        # Generate charts
        generate_dashboard_charts()
        
        # Update timestamp
        dashboard_data["last_updated"] = datetime.now()
        
    except Exception as e:
        logger.error(f"Error updating dashboard data: {str(e)}")

def dashboard_updater(refresh_interval):
    """Background thread to update dashboard data"""
    while True:
        try:
            update_dashboard_data()
        except Exception as e:
            logger.error(f"Error in dashboard updater: {str(e)}")
        
        time.sleep(refresh_interval)

# Create HTML template
def create_dashboard_template():
    """Create HTML template for the dashboard"""
    template = """<!DOCTYPE html>
<html>
<head>
    <title>PDF Extraction Monitoring Dashboard</title>
    <meta http-equiv="refresh" content="{{ refresh_interval }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #333;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .status-bar {
            display: flex;
            justify-content: space-between;
            background-color: #e9e9e9;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status-item {
            text-align: center;
            flex: 1;
        }
        .status-value {
            font-size: 24px;
            font-weight: bold;
        }
        .status-label {
            font-size: 14px;
            color: #666;
        }
        .chart-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .chart {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px;
            margin-bottom: 20px;
            width: calc(50% - 20px);
        }
        .chart img {
            width: 100%;
            height: auto;
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .system-health {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        .system-status {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .healthy {
            background-color: #4CAF50;
        }
        .warning {
            background-color: #FFC107;
        }
        .critical {
            background-color: #F44336;
        }
        .recent-errors {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 14px;
        }
        @media (max-width: 768px) {
            .chart {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>PDF Extraction Monitoring Dashboard</h1>
        <p>Last updated: {{ last_updated }}</p>
    </div>
    
    <div class="container">
        <div class="status-bar">
            <div class="status-item">
                <div class="status-value">{{ metrics.total_pdfs }}</div>
                <div class="status-label">Total PDFs</div>
            </div>
            <div class="status-item">
                <div class="status-value">{{ "%.1f"|format(metrics.success_rate * 100) }}%</div>
                <div class="status-label">Success Rate</div>
            </div>
            <div class="status-item">
                <div class="status-value">{{ "%.2f"|format(metrics.avg_processing_time) }}s</div>
                <div class="status-label">Avg Processing Time</div>
            </div>
            <div class="status-item">
                <div class="status-value">{{ metrics.failed_extractions }}</div>
                <div class="status-label">Failed Extractions</div>
            </div>
        </div>
        
        <div class="system-health">
            <div class="chart-title">System Health</div>
            <div class="system-status">
                <div class="status-indicator {{ system_health.status }}"></div>
                <div>{{ system_health.message }}</div>
            </div>
            <img src="{{ url_for('static', filename='system_health.png') }}" alt="System Health">
        </div>
        
        <div class="chart-container">
            <div class="chart">
                <div class="chart-title">Success Rate Over Time</div>
                <img src="{{ url_for('static', filename='success_rate.png') }}" alt="Success Rate">
            </div>
            <div class="chart">
                <div class="chart-title">Processing Time Distribution</div>
                <img src="{{ url_for('static', filename='processing_time_hist.png') }}" alt="Processing Time">
            </div>
            <div class="chart">
                <div class="chart-title">Errors by Category</div>
                <img src="{{ url_for('static', filename='error_distribution.png') }}" alt="Error Distribution">
            </div>
            <div class="chart">
                <div class="chart-title">Top 10 Hotels by Error Count</div>
                <img src="{{ url_for('static', filename='hotel_errors.png') }}" alt="Hotel Errors">
            </div>
        </div>
        
        <div class="recent-errors">
            <div class="chart-title">Recent Errors</div>
            <table>
                <tr>
                    <th>Time</th>
                    <th>Category</th>
                    <th>Severity</th>
                    <th>Hotel</th>
                    <th>Message</th>
                </tr>
                {% for error in recent_errors %}
                <tr>
                    <td>{{ error.timestamp }}</td>
                    <td>{{ error.category }}</td>
                    <td>{{ error.severity }}</td>
                    <td>{{ error.hotel_name }}</td>
                    <td>{{ error.message }}</td>
                </tr>
                {% endfor %}
            </table>
        </div>
    </div>
    
    <div class="footer">
        <p>Tripmilestone PDF Extraction Monitoring Dashboard</p>
        <p>Uptime: {{ uptime }}</p>
    </div>
</body>
</html>
"""
    
    # Write template to file
    with open(os.path.join(TEMPLATES_DIR, 'dashboard.html'), 'w') as f:
        f.write(template)

# Flask routes
@app.route('/')
def dashboard():
    """Render dashboard"""
    # Format uptime
    uptime_seconds = dashboard_data["metrics"].get("uptime_seconds", 0)
    days, remainder = divmod(uptime_seconds, 86400)
    hours, remainder = divmod(remainder, 3600)
    minutes, seconds = divmod(remainder, 60)
    uptime = f"{int(days)}d {int(hours)}h {int(minutes)}m {int(seconds)}s"
    
    # Get recent errors
    recent_errors = dashboard_data["metrics"].get("recent_errors", [])
    
    # Limit to 10 most recent errors
    recent_errors = sorted(recent_errors, key=lambda x: x.get("timestamp", ""), reverse=True)[:10]
    
    return render_template('dashboard.html',
                          metrics=dashboard_data["metrics"],
                          system_health=dashboard_data["system_health"],
                          last_updated=dashboard_data["last_updated"].strftime("%Y-%m-%d %H:%M:%S"),
                          refresh_interval=REFRESH_INTERVAL,
                          uptime=uptime,
                          recent_errors=recent_errors)

@app.route('/api/metrics')
def api_metrics():
    """API endpoint for metrics"""
    return jsonify(dashboard_data["metrics"])

@app.route('/api/health')
def api_health():
    """API endpoint for system health"""
    return jsonify(dashboard_data["system_health"])

def main():
    """Main function"""
    args = parse_args()
    
    # Create dashboard template
    create_dashboard_template()
    
    # Start updater thread
    updater_thread = threading.Thread(
        target=dashboard_updater,
        args=(args.refresh,),
        daemon=True
    )
    updater_thread.start()
    
    # Initial update
    update_dashboard_data()
    
    # Start Flask app
    logger.info(f"Starting dashboard on port {args.port}")
    app.run(host='0.0.0.0', port=args.port, debug=False)

if __name__ == "__main__":
    main()

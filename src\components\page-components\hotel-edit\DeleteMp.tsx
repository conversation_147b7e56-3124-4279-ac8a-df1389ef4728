import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { deleteMealPlan } from "@/utils/api-functions/hotel/meal-plan/delete-mealplan"
import { Trash } from "lucide-react"
import toast from "react-hot-toast"
import { useNavigate } from "react-router-dom"

interface DeleteMpProps {
    roomId : string
    mealPlanId : string
}

const DeleteMp = ({roomId, mealPlanId}: DeleteMpProps) => {
    const router = useNavigate();
    
    async function handleDelete(){
        try {
            const response = await deleteMealPlan(roomId, mealPlanId)
            if(response === true){
                toast.success('Meal plan deleted successfully')
                router(0)
            }
            else {
                toast.error('Error deleting Meal plan')
            }
        } catch (error) {
            console.log('[MEAL_PLAN DELETE ERROR]', error)
            toast.error('Error Deleting Meal Plan')
        }
    }

    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
            <Button 
                variant="outline" 
                size="sm" 
                className="flex items-center gap-1.5 text-red-600 border-red-200 hover:bg-red-50"
                onClick={(event) => event.stopPropagation()}
            >
                <Trash size={14}/> Delete
            </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
            <AlertDialogHeader>
                <AlertDialogTitle>
                    Are you sure?
                </AlertDialogTitle>
                <AlertDialogDescription>
                    This action cannot be undone
                </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
                <AlertDialogCancel>
                    Cancel
                </AlertDialogCancel>
                <AlertDialogAction 
                    onClick={handleDelete}
                    className="bg-red-600 hover:bg-red-700"
                >
                    Yes, Delete
                </AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    )
}

export default DeleteMp
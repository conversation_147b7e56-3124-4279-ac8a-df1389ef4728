import { ChangeEvent, useContext, useState } from "react";
import AddAmenities from "../../hotel-page/AddAmenities";
import toast from "react-hot-toast";
import AppContext from "@/utils/context/AppContext";
import { allRoomTypes } from "@/utils/constants/optionsData";
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-explicit-any */
export interface RoomData {
  hotelName: string;
  hotelRoomType: string;
  hotelRoomId: string;
  hotelId: string;
  maxAdult: number;
  maxChild: number;
  maxInf: number;
  roomCapacity: number;
  isAc: boolean;
  amenities: string[];
}

export interface Room {
  hotelRoomType: string;

  maxAdult: number;
  maxChild: number;
  maxInf: number;
  roomCapacity: number;
  isAc: boolean;
  amenities: string[];
}

export default function AddRoom() {
  const { handleRoomData, handleRoomDelete }: any = useContext(AppContext);
  const [roomType, setRoomType] = useState("");
  const [roomTypes, setRoomTypes] = useState(allRoomTypes)
  const [roomCapacity, setRoomCapacity] = useState(2);
  const [adultCapacity, setAdultCapacity] = useState(3)
  const [childCapacity, setChildCapacity] = useState(2)
  const [infantCapacity, setInfantCapacity] = useState(1);
  const [isAc, setIsAc] = useState<string>("")
  const [isSaved, setSaved] = useState(false);
  const [amenetiesId, setAmenetiesId] = useState<string[]>([]);
  const [view, setView] = useState(true);
  const [isInputFocused, setInputFocused] = useState(false);

  function handleSave() {
    let isAcVar: boolean;
    if (isAc == '1') {
      isAcVar = true;
    } else {
      isAcVar = false;
    }

    const roomData: Room = {
      hotelRoomType: roomType,
      maxAdult: adultCapacity,
      maxChild: childCapacity,
      maxInf: infantCapacity,
      roomCapacity: roomCapacity,
      isAc: isAcVar,
      amenities: amenetiesId,
    };
    if (
      roomType && roomCapacity && infantCapacity && adultCapacity && childCapacity && isAc &&
      amenetiesId?.length > 0
    ) {
      handleRoomData(roomData);
      setSaved(true);
    } else {
      toast.error("Enter all fields to save");
    }
  }
  
  function handleClose(roomType: string) {
    handleRoomDelete(roomType);
    setView(false);
  }

  function handleChangeRoonType(inp: string) {
    setInputFocused(true)
    const data = allRoomTypes.filter((k) => {
      return k.toLowerCase()?.includes(inp.toLowerCase())
    })
    setRoomTypes(data);
    setRoomType(inp)
  }
  
  function handleInputText(type: string) {
    setRoomType(type)
    setInputFocused(false)
  }

  // Input field styling
  const inputClass = "w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors";
  const labelClass = "block text-sm font-medium text-gray-700 mb-1";

  return view ? (
    !isSaved ? (
      <div className="bg-white border rounded-lg shadow-md p-6 my-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* First Column - Room Type & Capacity */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 border-b pb-2 mb-4">Room Basics</h3>
            
            <div className="space-y-4">
              <div className="relative">
                <label htmlFor="room-type" className={labelClass}>
                  Room Type
                </label>
                <input
                  type="text"
                  id="room-type"
                  placeholder="e.g Deluxe, Standard, Superior Room"
                  className={inputClass}
                  value={roomType}
                  onInput={(e: ChangeEvent<HTMLInputElement>) => handleChangeRoonType(e.target.value)}
                  onBlur={() => setInputFocused(false)}
                />
                {isInputFocused && roomTypes?.length > 0 && (
                  <div className="absolute z-40 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {roomTypes?.map((type) => (
                      <div 
                        key={type} 
                        onMouseDown={() => handleInputText(type)} 
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                      >
                        {type}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <label htmlFor="room-capacity" className={labelClass}>
                  Room Capacity
                </label>
                <input
                  type="number"
                  id="room-capacity"
                  placeholder="Total room capacity"
                  className={inputClass}
                  min={0}
                  value={roomCapacity}
                  onInput={(e: ChangeEvent<HTMLInputElement>) => setRoomCapacity(Number(e.target.value))}
                />
              </div>
            </div>
          </div>

          {/* Second Column - Capacity Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 border-b pb-2 mb-4">Occupancy Details</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="adult" className={labelClass}>
                  Max Adults
                </label>
                <input
                  type="number"
                  id="adult"
                  placeholder="Max adult capacity"
                  className={inputClass}
                  min={0}
                  value={adultCapacity}
                  onInput={(e: ChangeEvent<HTMLInputElement>) => setAdultCapacity(Number(e.target.value))}
                />
              </div>
              <div>
                <label htmlFor="child" className={labelClass}>
                  Max Children
                </label>
                <input
                  type="number"
                  id="child"
                  placeholder="Max child capacity"
                  className={inputClass}
                  min={0}
                  value={childCapacity}
                  onInput={(e: ChangeEvent<HTMLInputElement>) => setChildCapacity(Number(e.target.value))}
                />
              </div>
            </div>

            <div>
              <label htmlFor="infant" className={labelClass}>
                Max Infants
              </label>
              <input
                type="number"
                id="infant"
                placeholder="Max infant capacity"
                className={inputClass}
                min={0}
                value={infantCapacity}
                onInput={(e: ChangeEvent<HTMLInputElement>) => setInfantCapacity(Number(e.target.value))}
              />
            </div>

            <div className="pt-2">
              <fieldset className="border border-gray-300 rounded-md p-4">
                <legend className="text-sm font-medium text-gray-700 px-2">Room Type</legend>
                <div className="flex space-x-6">
                  <div className="flex items-center">
                    <input 
                      type="radio" 
                      id="isAc" 
                      name="isAc" 
                      value="1" 
                      onChange={(e: ChangeEvent<HTMLInputElement>) => setIsAc(e.target.value)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="isAc" className="ml-2 text-sm text-gray-700">AC</label>
                  </div>
                  <div className="flex items-center">
                    <input 
                      type="radio" 
                      id="isNonAc" 
                      name="isAc" 
                      value="0" 
                      onChange={(e: ChangeEvent<HTMLInputElement>) => setIsAc(e.target.value)} 
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="isNonAc" className="ml-2 text-sm text-gray-700">Non-AC</label>
                  </div>
                </div>
              </fieldset>
            </div>
          </div>

          {/* Third Column - Amenities */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 border-b pb-2 mb-4">Amenities</h3>
            <AddAmenities setAmenetiesId={setAmenetiesId} />
          </div>
        </div>

        {/* Save Button */}
        <div className="mt-8 flex justify-end">
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Save Room Details
          </button>
        </div>
      </div>
    ) : (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 my-4 flex justify-between items-center">
        <div className="flex space-x-8">
          <div className="text-sm">
            <span className="font-medium text-gray-700">Room Type:</span>
            <span className="ml-2 text-gray-600">{roomType}</span>
          </div>
          <div className="text-sm">
            <span className="font-medium text-gray-700">Room Capacity:</span>
            <span className="ml-2 text-gray-600">{roomCapacity}</span>
          </div>
        </div>
        <button
          onClick={() => handleClose(roomType)}
          className="p-1 bg-red-500 hover:bg-red-600 text-white rounded-full h-8 w-8 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
          aria-label="Remove room"
        >
          ×
        </button>
      </div>
    )
  ) : (
    <></>
  );
}

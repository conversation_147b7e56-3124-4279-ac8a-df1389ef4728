/**
 * Simple Express server for the Tripmilestone Admin Frontend
 * This server handles API requests and serves the frontend
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const bodyParser = require('body-parser');
const tariffRoutes = require('./routes/tariff');
const tariffExtractionRoutes = require('./routes/tariff-extraction');
const logger = require('./utils/logger');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'dist')));

// API Routes
app.use('/api/admin/hotel/tariff', tariffRoutes);
app.use('/api/admin/hotel/:hotelId/tariffs', (req, res, next) => {
  // Pass the request to the tariff routes
  // The route handler in tariff.js will handle the hotelId parameter
  tariffRoutes(req, res, next);
});

// Tariff extraction routes
app.use('/api/admin/hotel/tariff-extraction', tariffExtractionRoutes);

// Request logger middleware
app.use((req, res, next) => {
  logger.info(`[API] ${req.method} ${req.url}`);
  next();
});

// Serve the frontend for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  logger.info(`Server started on port ${PORT}`);
});

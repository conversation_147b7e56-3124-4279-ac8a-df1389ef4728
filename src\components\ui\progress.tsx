"use client";

import * as React from "react";

// Try to import Radix UI components
let ProgressPrimitive: any;
try {
  ProgressPrimitive = require("@radix-ui/react-progress");
} catch (error) {
  console.error("Failed to load @radix-ui/react-progress:", error);
  // We'll handle this case with a fallback implementation
}

import { cn } from "@/lib/utils";

// Simple fallback implementation if Radix UI fails to load
const FallbackProgress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { value?: number }
>(({ className, value = 0, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "relative h-4 w-full overflow-hidden rounded-full bg-gray-100",
      className
    )}
    {...props}
  >
    <div
      className="h-full bg-blue-600 transition-all"
      style={{ width: `${Math.max(0, Math.min(100, value))}%` }}
    />
  </div>
));
FallbackProgress.displayName = "FallbackProgress";

// Use Radix UI if available, otherwise use fallback
const Progress = ProgressPrimitive
  ? React.forwardRef<
      React.ElementRef<typeof ProgressPrimitive.Root>,
      React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
    >(({ className, value, ...props }, ref) => (
      <ProgressPrimitive.Root
        ref={ref}
        className={cn(
          "relative h-4 w-full overflow-hidden rounded-full bg-gray-100",
          className
        )}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className="h-full w-full flex-1 bg-blue-600 transition-all"
          style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
        />
      </ProgressPrimitive.Root>
    ))
  : FallbackProgress;

Progress.displayName = ProgressPrimitive ? ProgressPrimitive.Root.displayName : "Progress";

export { Progress }; 
# PDF Extraction Monitoring & Error Handling System

This document describes the monitoring, error handling, and logging system for the PDF tariff extraction process.

## Overview

The monitoring and error handling system consists of several components:

1. **Enhanced Logging**: Detailed logging with categorized errors and structured output
2. **Error Handling**: Robust error handling with categorization and severity levels
3. **Alerting**: <PERSON><PERSON> and <PERSON>lack alerts for critical issues
4. **Monitoring Dashboard**: Web-based dashboard for system health and performance metrics
5. **Batch Processing**: Parallel processing for efficient extraction of multiple PDFs

## Directory Structure

```
├── advanced_pdf_extractor.py       # Main extraction script with enhanced error handling
├── batch_extract.py                # Parallel batch processing script
├── run_monitoring.py               # Script to run the monitoring dashboard
├── utils/
│   └── extraction_logger.py        # Enhanced logging and error handling module
└── monitoring/
    └── dashboard.py                # Web-based monitoring dashboard
```

## Enhanced Logging

The enhanced logging system provides detailed information about the extraction process, including:

- Structured JSON logs for machine processing
- Categorized errors for easier troubleshooting
- Severity levels (INFO, WARNING, ERROR, CRITICAL)
- Context-aware logging with extraction details
- Performance metrics tracking

### Error Categories

The system categorizes errors into specific types:

- `PDF_ACCESS`: Issues accessing the PDF file
- `PDF_PARSING`: General PDF parsing errors
- `TABLE_EXTRACTION`: Issues extracting tables from the PDF
- `TEXT_EXTRACTION`: Issues extracting text from the PDF
- `DATE_PARSING`: Issues parsing dates
- `PRICE_EXTRACTION`: Issues extracting prices
- `MEAL_PLAN`: Issues with meal plan extraction
- `OCR`: Issues with OCR processing
- `NER`: Issues with Named Entity Recognition
- `VALIDATION`: Issues with data validation
- `MEMORY`: Memory-related errors
- `TIMEOUT`: Timeout errors
- `UNKNOWN`: Uncategorized errors

### Usage

```python
from utils.extraction_logger import ExtractionLogger, ExtractionError

# Create logger
logger = ExtractionLogger('my_logger', log_dir='logs')

# Log extraction start
context = logger.log_extraction_start(pdf_path='example.pdf', hotel_name='Example Hotel')

try:
    # Your extraction code here
    results = extract_data()
    
    # Log successful extraction
    logger.log_extraction_end(context=context, success=True, results=results)
except Exception as e:
    # Log error
    error = logger.log_error(
        message=f"Extraction failed: {str(e)}",
        category="PDF_PARSING",
        severity="ERROR",
        details={"pdf_path": "example.pdf"},
        context=context
    )
    
    # Log extraction failure
    logger.log_extraction_end(context=context, success=False, error=error)
```

## Error Handling

The system provides robust error handling with:

- Custom `ExtractionError` class with category and severity
- Detailed error information including stack traces
- Error handling decorator for consistent error handling
- Graceful degradation for non-critical errors

### Usage

```python
from utils.extraction_logger import ExtractionError, handle_extraction_errors

@handle_extraction_errors(logger)
def extract_data():
    try:
        # Your extraction code here
    except Exception as e:
        raise ExtractionError(
            message=f"Failed to extract data: {str(e)}",
            category="PDF_PARSING",
            severity="ERROR",
            details={"pdf_path": "example.pdf"}
        )
```

## Alerting System

The alerting system notifies administrators of critical issues:

- Email alerts via SMTP
- Slack alerts via webhooks
- Configurable alert thresholds
- Detailed error information in alerts

### Configuration

```python
from utils.extraction_logger import configure_alerting

# Configure email alerts
configure_alerting(
    enabled=True,
    email_config={
        "smtp_server": "smtp.example.com",
        "smtp_port": 587,
        "username": "<EMAIL>",
        "password": "password",
        "from_address": "<EMAIL>",
        "to_addresses": ["<EMAIL>"]
    },
    slack_config={
        "webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz"
    }
)
```

## Monitoring Dashboard

The monitoring dashboard provides real-time visibility into the extraction system:

- Success/failure rates
- Processing times
- Error distribution
- System health metrics
- Recent errors with details

### Running the Dashboard

```bash
python run_monitoring.py --port 5000
```

Then open your browser to http://localhost:5000

### Dashboard Features

- **Status Bar**: Shows key metrics like total PDFs processed, success rate, and average processing time
- **System Health**: Displays CPU, memory, and disk usage with status indicators
- **Charts**: Visual representation of success rates, processing times, and error distribution
- **Recent Errors**: Table of recent errors with details for troubleshooting

## Batch Processing

The batch processing system efficiently processes multiple PDFs in parallel:

- Multi-process execution for optimal performance
- Progress tracking and reporting
- Detailed summary reports
- Hotel name mapping for context-aware extraction

### Usage

```bash
python batch_extract.py --input-dir pdfs/ --output-dir results/ --processes 4
```

### Advanced Options

```bash
python batch_extract.py \
    --input-dir pdfs/ \
    --output-dir results/ \
    --processes 4 \
    --hotel-mapping hotel_mapping.json \
    --log-level INFO \
    --enable-alerts \
    --email-alerts \
    --smtp-server smtp.example.com \
    --smtp-port 587 \
    --smtp-user user \
    --smtp-password pass \
    --email-from <EMAIL> \
    --email-to <EMAIL>
```

## Performance Considerations

The system is designed for optimal performance with 600+ hotels:

- Parallel processing for batch extraction
- Memory usage optimization
- Efficient error handling without performance impact
- Minimal logging overhead

## Best Practices

1. **Regular Monitoring**: Check the dashboard regularly for system health and error patterns
2. **Alert Configuration**: Configure alerts for critical issues to ensure timely response
3. **Log Analysis**: Review logs periodically to identify common issues and improvement opportunities
4. **Batch Processing**: Use batch processing for large sets of PDFs to optimize performance
5. **Error Categorization**: Use error categories to prioritize and address issues

## Troubleshooting

### Common Issues

#### High Error Rate
- Check for common patterns in error categories
- Review recent errors in the dashboard
- Check if errors are concentrated in specific hotels or PDF formats

#### Performance Issues
- Monitor CPU and memory usage in the dashboard
- Adjust the number of parallel processes based on your hardware
- Consider splitting large batches into smaller ones

#### Alert Storms
- Adjust alert thresholds to reduce noise
- Group similar alerts to reduce volume
- Implement alert suppression for known issues

### Getting Help

For more information, contact the development team or refer to the main documentation.

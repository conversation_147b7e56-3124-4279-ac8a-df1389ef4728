#!/usr/bin/env python3
"""
Performance Profiling for PDF Extraction

This script profiles the performance of the PDF extraction process
to identify bottlenecks and optimization opportunities.
"""

import os
import sys
import time
import cProfile
import pstats
import io
import argparse
import logging
import json
import psutil
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any, Tuple
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from functools import partial

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from advanced_pdf_extractor import TariffExtractor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('profile_extractor')

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Profile PDF extraction performance')
    parser.add_argument('--pdf-dir', required=True, help='Directory containing PDF files')
    parser.add_argument('--output-dir', default='performance/results', help='Directory for output files')
    parser.add_argument('--parallel', action='store_true', help='Test parallel extraction')
    parser.add_argument('--processes', type=int, default=4, help='Number of processes for parallel extraction')
    parser.add_argument('--memory', action='store_true', help='Profile memory usage')
    parser.add_argument('--limit', type=int, help='Limit the number of PDFs to process')
    return parser.parse_args()

def profile_extraction(pdf_path: str) -> Tuple[Dict[str, Any], Dict[str, float]]:
    """Profile the extraction of a single PDF"""
    logger.info(f"Profiling extraction of {pdf_path}")
    
    # Initialize metrics
    metrics = {
        "pdf_path": pdf_path,
        "pdf_size_kb": os.path.getsize(pdf_path) / 1024,
        "total_time": 0,
        "extraction_time": 0,
        "text_extraction_time": 0,
        "table_extraction_time": 0,
        "date_extraction_time": 0,
        "ocr_time": 0,
        "ner_time": 0,
        "entries_count": 0,
        "memory_usage_mb": 0
    }
    
    # Track memory usage
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # Start profiling
    start_time = time.time()
    
    # Create profiler
    pr = cProfile.Profile()
    pr.enable()
    
    # Extract data
    extractor = TariffExtractor(pdf_path)
    
    # Time text and table extraction
    text_start = time.time()
    text, tables = extractor.extract_text_and_tables()
    text_end = time.time()
    metrics["text_extraction_time"] = text_end - text_start
    
    # Time date extraction
    date_start = time.time()
    date_ranges = extractor.extract_date_ranges(text)
    date_end = time.time()
    metrics["date_extraction_time"] = date_end - date_start
    
    # Time full extraction
    extraction_start = time.time()
    results = extractor.extract()
    extraction_end = time.time()
    metrics["extraction_time"] = extraction_end - extraction_start
    
    # Stop profiling
    pr.disable()
    metrics["total_time"] = time.time() - start_time
    metrics["entries_count"] = len(results)
    
    # Measure memory usage
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    metrics["memory_usage_mb"] = final_memory - initial_memory
    
    # Get profiling stats
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats(20)  # Print top 20 functions
    profile_stats = s.getvalue()
    
    # Extract time spent in key functions
    for line in profile_stats.split('\n'):
        if 'extract_with_ocr' in line:
            metrics["ocr_time"] = float(line.strip().split()[3])
        elif 'nlp(' in line and 'spacy' in line:
            metrics["ner_time"] = float(line.strip().split()[3])
    
    return results, metrics

def profile_batch(pdf_dir: str, output_dir: str, parallel: bool = False, processes: int = 4, 
                 memory_profile: bool = False, limit: int = None) -> List[Dict[str, Any]]:
    """Profile extraction for a batch of PDFs"""
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all PDF files
    pdf_files = []
    for root, _, files in os.walk(pdf_dir):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    if limit and len(pdf_files) > limit:
        pdf_files = pdf_files[:limit]
    
    logger.info(f"Found {len(pdf_files)} PDF files")
    
    all_metrics = []
    
    if parallel:
        logger.info(f"Running parallel extraction with {processes} processes")
        with ProcessPoolExecutor(max_workers=processes) as executor:
            results = list(executor.map(profile_extraction, pdf_files))
            for extracted_data, metrics in results:
                all_metrics.append(metrics)
                
                # Save extracted data
                output_file = os.path.join(output_dir, f"{os.path.basename(metrics['pdf_path'])}.json")
                with open(output_file, 'w') as f:
                    json.dump(extracted_data, f, indent=2)
    else:
        logger.info("Running sequential extraction")
        for pdf_file in pdf_files:
            extracted_data, metrics = profile_extraction(pdf_file)
            all_metrics.append(metrics)
            
            # Save extracted data
            output_file = os.path.join(output_dir, f"{os.path.basename(pdf_file)}.json")
            with open(output_file, 'w') as f:
                json.dump(extracted_data, f, indent=2)
    
    # Save metrics
    metrics_file = os.path.join(output_dir, "extraction_metrics.json")
    with open(metrics_file, 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    # Generate performance report
    generate_performance_report(all_metrics, output_dir)
    
    return all_metrics

def generate_performance_report(metrics: List[Dict[str, Any]], output_dir: str):
    """Generate performance report with charts"""
    logger.info("Generating performance report")
    
    # Create figures directory
    figures_dir = os.path.join(output_dir, "figures")
    os.makedirs(figures_dir, exist_ok=True)
    
    # Extract metrics for analysis
    pdf_sizes = [m["pdf_size_kb"] for m in metrics]
    total_times = [m["total_time"] for m in metrics]
    extraction_times = [m["extraction_time"] for m in metrics]
    text_extraction_times = [m["text_extraction_time"] for m in metrics]
    date_extraction_times = [m["date_extraction_time"] for m in metrics]
    ocr_times = [m.get("ocr_time", 0) for m in metrics]
    ner_times = [m.get("ner_time", 0) for m in metrics]
    entry_counts = [m["entries_count"] for m in metrics]
    memory_usages = [m["memory_usage_mb"] for m in metrics]
    
    # 1. PDF Size vs. Processing Time
    plt.figure(figsize=(10, 6))
    plt.scatter(pdf_sizes, total_times)
    plt.xlabel('PDF Size (KB)')
    plt.ylabel('Processing Time (s)')
    plt.title('PDF Size vs. Processing Time')
    plt.grid(True)
    plt.savefig(os.path.join(figures_dir, 'size_vs_time.png'))
    
    # 2. Time breakdown
    plt.figure(figsize=(12, 8))
    labels = ['Text Extraction', 'Date Extraction', 'OCR', 'NER', 'Other']
    
    # Calculate average time for each component
    avg_text = sum(text_extraction_times) / len(text_extraction_times)
    avg_date = sum(date_extraction_times) / len(date_extraction_times)
    avg_ocr = sum(ocr_times) / len(ocr_times)
    avg_ner = sum(ner_times) / len(ner_times)
    avg_total = sum(total_times) / len(total_times)
    avg_other = avg_total - avg_text - avg_date - avg_ocr - avg_ner
    
    sizes = [avg_text, avg_date, avg_ocr, avg_ner, avg_other]
    plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
    plt.axis('equal')
    plt.title('Average Time Breakdown')
    plt.savefig(os.path.join(figures_dir, 'time_breakdown.png'))
    
    # 3. Memory usage vs. PDF size
    plt.figure(figsize=(10, 6))
    plt.scatter(pdf_sizes, memory_usages)
    plt.xlabel('PDF Size (KB)')
    plt.ylabel('Memory Usage (MB)')
    plt.title('PDF Size vs. Memory Usage')
    plt.grid(True)
    plt.savefig(os.path.join(figures_dir, 'memory_usage.png'))
    
    # 4. Processing time per entry
    time_per_entry = []
    for i in range(len(metrics)):
        if entry_counts[i] > 0:
            time_per_entry.append(total_times[i] / entry_counts[i])
        else:
            time_per_entry.append(0)
    
    plt.figure(figsize=(10, 6))
    plt.bar(range(len(metrics)), time_per_entry)
    plt.xlabel('PDF Index')
    plt.ylabel('Time per Entry (s)')
    plt.title('Processing Time per Entry')
    plt.grid(True)
    plt.savefig(os.path.join(figures_dir, 'time_per_entry.png'))
    
    # Generate HTML report
    html_report = os.path.join(output_dir, "performance_report.html")
    with open(html_report, 'w') as f:
        f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>PDF Extraction Performance Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2, h3 {{ color: #333; }}
        .summary {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        .chart {{ margin: 20px 0; }}
    </style>
</head>
<body>
    <h1>PDF Extraction Performance Report</h1>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>Total PDFs processed: {len(metrics)}</p>
        <p>Average processing time: {avg_total:.2f} seconds</p>
        <p>Average memory usage: {sum(memory_usages)/len(memory_usages):.2f} MB</p>
        <p>Total entries extracted: {sum(entry_counts)}</p>
    </div>
    
    <h2>Performance Charts</h2>
    
    <div class="chart">
        <h3>PDF Size vs. Processing Time</h3>
        <img src="figures/size_vs_time.png" alt="PDF Size vs. Processing Time" width="800">
    </div>
    
    <div class="chart">
        <h3>Average Time Breakdown</h3>
        <img src="figures/time_breakdown.png" alt="Time Breakdown" width="800">
    </div>
    
    <div class="chart">
        <h3>PDF Size vs. Memory Usage</h3>
        <img src="figures/memory_usage.png" alt="Memory Usage" width="800">
    </div>
    
    <div class="chart">
        <h3>Processing Time per Entry</h3>
        <img src="figures/time_per_entry.png" alt="Time per Entry" width="800">
    </div>
    
    <h2>Detailed Metrics</h2>
    
    <table>
        <tr>
            <th>PDF</th>
            <th>Size (KB)</th>
            <th>Total Time (s)</th>
            <th>Entries</th>
            <th>Memory (MB)</th>
        </tr>
""")
        
        for i, m in enumerate(metrics):
            f.write(f"""        <tr>
            <td>{os.path.basename(m["pdf_path"])}</td>
            <td>{m["pdf_size_kb"]:.2f}</td>
            <td>{m["total_time"]:.2f}</td>
            <td>{m["entries_count"]}</td>
            <td>{m["memory_usage_mb"]:.2f}</td>
        </tr>
""")
        
        f.write("""    </table>
</body>
</html>
""")
    
    logger.info(f"Performance report generated at {html_report}")

def main():
    """Main function"""
    args = parse_args()
    
    # Profile batch extraction
    profile_batch(
        args.pdf_dir, 
        args.output_dir, 
        parallel=args.parallel, 
        processes=args.processes,
        memory_profile=args.memory,
        limit=args.limit
    )

if __name__ == "__main__":
    main()

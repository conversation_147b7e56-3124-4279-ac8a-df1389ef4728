#!/usr/bin/env python3
"""
Helper script to download and test PDF extraction from a URL

Usage:
    python test_helper.py <pdf_url>

This script downloads a PDF from a URL, saves it locally, then tests the extraction.
"""

import sys
import os
import json
import logging
import requests
import urllib.parse
from extract_pdf_improved import extract_from_tables

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('extraction_test.log')
    ]
)
logger = logging.getLogger('tariff_helper')

def download_pdf(url):
    """Download a PDF from a URL and save it locally"""
    try:
        logger.info(f"Downloading PDF from URL: {url}")
        
        # Create a downloads directory if it doesn't exist
        os.makedirs('downloads', exist_ok=True)
        
        # Get filename from URL
        parsed_url = urllib.parse.urlparse(url)
        filename = os.path.basename(parsed_url.path)
        
        # Remove URL encoding from filename
        filename = urllib.parse.unquote(filename)
        
        # Save path
        save_path = os.path.join('downloads', filename)
        
        # Download the file
        response = requests.get(url, stream=True)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        # Save the file
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
                
        logger.info(f"PDF saved to: {save_path}")
        return save_path
        
    except Exception as e:
        logger.error(f"Error downloading PDF: {str(e)}")
        raise

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_helper.py <pdf_url>")
        sys.exit(1)

    pdf_url = sys.argv[1]
    
    try:
        # Download the PDF
        pdf_path = download_pdf(pdf_url)
        
        # Extract data
        print(f"\nExtracting data from {pdf_path}...")
        results = extract_from_tables(pdf_path)
        
        # Print results
        print("\n==== EXTRACTION RESULTS ====")
        print(f"Found {len(results)} tariff entries")
        print(json.dumps(results, indent=2))
        
        # Create a results directory if it doesn't exist
        os.makedirs('results', exist_ok=True)
        
        # Save results to JSON file
        output_filename = os.path.basename(pdf_path).split('.')[0] + '_results.json'
        output_path = os.path.join('results', output_filename)
        with open(output_path, 'w') as f:
            json.dump(results, indent=2, fp=f)
            
        print(f"\nResults saved to: {output_path}")
        print("Check extraction_test.log for detailed extraction logs.")
        
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 
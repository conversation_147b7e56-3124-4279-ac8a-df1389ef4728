import fpdf
import os

def create_test_pdf(output_path):
    """Create a simple test PDF with hotel tariff information"""
    pdf = fpdf.FPDF()
    pdf.add_page()
    
    # Set font
    pdf.set_font("Arial", "B", 16)
    
    # Title
    pdf.cell(0, 10, "Hotel Dewdrop Tariff 2024-25", 0, 1, "C")
    pdf.ln(10)
    
    # Validity period
    pdf.set_font("Arial", "", 12)
    pdf.cell(0, 10, "Validity: 01/10/2024 - 31/03/2025", 0, 1, "C")
    pdf.ln(10)
    
    # Table header
    pdf.set_font("Arial", "B", 12)
    pdf.cell(60, 10, "Room Type", 1, 0, "C")
    pdf.cell(40, 10, "CP Rate", 1, 0, "C")
    pdf.cell(40, 10, "MAP Rate", 1, 0, "C")
    pdf.cell(40, 10, "AP Rate", 1, 1, "C")
    
    # Table data
    pdf.set_font("Arial", "", 12)
    
    # Room types and rates
    room_data = [
        ["Standard Room", "Rs. 3500", "Rs. 4500", "Rs. 5500"],
        ["Deluxe Room", "Rs. 4500", "Rs. 5500", "Rs. 6500"],
        ["Suite", "Rs. 6000", "Rs. 7000", "Rs. 8000"],
        ["Mountain View", "Rs. 5000", "Rs. 6000", "Rs. 7000"]
    ]
    
    for row in room_data:
        pdf.cell(60, 10, row[0], 1, 0, "L")
        pdf.cell(40, 10, row[1], 1, 0, "C")
        pdf.cell(40, 10, row[2], 1, 0, "C")
        pdf.cell(40, 10, row[3], 1, 1, "C")
    
    # Extra person charges
    pdf.ln(10)
    pdf.set_font("Arial", "B", 12)
    pdf.cell(0, 10, "Extra Person Charges", 0, 1, "L")
    
    pdf.set_font("Arial", "", 12)
    pdf.cell(60, 10, "Extra Adult (CP)", 1, 0, "L")
    pdf.cell(40, 10, "Rs. 1000", 1, 1, "C")
    
    pdf.cell(60, 10, "Extra Adult (MAP)", 1, 0, "L")
    pdf.cell(40, 10, "Rs. 1500", 1, 1, "C")
    
    pdf.cell(60, 10, "Extra Adult (AP)", 1, 0, "L")
    pdf.cell(40, 10, "Rs. 2000", 1, 1, "C")
    
    # Save the PDF
    pdf.output(output_path)
    print(f"Test PDF created at {output_path}")

if __name__ == "__main__":
    output_path = "test_hotel_tariff.pdf"
    create_test_pdf(output_path)

[{"type": "missing_fields", "message": "Entry 108: Missing required fields: mealPlanType, roomPrice", "entry_index": 108, "missing_fields": ["mealPlanType", "roomPrice"], "severity": "error"}, {"type": "invalid_date_range", "message": "Entry 109: End date 2025-04-01 is before start date 2025-06-09", "entry_index": 109, "start_date": "2025-06-09", "end_date": "2025-04-01", "severity": "error"}, {"type": "short_date_range", "message": "Entry 109: Date range is only -69 days", "entry_index": 109, "days_diff": -69, "severity": "warning"}, {"type": "date_range_overlap", "message": "Overlap of 70 days between entries 4 and 110", "entry_indices": [4, 110], "overlap_days": 70, "severity": "error"}, {"type": "date_range_overlap", "message": "Overlap of 70 days between entries 20 and 111", "entry_indices": [20, 111], "overlap_days": 70, "severity": "error"}, {"type": "date_range_overlap", "message": "Overlap of 161 days between entries 38 and 113", "entry_indices": [38, 113], "overlap_days": 161, "severity": "error"}, {"type": "date_range_overlap", "message": "Overlap of 70 days between entries 56 and 112", "entry_indices": [56, 112], "overlap_days": 70, "severity": "error"}, {"type": "date_range_gap", "message": "Gap of 103 days between entries 80 and 81", "entry_indices": [80, 81], "gap_days": 103, "severity": "warning"}, {"type": "date_range_gap", "message": "Gap of 103 days between entries 83 and 84", "entry_indices": [83, 84], "gap_days": 103, "severity": "warning"}, {"type": "date_range_gap", "message": "Gap of 103 days between entries 86 and 87", "entry_indices": [86, 87], "gap_days": 103, "severity": "warning"}, {"type": "date_range_gap", "message": "Gap of 103 days between entries 89 and 90", "entry_indices": [89, 90], "gap_days": 103, "severity": "warning"}, {"type": "date_range_overlap", "message": "Overlap of 40 days between entries 96 and 115", "entry_indices": [96, 115], "overlap_days": 40, "severity": "error"}, {"type": "date_range_overlap", "message": "Overlap of 36 days between entries 115 and 97", "entry_indices": [115, 97], "overlap_days": 36, "severity": "error"}, {"type": "zero_price", "message": "Entry 108: Zero price", "entry_index": 108, "severity": "error"}, {"type": "inconsistent_price_hierarchy", "message": "Room type 'Deluxe Room' has lower price (4200.0) than 'Standard Room' (7200.0)", "room_types": ["Standard Room", "Deluxe Room"], "prices": [7200.0, 4200.0], "meal_plan": "cp", "severity": "warning"}, {"type": "inconsistent_meal_plans", "message": "Room type 'Problem Room' has different meal plans (, cp) than other room types", "room_type": "Problem Room", "meal_plans": ["", "cp"], "reference_meal_plans": ["ap", "ep", "cp", "map"], "severity": "warning"}, {"type": "inconsistent_meal_plans", "message": "Room type 'XYZ Special' has different meal plans (cp) than other room types", "room_type": "XYZ Special", "meal_plans": ["cp"], "reference_meal_plans": ["ap", "ep", "cp", "map"], "severity": "warning"}, {"type": "inconsistent_meal_plan_diff", "message": "Room type 'Villa' has unusual CP-MAP price difference (2600.0) compared to average (1751.1111111111113)", "room_type": "Villa", "diff": 2600.0, "avg_diff": 1751.1111111111113, "severity": "warning"}, {"type": "similar_room_types", "message": "Similar room types found: 'Super Deluxe Room' and 'Deluxe Room'", "room_types": ["Super Deluxe Room", "Deluxe Room"], "severity": "warning"}, {"type": "unusual_room_type", "message": "Unusual room type name: 'XYZ Special'", "room_type": "XYZ Special", "severity": "info"}]
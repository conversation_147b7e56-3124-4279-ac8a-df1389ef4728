/* eslint-disable @typescript-eslint/no-explicit-any */

import {  useState } from "react"
import toast from "react-hot-toast";
import { handleDate } from "@/components/page-components/hotel-details/room/mealPlan/handlingDates";

interface SelectDatesProp{
  setDates:(s:string,e:string)=>void
  deleteDates:(s:string,e:string)=>void
  period:{startDate:string,endDate:string}[],
}
export default function SelectPackageDates(props:SelectDatesProp) {
    const [startDate,setStartDate] = useState("")
    const [endDate,setEndDate] = useState("")
    const [checked,setChecked] = useState(false);

    function handleSave(){
        if(startDate && endDate){
            setChecked(true)
            props.setDates(startDate,endDate)
            setStartDate("")
            setEndDate("")
            setChecked(false)
        }
        else{
        toast.error("input dates to save")
    }
}
  return (
    <div className={"relative m-1 pt-4 border"}>
      {
            props.period?.length>0?props.period?.map((k,i)=>{
                return <div key={i} className="px-4 py-2 bg-slate-300 m-1 flex justify-between">{handleDate(k.startDate)} &nbsp; &nbsp;--&gt;&nbsp;&nbsp; {handleDate(k.endDate)}<button onClick={()=>props.deleteDates(k.startDate,k.endDate)} className="text-white bg-red-500 w-[25px] h-[25px] rounded-full font-extrabold ">X</button></div>
            })
              :""
            }
        <div className="flex p-2 gap-2">
            <input type="checkbox" checked={checked} onChange={handleSave} className="w-10 h-10" name="" id="" />
         
              <div className="flex flex-col">
                <label htmlFor="sDate"  className="mt-2 text-sm font-bold ">
                  Start Date
                </label>
                <input type="date" id="sDate" value={startDate} onChange={(e)=> setStartDate(e.target.value)}/>
              </div>
              <div className="flex flex-col">
                <label htmlFor="eDate" className="mt-2 text-sm font-bold ">
                  End Date
                </label>
                <input type="date" id="eDate" value={endDate} onChange={(e)=> setEndDate(e.target.value)}/>
              </div>
            </div>
       
    </div>
  )
}

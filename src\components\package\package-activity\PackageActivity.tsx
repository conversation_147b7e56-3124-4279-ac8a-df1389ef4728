/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeEvent, useContext, useEffect, useState } from "react";
import { label_css } from "../PackageForm";
import ActivityDay from "./ActivityDay";
import PackageContext from "@/utils/context/PackageContext";
import { useParams } from "react-router-dom";
import toast from "react-hot-toast";

export default function PackageActivity() {
  const {
    handleAddActivity,
    clearActivityEvent,
    numDays,
    setNumDays,
    noDays,
    noNights,
    destinationIds,
    dayActivity,
    handleDeleteAcvity,
  }: any = useContext(PackageContext);
  const [disable, setDisable] = useState(false);
  const { id } = useParams();
  const handleSetButtonClick = () => {
    destinationIds.length > 0
      ? numDays > 0 && numDays <= Math.max(noDays, noNights) + 1
        ? (function () {
            setDisable(true);
            const days = [];
            for (let i = 0; i < numDays; i++) {
              days.push(<ActivityDay key={i} day={i+1} />);

              handleAddActivity({
                day: i+1,
                from: "",
                to: "",
                startDateWise: 0,
                event: [],
              });
            }
          })()
        : toast.error("Invalid No. of Days")
      : toast.error("Please select at least one destination");
  };

  function handleDelete() {
    setDisable(false);
    setNumDays(0);
    clearActivityEvent();
  }
  useEffect(() => {
    if (id) {
      setDisable(true);
    }
  }, []);

  function addOneActivity() {
    handleAddActivity({ day: dayActivity.length + 1, event: [] });
  }
  function popOneActivity() {
    handleDeleteAcvity(dayActivity.length);
  }

  return (
    <div className="p-2">
      <div className="flex items-center">
        <div className={label_css + " mr-3"}>Activity:</div>
        <div className="">No Of Days :&nbsp;&nbsp;</div>
        <input
          value={numDays}
          disabled={disable}
          type="number"
          placeholder="No. of Days"
          onInput={(e: ChangeEvent<HTMLInputElement>) =>
            setNumDays(e.target.valueAsNumber)
          }
          className={"text-xl border-2 rounded-lg px-2 mr-1 w-14"}
        />
        <button
          disabled={disable}
          className={
            !disable
              ? "bg-yellow-500 px-2 rounded-sm"
              : "bg-yellow-400 cursor-not-allowed px-2 rounded-sm"
          }
          onClick={handleSetButtonClick}
        >
          Set
        </button>
        <button
          className={"bg-lime-500 px-2 rounded-sm mx-1"}
          onClick={addOneActivity}
        >
          Add One
        </button>
        <button
          className={"bg-orange-400 px-2 rounded-sm mx-1"}
          onClick={popOneActivity}
        >
          Pop
        </button>

        <button
          className="bg-red-500 px-2 rounded-sm m-1"
          onClick={handleDelete}
        >
          Clear
        </button>
      </div>
      <div className="flex flex-wrap">
        {dayActivity?.map((activity: any) => (
          <ActivityDay key={activity.day} day={activity.day} />
        ))}
      </div>
    </div>
  );
}

# PDF Tariff Extraction Testing & Validation

This document describes the testing and validation system for the PDF tariff extraction process.

## Overview

The testing and validation system consists of several components:

1. **Unit Tests**: Tests for individual components of the extraction system
2. **Integration Tests**: End-to-end tests with real PDF files
3. **Validation Rules**: Business rules to validate extraction results
4. **Test Data Generation**: Tools to generate synthetic test data
5. **Reporting**: HTML and JSON reports for validation results

## Directory Structure

```
├── advanced_pdf_extractor.py       # Main extraction script
├── run_tests.py                    # Main test runner
├── tests/
│   ├── test_pdf_extractor.py       # Unit tests
│   ├── generate_test_data.py       # Test data generator
│   ├── test_data/                  # Test PDF files
│   │   └── golden/                 # Golden files for validation
│   └── results/                    # Test results
└── validation/
    ├── tariff_validator.py         # Validation rules
    └── validate_extraction.py      # Validation script
```

## Running Tests

To run all tests:

```bash
python run_tests.py --all
```

To run specific tests:

```bash
# Generate test data
python run_tests.py --generate

# Run unit tests
python run_tests.py --unit-tests

# Run integration tests
python run_tests.py --integration-tests --pdf-dir /path/to/pdfs --output-dir /path/to/output

# Run validation on existing extraction results
python validation/validate_extraction.py /path/to/results.json --html
```

## Test Data Generation

The system can generate synthetic test data for testing the validation rules:

```bash
python tests/generate_test_data.py
```

This will generate:
- Standard test data with consistent pricing
- Dew Drops Farm Resorts Munnar specific test data
- Problematic test data with various issues for validation testing

## Validation Rules

The validation system checks extraction results against business rules, including:

### Required Fields
- All entries must have roomType, mealPlanType, and roomPrice

### Date Range Validation
- End date must be after start date
- No gaps or overlaps in date ranges
- Date ranges should cover at least 10 months

### Price Validation
- No negative or zero prices
- No unrealistically low or high prices
- No statistical outliers (using z-score)
- Consistent price hierarchy between room types
- Reasonable price ratios between room types

### Meal Plan Validation
- Expected meal plans (CP, MAP) should be present
- Consistent meal plans across room types
- Consistent price differences between meal plans

### Room Type Validation
- No similar room types that might be duplicates
- No unusual room type names
- Consistent price ratios between room types

### Completeness Validation
- Minimum number of entries
- Complete date coverage
- Expected extra fields (extraAdultCharge, etc.)

### Hotel-Specific Validation
- Dew Drops Farm Resorts Munnar specific rules
  - Expected room types
  - Expected date ranges
  - Correct MAP supplement (₹500 × 2 people)

## Validation Reports

The validation system generates two types of reports:

### JSON Report
Contains detailed information about validation issues, including:
- Issue type
- Message
- Severity (error, warning, info)
- Additional details specific to the issue type

### HTML Report
A user-friendly report with:
- Summary of validation issues
- Detailed list of issues with severity highlighting
- Table of extracted data

## Adding New Tests

### Adding Unit Tests
Add new test methods to `tests/test_pdf_extractor.py`.

### Adding Validation Rules
Add new validation methods to `validation/tariff_validator.py`.

### Adding Test PDFs
Place new PDF files in `tests/test_data/`.

### Adding Golden Files
Create JSON files in `tests/test_data/golden/` with the expected extraction results.

## Best Practices

1. **Run tests regularly**: Integrate testing into your development workflow
2. **Add tests for new features**: When adding new extraction capabilities, add corresponding tests
3. **Update golden files**: When extraction logic changes intentionally, update the golden files
4. **Review validation reports**: Check HTML reports to identify patterns of issues
5. **Add hotel-specific rules**: For hotels with unique formats, add specific validation rules

## Troubleshooting

### Common Issues

#### No PDFs Found
Ensure the PDF directory path is correct and contains PDF files.

#### Extraction Failures
Check the error messages in the logs. Common issues include:
- PDF is password protected
- PDF is scanned (requires OCR)
- PDF has unusual formatting

#### Validation Issues
Review the HTML validation report for details on specific issues.

### Getting Help

For more information, contact the development team or refer to the main documentation.

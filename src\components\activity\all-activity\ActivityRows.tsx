import { GoPencil } from "react-icons/go";
import { <PERSON> } from "react-router-dom";

/* eslint-disable @typescript-eslint/no-explicit-any */
export default function ActivityRows({data}:{data:any}) {
  return (
    <div className=" flex w-full border p-1 relative">
        <div className="w-1/6 text-center">{data?.name}</div>
        <div className="w-1/6 text-center">{data?.destinationName}</div>
        <div className="w-1/6 text-center">{data?.dayType}</div>
        <div className="w-1/6 text-center">{data?.level}</div>
        <div className="w-1/6 text-center">{data?.duration}</div>
        <div className="w-1/6 text-center">{data?.price}</div>
        <Link to={`/activity/${data?.activityId}/edit/`} className="absolute right-[5px] top-[3px] text-black">
        <GoPencil/>
        </Link>
    </div>
  )
}

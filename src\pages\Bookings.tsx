/* eslint-disable @typescript-eslint/no-explicit-any */

import { BookingColumns } from "@/components/bookings/BookingColumns";
import { DataTable } from "@/components/bookings/data-table";
import Loading from "@/components/package/Loading";
import { getBookings } from "@/utils/api-functions/getBookings";
import { useEffect, useState } from "react";
export default function Bookings() {
  const [search , setSearch] = useState("");
  const [offset, setOffset] = useState(0);
  const[bookings, setBookings] = useState<any>()
  const [isLoading, setIsLoading] = useState(true);
  console.log(setSearch);
  
  useEffect(()=>{
    async function fetchNext(){
      setIsLoading(true);
      const resp = await getBookings(offset, search);
      setBookings(resp);
      setIsLoading(false);
    }
    fetchNext()
  }, [offset, search])


  return isLoading ? <Loading /> :(
    <>
    <div className='min-h-[91vh] flex flex-col justify-between'> 
    <div className=" px-5 pb-24">
 
        <DataTable setSearch={setSearch} search={search} offset={offset} setOffset={setOffset} noOfHotels={bookings?.totalDocs} columns={BookingColumns} data={bookings?.docs}/>
    </div>
      </div>
    </>
  )
}
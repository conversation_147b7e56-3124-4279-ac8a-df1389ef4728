#!/usr/bin/env python3
"""
Comprehensive Test Suite for PDF Extraction

This module provides a test framework for the PDF extraction process,
with test cases for different PDF formats and expected outputs.
"""

import os
import sys
import json
import unittest
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import extraction components
from advanced_pdf_extractor import TariffExtractor
from utils.config_loader import ConfigLoader
from utils.extraction_models import PriceEntry, DateRange, ExtractionSource, ExtractionWarning
from utils.extraction_logger import ExtractionLogger

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_extraction')

# Test data directory
TEST_DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_data')

class TestExtractionBase(unittest.TestCase):
    """Base class for extraction tests"""
    
    def setUp(self):
        """Set up test environment"""
        # Create test data directory if it doesn't exist
        os.makedirs(TEST_DATA_DIR, exist_ok=True)
        
        # Load configuration
        self.config = ConfigLoader()
        
        # Create logger
        self.logger = ExtractionLogger('test_extraction', log_dir=os.path.join(TEST_DATA_DIR, 'logs'))
        
    def tearDown(self):
        """Clean up after tests"""
        pass
        
    def _get_test_pdf_path(self, pdf_name: str) -> str:
        """Get path to test PDF file"""
        return os.path.join(TEST_DATA_DIR, pdf_name)
        
    def _get_expected_output_path(self, pdf_name: str) -> str:
        """Get path to expected output file"""
        base_name = os.path.splitext(pdf_name)[0]
        return os.path.join(TEST_DATA_DIR, f"{base_name}_expected.json")
        
    def _load_expected_output(self, pdf_name: str) -> List[Dict[str, Any]]:
        """Load expected output from JSON file"""
        expected_output_path = self._get_expected_output_path(pdf_name)
        
        if not os.path.exists(expected_output_path):
            logger.warning(f"Expected output file not found: {expected_output_path}")
            return []
            
        try:
            with open(expected_output_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading expected output: {str(e)}")
            return []
            
    def _save_expected_output(self, pdf_name: str, output: List[Dict[str, Any]]) -> None:
        """Save expected output to JSON file"""
        expected_output_path = self._get_expected_output_path(pdf_name)
        
        try:
            with open(expected_output_path, 'w') as f:
                json.dump(output, f, indent=2)
                
            logger.info(f"Saved expected output to {expected_output_path}")
        except Exception as e:
            logger.error(f"Error saving expected output: {str(e)}")
            
    def _compare_outputs(self, actual: List[Dict[str, Any]], expected: List[Dict[str, Any]]) -> bool:
        """
        Compare actual and expected outputs
        
        Args:
            actual: Actual output from extraction
            expected: Expected output
            
        Returns:
            True if outputs match, False otherwise
        """
        # Check if number of entries matches
        if len(actual) != len(expected):
            logger.error(f"Number of entries doesn't match: actual={len(actual)}, expected={len(expected)}")
            return False
            
        # Compare each entry
        for i, (actual_entry, expected_entry) in enumerate(zip(actual, expected)):
            # Check required fields
            for field in ['room_type', 'room_price', 'meal_plan']:
                if field not in actual_entry:
                    logger.error(f"Entry {i} missing required field: {field}")
                    return False
                    
                if field not in expected_entry:
                    logger.error(f"Expected entry {i} missing required field: {field}")
                    return False
                    
                if actual_entry[field] != expected_entry[field]:
                    logger.error(f"Entry {i} field '{field}' doesn't match: actual={actual_entry[field]}, expected={expected_entry[field]}")
                    return False
                    
            # Check date range
            if 'date_range' not in actual_entry:
                logger.error(f"Entry {i} missing date_range")
                return False
                
            if 'date_range' not in expected_entry:
                logger.error(f"Expected entry {i} missing date_range")
                return False
                
            actual_date_range = actual_entry['date_range']
            expected_date_range = expected_entry['date_range']
            
            for field in ['start_date', 'end_date']:
                if field not in actual_date_range:
                    logger.error(f"Entry {i} date_range missing field: {field}")
                    return False
                    
                if field not in expected_date_range:
                    logger.error(f"Expected entry {i} date_range missing field: {field}")
                    return False
                    
                if actual_date_range[field] != expected_date_range[field]:
                    logger.error(f"Entry {i} date_range field '{field}' doesn't match: actual={actual_date_range[field]}, expected={expected_date_range[field]}")
                    return False
                    
        return True
        
    def _run_extraction_test(self, pdf_name: str, hotel_name: Optional[str] = None, 
                           room_id: Optional[str] = None, save_expected: bool = False) -> None:
        """
        Run extraction test for a PDF file
        
        Args:
            pdf_name: Name of the PDF file in the test data directory
            hotel_name: Optional hotel name
            room_id: Optional room ID
            save_expected: Whether to save the output as expected output
        """
        pdf_path = self._get_test_pdf_path(pdf_name)
        
        # Check if PDF file exists
        if not os.path.exists(pdf_path):
            self.fail(f"Test PDF file not found: {pdf_path}")
            
        # Create extractor
        extractor = TariffExtractor(pdf_path, hotel_name, room_id)
        
        # Extract data
        actual_output = extractor.extract()
        
        # Save as expected output if requested
        if save_expected:
            self._save_expected_output(pdf_name, actual_output)
            return
            
        # Load expected output
        expected_output = self._load_expected_output(pdf_name)
        
        # Compare outputs
        self.assertTrue(self._compare_outputs(actual_output, expected_output),
                      f"Extraction output doesn't match expected output for {pdf_name}")

class TestStandardTabularFormat(TestExtractionBase):
    """Tests for standard tabular format PDFs"""
    
    def test_gokulam_park(self):
        """Test extraction from Gokulam Park PDF"""
        self._run_extraction_test('gokulam_park_tariff.pdf', hotel_name='Gokulam Park')
        
class TestSeasonMealMatrixFormat(TestExtractionBase):
    """Tests for season-meal matrix format PDFs"""
    
    def test_spice_jungle(self):
        """Test extraction from Spice Jungle PDF"""
        self._run_extraction_test('spice_jungle_tariff.pdf', hotel_name='Spice Jungle')
        
class TestDualRateFormat(TestExtractionBase):
    """Tests for dual rate format PDFs"""
    
    def test_dew_drop(self):
        """Test extraction from Dew Drop PDF"""
        self._run_extraction_test('dew_drop_tariff_25_26.pdf', hotel_name='Dew Drop')
        
class TestComplexFormats(TestExtractionBase):
    """Tests for complex format PDFs"""
    
    def test_complex_pdf(self):
        """Test extraction from a complex PDF"""
        self._run_extraction_test('complex_format.pdf')
        
class TestImageBasedPDFs(TestExtractionBase):
    """Tests for image-based PDFs that require OCR"""
    
    def test_scanned_pdf(self):
        """Test extraction from a scanned PDF"""
        self._run_extraction_test('scanned_tariff.pdf')
        
class TestGenerateExpectedOutputs(TestExtractionBase):
    """Tests to generate expected outputs for test PDFs"""
    
    def test_generate_all_expected_outputs(self):
        """Generate expected outputs for all test PDFs"""
        test_pdfs = [
            ('gokulam_park_tariff.pdf', 'Gokulam Park', None),
            ('spice_jungle_tariff.pdf', 'Spice Jungle', None),
            ('dew_drop_tariff_25_26.pdf', 'Dew Drop', None),
            ('complex_format.pdf', None, None),
            ('scanned_tariff.pdf', None, None)
        ]
        
        for pdf_name, hotel_name, room_id in test_pdfs:
            pdf_path = self._get_test_pdf_path(pdf_name)
            
            # Skip if PDF file doesn't exist
            if not os.path.exists(pdf_path):
                logger.warning(f"Test PDF file not found: {pdf_path}")
                continue
                
            logger.info(f"Generating expected output for {pdf_name}")
            self._run_extraction_test(pdf_name, hotel_name, room_id, save_expected=True)

if __name__ == '__main__':
    unittest.main()

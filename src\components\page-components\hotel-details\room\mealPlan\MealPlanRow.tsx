import { Link } from 'react-router-dom';
import { MealPlan } from '../RoomDetailsHeader'
import { GoPencil } from "react-icons/go";
import { handleDate, isEndDatePast, isWithin10Days } from './handlingDates';


export default function MealPlanRow({ data }:{data:MealPlan}) {
  return (
    <div className="flex items-center py-4 px-6 border-b border-blue-100 bg-white rounded-xl shadow-sm mb-2 hover:shadow-lg hover:bg-blue-50 transition-all">
      <div className="w-1/6 font-semibold text-blue-900 text-base">{data.mealPlan.toUpperCase()}</div>
      <div className="w-1/6 text-green-700 font-bold text-base">₹{data.roomPrice}</div>
      <div className="w-1/6 text-blue-800 font-medium">₹{data.adultPrice}</div> 
      <div className="w-1/6 text-blue-800 font-medium">₹{data.childPrice}</div> 
      <div className="w-1/6">
        {Array.isArray(data.startDate) && data.startDate?.length > 0 ? (
          data.startDate?.map((k, i) => <div className='text-xs text-gray-600' key={i}>{handleDate(k)}</div>)
        ) : (
          ""
        )}
      </div>
      <div className="w-1/6">
        {Array.isArray(data.endDate) && data.endDate?.length > 0 ? (
          data.endDate?.map((k, i) =><div className={isWithin10Days(k)?"text-xs text-yellow-600":isEndDatePast(k)?"text-xs text-red-600":'text-xs text-gray-600'} key={i}>{handleDate(k)}</div>)
        ) : (
          ""
        )}
      </div>
      <Link to={`/hotels/edit/${data.hotelId}/${data.hotelRoomId}/${data.hotelMealId}`} className="ml-4">
        <div className="cursor-pointer p-2 rounded-full bg-blue-100 hover:bg-blue-200 transition">
          <GoPencil className="text-blue-700 text-lg"/>
        </div>
      </Link>
    </div>
  )
}

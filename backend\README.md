# Tripmilestone Admin Backend

This is the backend server for the Tripmilestone Admin Frontend, specifically handling the tariff upload functionality.

## Features

- PDF tariff upload and storage in Linode Object Storage
- PDF data extraction using Python scripts
- API endpoints for tariff management

## Prerequisites

- Node.js (v14 or higher)
- Python 3.x
- Linode Object Storage account

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/tripmilestone-admin-backend.git
   cd tripmilestone-admin-backend
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Run the setup script:
   ```bash
   npm run setup
   ```

4. Update the `.env` file with your Linode Object Storage credentials:
   ```
   LINODE_BUCKET_NAME=tripemilestone
   LINODE_ENDPOINT=https://in-maa-1.linodeobjects.com
   LINODE_ACCESS_KEY=your_access_key
   LINODE_SECRET_KEY=your_secret_key
   ```

## Running the Server

Start the server in development mode:
```bash
npm run dev
```

Start the server in production mode:
```bash
npm start
```

## API Endpoints

### Tariff Upload

- `POST /api/admin/hotel/tariff` - Create a new tariff upload
- `GET /api/admin/hotel/:hotelId/tariffs` - Get all tariffs for a hotel
- `PUT /api/admin/hotel/tariff/:tariffId` - Update tariff status (approve/reject)
- `DELETE /api/admin/hotel/tariff/:tariffId` - Delete a tariff upload

### Tariff Extraction

- `POST /api/admin/hotel/tariff/extract` - Extract data from a tariff PDF
- `POST /api/admin/hotel/tariff-extraction/extract-enhanced` - Extract data with enhanced extraction

## PDF Extraction

The backend uses Python scripts to extract data from PDF files. The extraction process follows these steps:

1. Upload the PDF file to Linode Object Storage
2. Download the PDF file to a temporary directory
3. Run the Python extraction script on the PDF file
4. Parse the extracted data and return it to the frontend
5. Clean up the temporary file

## Deployment

### Deploying to Linode

1. SSH into your Linode server:
   ```bash
   ssh username@your-linode-ip
   ```

2. Clone the repository:
   ```bash
   git clone https://github.com/your-username/tripmilestone-admin-backend.git
   cd tripmilestone-admin-backend
   ```

3. Install dependencies and set up the server:
   ```bash
   npm install
   npm run setup
   ```

4. Update the `.env` file with your Linode Object Storage credentials.

5. Start the server using PM2:
   ```bash
   npm install -g pm2
   pm2 start server.js --name tripmilestone-admin-backend
   ```

6. Configure PM2 to start on boot:
   ```bash
   pm2 startup
   pm2 save
   ```

## Troubleshooting

### PDF Extraction Issues

If PDF extraction is failing:

1. Check that Python is installed and in the PATH
2. Verify that all Python dependencies are installed
3. Check the logs in the `logs` directory for error messages
4. Try running the extraction script manually:
   ```bash
   python extract_pdf.py path/to/pdf/file.pdf
   ```

### Linode Object Storage Issues

If file uploads to Linode Object Storage are failing:

1. Verify your Linode Object Storage credentials in the `.env` file
2. Check that the bucket exists and is accessible
3. Verify that the access key has the necessary permissions

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

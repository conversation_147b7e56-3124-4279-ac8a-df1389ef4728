#!/usr/bin/env python3
"""
Run the PDF Extraction Monitoring Dashboard

This script starts the monitoring dashboard for the PDF extraction system.
"""

import os
import sys
import argparse
import logging
import subprocess
import time
import webbrowser
from threading import Thread

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('run_monitoring')

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Run PDF Extraction Monitoring Dashboard')
    parser.add_argument('--port', type=int, default=5000, help='Dashboard port')
    parser.add_argument('--no-browser', action='store_true', help='Do not open browser automatically')
    parser.add_argument('--log-dir', default='logs', help='Log directory to monitor')
    return parser.parse_args()

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import flask
        import matplotlib
        import psutil
        return True
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        print(f"Error: Missing dependency: {e}")
        print("Please install required dependencies:")
        print("pip install flask matplotlib psutil")
        return False

def open_browser(port, delay=2):
    """Open browser after a delay"""
    def _open_browser():
        time.sleep(delay)
        webbrowser.open(f"http://localhost:{port}")
    
    browser_thread = Thread(target=_open_browser)
    browser_thread.daemon = True
    browser_thread.start()

def create_template_directories():
    """Create necessary directories for the dashboard"""
    # Create templates directory
    templates_dir = os.path.join('monitoring', 'templates')
    os.makedirs(templates_dir, exist_ok=True)
    
    # Create static directory
    static_dir = os.path.join('monitoring', 'static')
    os.makedirs(static_dir, exist_ok=True)

def main():
    """Main function"""
    args = parse_args()
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Create template directories
    create_template_directories()
    
    # Create logs directory if it doesn't exist
    os.makedirs(args.log_dir, exist_ok=True)
    
    # Open browser if requested
    if not args.no_browser:
        open_browser(args.port)
    
    # Start the dashboard
    try:
        logger.info(f"Starting monitoring dashboard on port {args.port}")
        subprocess.run([
            sys.executable,
            'monitoring/dashboard.py',
            '--port', str(args.port),
            '--log-dir', args.log_dir
        ], check=True)
        return 0
    except subprocess.CalledProcessError as e:
        logger.error(f"Error starting dashboard: {e}")
        return 1
    except KeyboardInterrupt:
        logger.info("Dashboard stopped by user")
        return 0

if __name__ == "__main__":
    sys.exit(main())

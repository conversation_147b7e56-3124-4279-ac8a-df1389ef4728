[33mcommit f672a3c9f632384aa66ae5085744a9175484734f[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m
Author: sathishshah <<EMAIL>>
Date:   Wed May 14 18:09:15 2025 +0530

    Fix UI of Hotel List page pagination and Package list page UI. Fix build errors.

package-lock.json
src/components/package/PackageDelete.tsx
src/components/package/PackageForm.tsx
src/components/package/all-package/columns.tsx
src/components/package/all-package/data-table.tsx
src/components/package/package-activity/ActivityDay.tsx
src/components/package/package-activity/ActivityDetails.tsx
src/components/package/package-inputs/AddImage.tsx
src/components/package/package-inputs/EnhancedActivityMultiSelect.tsx
src/components/package/package-inputs/EnhancedDateSelect.tsx
src/components/package/package-inputs/EnhancedDestinationSelect.tsx
src/components/package/package-inputs/EnhancedHotelSelect.tsx
src/components/package/package-inputs/EnhancedVehicleMultiSelect.tsx
src/components/package/package-inputs/HotelFields.tsx
src/components/package/package-inputs/HotelFieldsMp.tsx
src/components/package/package-inputs/NoInputFields.tsx
src/components/package/package-inputs/NumberDropdown.tsx
src/components/package/package-inputs/PackageSearchSelect.tsx
src/components/page-components/hotel-details/room/mealPlan/AddMealPlan.tsx
src/components/page-components/hotel-edit/EditMp.tsx
src/components/page-components/hotel-edit/EditRooms.tsx
src/components/page-components/hotel-edit/TariffComparison.tsx
src/components/page-components/hotel-edit/TariffUploadList.tsx
src/components/page-components/hotel-edit/editMp.css
src/components/page-components/hotel-page/data-table.tsx
src/components/ui/alert.tsx
src/pages/Packages.tsx
src/types/types.ts
src/utils/api-functions/auth.ts
src/utils/api-functions/createPackage.ts
src/utils/api-functions/fetch-rooms.ts
src/utils/api-functions/getMealPlans.ts
src/utils/api-functions/getPackageHotelData.ts
src/utils/api-functions/getPackages.ts
src/utils/api-functions/tariff-upload.ts
src/utils/api-functions/upload-file.ts
src/utils/cn.ts
src/utils/constants/optionsData.tsx
src/utils/context/PackageContext.tsx

/**
 * Linode Object Storage Integration
 * This utility provides functions to interact with Linode Object Storage
 */

const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');
const logger = require('./logger');
const dotenv = require('dotenv');

// Load environment variables from .env file
const envPath = path.resolve(__dirname, '../.env');
dotenv.config({ path: envPath });

// Log the path to the .env file
logger.info(`Loading .env file from: ${envPath}`, {
  env_file_exists: fs.existsSync(envPath)
});

// Log credential status (without showing actual values)
logger.info('Linode Object Storage Configuration:', {
  endpoint: process.env.LINODE_ENDPOINT || 'https://in-maa-1.linodeobjects.com',
  bucket: process.env.LINODE_BUCKET_NAME || 'tripemilestone',
  has_access_key: !!process.env.LINODE_ACCESS_KEY,
  has_secret_key: !!process.env.LINODE_SECRET_KEY
});

// Configure AWS SDK to use Linode Object Storage
const s3 = new AWS.S3({
  endpoint: process.env.LINODE_ENDPOINT || 'https://in-maa-1.linodeobjects.com',
  accessKeyId: process.env.LINODE_ACCESS_KEY,
  secretAccessKey: process.env.LINODE_SECRET_KEY,
  s3ForcePathStyle: true, // Required for Linode Object Storage
  signatureVersion: 'v4'
});

const bucketName = process.env.LINODE_BUCKET_NAME || 'pdf-tariff';

/**
 * Upload a file to Linode Object Storage
 *
 * @param {string} localFilePath - Path to the local file
 * @param {string} remoteFilePath - Path in the bucket where the file will be stored
 * @returns {Promise<string>} - URL of the uploaded file
 */
async function uploadFile(localFilePath, remoteFilePath) {
  try {
    // Read the file
    const fileContent = fs.readFileSync(localFilePath);

    // Set up the upload parameters
    const params = {
      Bucket: bucketName,
      Key: remoteFilePath,
      Body: fileContent,
      ContentType: getContentType(localFilePath)
    };

    // Upload the file
    const data = await s3.upload(params).promise();

    logger.info(`File uploaded successfully to ${data.Location}`, {
      local_path: localFilePath,
      remote_path: remoteFilePath,
      bucket: bucketName
    });

    return data.Location;
  } catch (error) {
    logger.error(`Error uploading file to Linode: ${error.message}`, {
      error_stack: error.stack,
      local_path: localFilePath,
      remote_path: remoteFilePath,
      bucket: bucketName
    });
    throw new Error(`Failed to upload file to Linode: ${error.message}`);
  }
}

/**
 * Download a file from Linode Object Storage
 *
 * @param {string} remoteFilePath - Path in the bucket where the file is stored
 * @param {string} localFilePath - Path where the file will be saved locally
 * @returns {Promise<string>} - Path to the downloaded file
 */
async function downloadFile(remoteFilePath, localFilePath) {
  try {
    // Set up the download parameters
    const params = {
      Bucket: bucketName,
      Key: remoteFilePath
    };

    // Create directory if it doesn't exist
    const dir = path.dirname(localFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Create a write stream for the file
    const fileStream = fs.createWriteStream(localFilePath);

    // Download the file
    const s3Stream = s3.getObject(params).createReadStream();

    return new Promise((resolve, reject) => {
      s3Stream.on('error', error => {
        logger.error(`Error downloading file from Linode: ${error.message}`, {
          error_stack: error.stack,
          remote_path: remoteFilePath,
          local_path: localFilePath,
          bucket: bucketName
        });
        reject(error);
      });

      fileStream.on('error', error => {
        logger.error(`Error writing file: ${error.message}`, {
          error_stack: error.stack,
          local_path: localFilePath
        });
        reject(error);
      });

      fileStream.on('finish', () => {
        logger.info(`File downloaded successfully to ${localFilePath}`, {
          remote_path: remoteFilePath,
          local_path: localFilePath,
          bucket: bucketName
        });
        resolve(localFilePath);
      });

      s3Stream.pipe(fileStream);
    });
  } catch (error) {
    logger.error(`Error downloading file from Linode: ${error.message}`, {
      error_stack: error.stack,
      remote_path: remoteFilePath,
      local_path: localFilePath,
      bucket: bucketName
    });
    throw new Error(`Failed to download file from Linode: ${error.message}`);
  }
}

/**
 * Delete a file from Linode Object Storage
 *
 * @param {string} remoteFilePath - Path in the bucket where the file is stored
 * @returns {Promise<boolean>} - True if the file was deleted successfully
 */
async function deleteFile(remoteFilePath) {
  try {
    // Set up the delete parameters
    const params = {
      Bucket: bucketName,
      Key: remoteFilePath
    };

    // Delete the file
    await s3.deleteObject(params).promise();

    logger.info(`File deleted successfully: ${remoteFilePath}`, {
      remote_path: remoteFilePath,
      bucket: bucketName
    });

    return true;
  } catch (error) {
    logger.error(`Error deleting file from Linode: ${error.message}`, {
      error_stack: error.stack,
      remote_path: remoteFilePath,
      bucket: bucketName
    });
    throw new Error(`Failed to delete file from Linode: ${error.message}`);
  }
}

/**
 * Get the content type of a file based on its extension
 *
 * @param {string} filePath - Path to the file
 * @returns {string} - Content type
 */
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();

  const contentTypes = {
    '.pdf': 'application/pdf',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.txt': 'text/plain',
    '.html': 'text/html',
    '.json': 'application/json'
  };

  return contentTypes[ext] || 'application/octet-stream';
}

module.exports = {
  uploadFile,
  downloadFile,
  deleteFile
};

#!/bin/bash

echo "🚀 Starting server deployment fix..."

# Navigate to project directory
cd Tripmilestone-admin-fronend

echo "📋 Current status:"
echo "Git status:"
git status
echo "Current branch:"
git branch
echo "PM2 processes:"
pm2 list

echo "🔧 Fixing git issues..."
# Handle uncommitted changes
echo "Stashing any uncommitted changes..."
git stash

# Switch to main branch
echo "Switching to main branch..."
git checkout main

# Pull latest changes
echo "Pulling latest changes from main..."
git pull origin main

echo "✅ Git issues fixed!"

echo "🔍 Checking PM2 processes..."
pm2 list

# Check if package-lock.json exists and handle it
if [ -f "package-lock.json" ]; then
    echo "🗑️  Removing package-lock.json to prevent conflicts..."
    rm package-lock.json
fi

# Check if vite.config.js exists and handle it
if [ -f "vite.config.js" ]; then
    echo "🗑️  Removing vite.config.js (duplicate)..."
    rm vite.config.js
fi

echo "📦 Installing dependencies..."
export NVM_DIR=~/.nvm
source ~/.nvm/nvm.sh
npm install

echo "🏗️  Building application..."
npm run build

echo "🔄 Restarting PM2 processes..."
# Try to restart specific process first, then fallback to restart all
pm2 restart admin-frontend 2>/dev/null || pm2 restart 2 2>/dev/null || pm2 restart all

echo "📊 Final PM2 status:"
pm2 list

echo "✅ Server deployment fix completed!"
echo "🎉 Your application should now be running properly!" 
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Edit,
  Plus,
  Save,
  Trash2
} from 'lucide-react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';

interface ExtraCharge {
  id: string;
  type: string;
  description: string;
  amount: number;
  confidence?: number;
  isDerived?: boolean;
}

interface ExtraChargeMappingProps {
  extractedData: ExtraCharge[];
  mappingData: Record<string, string>;
  onMappingChange: (originalCharge: ExtraCharge, updatedCharge: ExtraCharge) => void;
  onNext: () => void;
  onPrevious: () => void;
  isProcessing: boolean;
  onSave: () => void;
}

const CHARGE_TYPES = [
  { id: 'extra_adult_cp', name: 'Extra Adult (CP)' },
  { id: 'extra_adult_map', name: 'Extra Adult (MAP)' },
  { id: 'extra_adult_ap', name: 'Extra Adult (AP)' },
  { id: 'extra_child_with_bed', name: 'Extra Child with Bed' },
  { id: 'extra_child_without_bed', name: 'Extra Child without Bed' },
  { id: 'map_supplement', name: 'MAP Supplement' },
  { id: 'ap_supplement', name: 'AP Supplement' }
];

const ExtraChargeMapping: React.FC<ExtraChargeMappingProps> = ({
  extractedData,
  onMappingChange,
  onNext,
  onPrevious
}) => {
  const [editingCharge, setEditingCharge] = useState<ExtraCharge | null>(null);
  const [newCharge, setNewCharge] = useState<Partial<ExtraCharge>>({
    type: 'extra_adult_cp',
    amount: 0,
    description: ''
  });
  const [showAddForm, setShowAddForm] = useState(false);

  // Format price for display
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(price);
  };

  // Handle charge update
  const handleUpdateCharge = (originalCharge: ExtraCharge) => {
    if (editingCharge) {
      // Validate charge data
      if (!editingCharge.type) {
        console.error('[TARIFF_MAPPING] Missing charge type in update');
        return;
      }

      if (typeof editingCharge.amount !== 'number' || isNaN(editingCharge.amount) || editingCharge.amount < 0) {
        console.error('[TARIFF_MAPPING] Invalid charge amount:', editingCharge.amount);
        return;
      }

      // Ensure description is set
      const updatedCharge = {
        ...editingCharge,
        description: editingCharge.description ||
                    CHARGE_TYPES.find(t => t.id === editingCharge.type)?.name ||
                    'Unknown Charge'
      };

      console.log('[TARIFF_MAPPING] Updating charge:', {
        original: originalCharge,
        updated: updatedCharge
      });

      onMappingChange(originalCharge, updatedCharge);
      setEditingCharge(null);
    }
  };

  // Handle new charge addition
  const handleAddCharge = () => {
    if (newCharge.type && newCharge.amount !== undefined) {
      // Validate charge data
      if (typeof newCharge.amount !== 'number' || isNaN(newCharge.amount) || newCharge.amount < 0) {
        console.error('[TARIFF_MAPPING] Invalid charge amount for new charge:', newCharge.amount);
        return;
      }

      const chargeToAdd = {
        id: `new_${Date.now()}`,
        type: newCharge.type,
        description: newCharge.description || CHARGE_TYPES.find(t => t.id === newCharge.type)?.name || 'Unknown Charge',
        amount: newCharge.amount
      };

      console.log('[TARIFF_MAPPING] Adding new charge:', chargeToAdd);

      onMappingChange(chargeToAdd, chargeToAdd);

      // Reset form
      setNewCharge({
        type: 'extra_adult_cp',
        amount: 0,
        description: ''
      });
      setShowAddForm(false);
    } else {
      console.error('[TARIFF_MAPPING] Cannot add charge: missing type or amount', newCharge);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">
          Step 4: Verify Extra Charges
        </h3>
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          {extractedData.length} Extra Charges
        </Badge>
      </div>

      <Alert className="bg-blue-50 border-blue-200">
        <AlertCircle className="h-4 w-4 text-blue-600" />
        <AlertTitle className="text-blue-800">Verify Extra Charges</AlertTitle>
        <AlertDescription className="text-blue-700">
          Review and adjust the extra charges extracted from the PDF. These include charges for extra adults, children, and meal plan supplements.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        {extractedData.map((charge) => {
          const isEditing = editingCharge && editingCharge.id === charge.id;
          const mappedCharge = charge;

          return (
            <div key={charge.id} className="bg-white rounded-lg border p-4 shadow-sm">
              {isEditing ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-gray-500 mb-1">Charge Type:</div>
                    <select
                      value={editingCharge.type}
                      onChange={(e) => setEditingCharge({
                        ...editingCharge,
                        type: e.target.value
                      })}
                      className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      {CHARGE_TYPES.map((type) => (
                        <option key={type.id} value={type.id}>
                          {type.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <div className="text-sm font-medium text-gray-500 mb-1">Amount:</div>
                    <Input
                      type="number"
                      value={editingCharge.amount}
                      onChange={(e) => setEditingCharge({
                        ...editingCharge,
                        amount: Number(e.target.value)
                      })}
                    />
                  </div>

                  <div className="md:col-span-2 flex justify-end gap-2 mt-2">
                    <Button
                      size="sm"
                      onClick={() => handleUpdateCharge(charge)}
                    >
                      <Save size={16} className="mr-1" />
                      Save
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditingCharge(null)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                  <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium text-gray-500 mb-1">Charge Type:</div>
                      <div className="font-medium text-gray-800">
                        {CHARGE_TYPES.find(t => t.id === mappedCharge.type)?.name || mappedCharge.description}
                      </div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-500 mb-1">Amount:</div>
                      <div className="font-medium text-gray-800">
                        {formatPrice(mappedCharge.amount)}

                        {mappedCharge.isDerived && (
                          <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200">
                            Derived
                          </Badge>
                        )}

                        {mappedCharge.confidence && (
                          <Badge
                            variant="outline"
                            className={`ml-2 ${
                              mappedCharge.confidence > 0.8
                                ? 'bg-green-50 text-green-700 border-green-200'
                                : 'bg-amber-50 text-amber-700 border-amber-200'
                            }`}
                          >
                            {Math.round(mappedCharge.confidence * 100)}% confidence
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditingCharge(mappedCharge)}
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-red-600 border-red-200 hover:bg-red-50"
                      onClick={() => onMappingChange(charge, charge)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* Add New Extra Charge Form */}
        {showAddForm ? (
          <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
            <div className="text-sm font-medium text-blue-800 mb-3">Add New Extra Charge</div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">Charge Type:</div>
                <select
                  value={newCharge.type}
                  onChange={(e) => setNewCharge({
                    ...newCharge,
                    type: e.target.value
                  })}
                  className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  {CHARGE_TYPES.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">Amount:</div>
                <Input
                  type="number"
                  value={newCharge.amount}
                  onChange={(e) => setNewCharge({
                    ...newCharge,
                    amount: Number(e.target.value)
                  })}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button
                size="sm"
                onClick={handleAddCharge}
                disabled={!newCharge.type || newCharge.amount === undefined}
              >
                <Save size={16} className="mr-1" />
                Add Charge
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowAddForm(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <Button
            variant="outline"
            className="w-full border-dashed border-blue-200 text-blue-600 hover:bg-blue-50"
            onClick={() => setShowAddForm(true)}
          >
            <Plus size={16} className="mr-1" />
            Add New Extra Charge
          </Button>
        )}
      </div>

      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={onPrevious}
          className="flex items-center"
        >
          <ChevronLeft size={16} className="mr-1" />
          Back
        </Button>
        <Button
          onClick={onNext}
          className="flex items-center"
        >
          Next Step
          <ChevronRight size={16} className="ml-1" />
        </Button>
      </div>
    </div>
  );
};

export default ExtraChargeMapping;

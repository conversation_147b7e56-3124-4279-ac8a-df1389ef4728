import { ColumnDef } from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Eye,
  MoreHorizontal,
  Pencil,
  Trash,
} from "lucide-react";

import { Link } from "react-router-dom";
import { BookingType } from "@/types/BookingType";
/**

couponCode (Applied)
redeemAmount (Applied)
discountPrice (For Package)
finalPrice

 */
export const BookingColumns: ColumnDef<BookingType>[] = [
  {
    accessorKey: "bookingId",
    header: () => {
      return <h1 className="text-center">Booking ID</h1>;
    },
    cell: ({ row }) => {
      const bookingId: string = row.getValue("bookingId");
      return <div className="text-center">{bookingId}</div>;
    },
  },
  {
    accessorKey: "full name",
    header: () => {
      return <h1>Full name</h1>;
    },
    cell: ({ row }) => {
      const userDetails = row.original.userDetails?.[0];
      return userDetails ? userDetails.fullName : "N/A";
    },
  },
  {
    accessorKey: "email",
    header: () => {
      return <h1>Email</h1>;
    },
    cell: ({ row }) => {
      const userDetails = row.original.userDetails?.[0];
      return userDetails ? userDetails.email : "N/A";
    },
  },
  {
    accessorKey: "mobile no",
    header: () => {
      return <h1>Mobile No</h1>;
    },
    cell: ({ row }) => {
      const userDetails = row.original.userDetails?.[0];
      return userDetails ? userDetails.mobileNo : "N/A";
    },
  },
  {
    accessorKey: "packageName",
    header: () => {
      return <h1>Package Name</h1>;
    },
    cell: ({ row }) => {
      const packageName: string = row.getValue("packageName");
      return <>{packageName}</>;
    },
  },



  {
    accessorKey: "interestName",
    header: () => {
      return <h1>Interest Name</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("interestName");
      return <>{interestName}</>;
    },
  },
  {
    accessorKey: "noOfDays",
    header: () => {
      return <h1>noOfDays</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("noOfDays");
      return <>{interestName}</>;
    },
  },



  {
    accessorKey: "noOfNight",
    header: () => {
      return <h1>noOfNight</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("noOfNight");
      return <>{interestName}</>;
    },
  },
  {
    accessorKey: "noOfAdult",
    header: () => {
      return <h1>noOfAdult</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("noOfAdult");
      return <>{interestName}</>;
    },
  },
  {
    accessorKey: "noOfChild",
    header: () => {
      return <h1>noOfChild</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("noOfChild");
      return <>{interestName}</>;
    },
  },
  {
    accessorKey: "startFrom",
    header: () => {
      return <h1>startFrom</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("startFrom");
      return <>{interestName}</>;
    },
  },
  {
    accessorKey: "fullStartDate",
    header: () => {
      return <h1>fullStartDate</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("fullStartDate");
      return <>{interestName}</>;
    },
  },
  {
    accessorKey: "fullEndDate",
    header: () => {
      return <h1>fullEndDate</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("fullEndDate");
      return <>{interestName}</>;
    },
  },
  {
    accessorKey: "status",
    header: () => {
      return <h1>status</h1>;
    },
    cell: ({ row }) => {
      const interestName: string = row.getValue("status");
      return <>{interestName}</>;
    },
  },
// fullStartDate



{
  accessorKey: "gstPrice",
  header: () => {
    return <h1>gstPrice</h1>;
  },
  cell: ({ row }) => {
    const interestName: string = row.getValue("gstPrice");
    return <>{interestName}</>;
  },
},
{
  accessorKey: "totalPackagePrice",
  header: () => {
    return <h1>totalPackagePrice</h1>;
  },
  cell: ({ row }) => {
    const interestName: string = row.getValue("totalPackagePrice");
    return <>{interestName}</>;
  },
},
{
  accessorKey: "agentAmount",
  header: () => {
    return <h1>agentAmount</h1>;
  },
  cell: ({ row }) => {
    const interestName: string = row.getValue("agentAmount");
    return <>{interestName}</>;
  },
},

{
  accessorKey: "discountPrice",
  header: () => {
    return <h1>discountPrice</h1>;
  },
  cell: ({ row }) => {
    const interestName: string = row.getValue("discountPrice");
    return <>{interestName}</>;
  },
},
// redeemCoin
// finalPrice

{
  accessorKey: "couponCode",
  header: () => {
    return <h1>couponCode</h1>;
  },
  cell: ({ row }) => {
    const interestName: null | string = row.getValue("couponCode");
    return <>{interestName === null ? "-" : interestName}</>;
  },
},
{
  accessorKey: "redeemCoin",
  header: () => {
    return <h1>redeemCoin</h1>;
  },
  cell: ({ row }) => {
    const interestName: null | string = row.getValue("redeemCoin");
    return <>{interestName}</>;
  },
},
{
  accessorKey: "finalPrice",
  header: () => {
    return <h1>finalPrice</h1>;
  },
  cell: ({ row }) => {
    const interestName: number = row.getValue("finalPrice");
    return <>{interestName}</>;
  },
},


  {
    accessorKey: "Actions",
    header: () => {
      return <h1>Actions</h1>;
    },
    cell: ({ row }) => {
      const { bookingId } = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant={"ghost"} className="h-4 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <Link to={`/bookings/${bookingId}`}>
              <DropdownMenuItem className="flex items-center gap-2">
                <Eye className="h-4 w-4 mr-2 text-green-500" />
                View
              </DropdownMenuItem>
            </Link>
            <Link to={`/bookings/edit/${bookingId}`}>
              <DropdownMenuItem className="flex items-center gap-2">
                <Pencil className="h-4 w-4 mr-2 text-slate-700" />
                Edit
              </DropdownMenuItem>
            </Link>
            <DropdownMenuItem className="flex items-center gap-2">
              <Trash className="h-4 w-4 mr-2 text-red-500" />
              {/* <Delete hotelId={hotelId} /> */}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

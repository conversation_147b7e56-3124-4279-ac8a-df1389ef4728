/* eslint-disable @typescript-eslint/no-explicit-any */
import toast from 'react-hot-toast';
import api from '../auth';

export const editActivity = async (activityId:string, data: any) => {
  try {
    const response = await api.put(
      `admin/activity/${activityId}/`,
      data
    );
    return Promise.resolve(response);
  } catch (error) {
    toast.error('An Error occurred');
    return Promise.reject('error');
  }
};

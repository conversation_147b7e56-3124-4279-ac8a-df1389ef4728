/* eslint-disable @typescript-eslint/no-explicit-any */

import {  useEffect, useState } from "react"
import toast from "react-hot-toast";
import { handleDate } from "./handlingDates";

interface SelectDatesProp{
  setDates:(s:string,e:string)=>void
  deleteDates:(s:string,e:string)=>void
  startDates:string[],
  endDates:string[]
}
export default function SelectDates(props:SelectDatesProp) {
    const [startDate,setStartDate] = useState("")
    const [endDate,setEndDate] = useState("")
    const [checked,setChecked] = useState(false);
  const [seDate,setSeDate] = useState<{s:string,e:string}[]>([])

    function handleSave(){
        if(startDate && endDate){
            setChecked(true)
            props.setDates(startDate,endDate)
            setStartDate("")
            setEndDate("")
            setChecked(false)
        }
        else{
        toast.error("input dates to save")
    }
}
function handleSeDates(){
  const arr = [];
  for(let i = 0;i<props.startDates?.length;i++){
    arr.push({s:props.startDates[i],e:props.endDates[i]})
  }
  setSeDate(arr);
}
useEffect(() => {
  handleSeDates();
}, [props.startDates,props.endDates]);

  return (
    <div className={"relative m-1 pt-4 border"}>
      {
            seDate?.length>0?seDate?.map((k,i)=>{
                return <div key={i} className="px-4 py-2 bg-slate-300 m-1 flex justify-between">{handleDate(k.s)} &nbsp; &nbsp;--&gt;&nbsp;&nbsp; {handleDate(k.e)}<button onClick={()=>props.deleteDates(k.s,k.e)} className="text-white bg-red-500 w-[25px] h-[25px] rounded-full font-extrabold ">X</button></div>
            })
              :""
            }
        <div className="flex p-2 gap-2">
            <input type="checkbox" checked={checked} onChange={handleSave} className="w-10 h-10" name="" id="" />
         
              <div className="flex flex-col">
                <label htmlFor="sDate"  className="mt-2 text-sm font-bold ">
                  Start Date
                </label>
                <input type="date" id="sDate" value={startDate} onChange={(e)=> setStartDate(e.target.value)}/>
              </div>
              <div className="flex flex-col">
                <label htmlFor="eDate" className="mt-2 text-sm font-bold ">
                  End Date
                </label>
                <input type="date" id="eDate" value={endDate} onChange={(e)=> setEndDate(e.target.value)}/>
              </div>
            </div>
       
    </div>
  )
}

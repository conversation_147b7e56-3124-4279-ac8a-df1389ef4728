#!/usr/bin/env python3
"""
Test script for PDF tariff extraction

Usage:
    python test_extractor.py <pdf_file_path>

This script tests the extraction of tariff data from a PDF and prints the results.
It's useful for verifying that the extraction works correctly before using it in the dashboard.
"""

import sys
import json
import logging
from extract_pdf_improved import extract_from_tables

# Configure more verbose logging for testing
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('extraction_test.log')
    ]
)
logger = logging.getLogger('tariff_test')

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_extractor.py <pdf_file_path>")
        sys.exit(1)

    pdf_path = sys.argv[1]
    logger.info(f"Testing extraction from: {pdf_path}")
    
    # Extract data
    print(f"\nExtracting data from {pdf_path}...")
    results = extract_from_tables(pdf_path)
    
    # Print results
    print("\n==== EXTRACTION RESULTS ====")
    print(f"Found {len(results)} tariff entries")
    print(json.dumps(results, indent=2))
    print("\nExtraction test complete. Check extraction_test.log for detailed logs.")

    # Save to file for reference
    output_file = f"{pdf_path.split('/')[-1].split('.')[0]}_extraction_results.json"
    with open(output_file, 'w') as f:
        json.dump(results, indent=2, fp=f)
    print(f"\nResults saved to {output_file}")

if __name__ == "__main__":
    main() 
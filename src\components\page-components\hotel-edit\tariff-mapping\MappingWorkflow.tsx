import React, { useState, useEffect } from 'react';
import { HotelRoom } from '@/types/types';
import RoomTypeMapping from './RoomTypeMapping';
import DateRangeMapping from './DateRangeMapping';
import PriceMapping from './PriceMapping';
import ExtraChargeMapping from './ExtraChargeMapping';
import ReviewStep from './ReviewStep';
import { Button } from '@/components/ui/button';
import {
  AlertCircle,
  X
} from 'lucide-react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';
import { v4 as uuidv4 } from 'uuid';

interface DateRange {
  startDate: string;
  endDate: string;
  confidence?: number;
}

interface Price {
  id: string;
  roomType: string;
  mealPlan: string;
  price: number;
  startDate: string;
  endDate: string;
  confidence?: number;
  isDerived?: boolean;
}

interface ExtraCharge {
  id: string;
  type: string;
  description: string;
  amount: number;
  confidence?: number;
  isDerived?: boolean;
}

interface ExtractedData {
  roomTypes: string[];
  dateRanges: DateRange[];
  prices: Price[];
  extraCharges: ExtraCharge[];
  confidence?: number;
  filePath?: string;
  originalFile?: File;
}

interface MappingWorkflowProps {
  hotelId: string;
  roomId?: string;
  hotelRooms: HotelRoom[];
  extractedData: ExtractedData;
  onSave: (mappedData: any) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const MappingWorkflow: React.FC<MappingWorkflowProps> = ({
  hotelId,
  roomId,
  hotelRooms,
  extractedData,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [roomMappings, setRoomMappings] = useState<Record<string, string>>({});
  const [dateRangeMappings, setDateRangeMappings] = useState<Record<string, DateRange>>({});
  const [priceMappings, setPriceMappings] = useState<Record<string, Price>>({});
  const [chargeMappings, setChargeMappings] = useState<Record<string, ExtraCharge>>({});
  const [dateRanges, setDateRanges] = useState<DateRange[]>([]);
  const [prices, setPrices] = useState<Price[]>([]);
  const [extraCharges, setExtraCharges] = useState<ExtraCharge[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [saveLoading, setSaveLoading] = useState(false);

  // Initialize data from extracted data
  useEffect(() => {
    if (extractedData) {
      console.log('[TARIFF_MAPPING] Initializing data from extracted data:', extractedData);

      // Initialize room mappings
      const initialRoomMappings: Record<string, string> = {};

      // Ensure roomTypes is an array
      const roomTypes = Array.isArray(extractedData.roomTypes) ? extractedData.roomTypes : [];

      roomTypes.forEach(roomType => {
        if (!roomType) return; // Skip empty room types

        // Try to find a matching room
        const matchingRoom = hotelRooms.find(
          room => room.hotelRoomType && room.hotelRoomType.toLowerCase() === roomType.toLowerCase()
        );

        if (matchingRoom) {
          console.log(`[TARIFF_MAPPING] Found matching room for "${roomType}": ${matchingRoom.hotelRoomType} (${matchingRoom.hotelRoomId})`);
          initialRoomMappings[roomType] = matchingRoom.hotelRoomId;
        } else {
          console.log(`[TARIFF_MAPPING] No matching room found for "${roomType}"`);
          initialRoomMappings[roomType] = '';
        }
      });

      console.log('[TARIFF_MAPPING] Initial room mappings:', initialRoomMappings);
      setRoomMappings(initialRoomMappings);

      // Initialize date ranges with validation
      const validDateRanges = (extractedData.dateRanges || []).filter(range => {
        // Ensure both dates are valid
        const hasValidDates = range.startDate && range.endDate &&
                             isValidDateString(range.startDate) &&
                             isValidDateString(range.endDate);

        if (!hasValidDates) {
          console.log(`[TARIFF_MAPPING] Skipping invalid date range:`, range);
        }

        return hasValidDates;
      });

      console.log('[TARIFF_MAPPING] Valid date ranges:', validDateRanges);
      setDateRanges(validDateRanges);

      // Initialize prices with validation
      const validPrices = (extractedData.prices || []).filter(price => {
        // Ensure price has required fields
        const isValid = price &&
                       price.roomType &&
                       typeof price.price === 'number' &&
                       price.startDate &&
                       price.endDate &&
                       isValidDateString(price.startDate) &&
                       isValidDateString(price.endDate);

        if (!isValid) {
          console.log(`[TARIFF_MAPPING] Skipping invalid price:`, price);
        }

        return isValid;
      }).map(price => ({
        ...price,
        id: price.id || uuidv4(),
        // Ensure mealPlan is lowercase and valid
        mealPlan: validateMealPlan(price.mealPlan)
      }));

      console.log('[TARIFF_MAPPING] Valid prices:', validPrices);
      setPrices(validPrices);

      // Initialize extra charges with validation
      const validCharges = (extractedData.extraCharges || []).filter(charge => {
        // Ensure charge has required fields
        const isValid = charge &&
                       charge.type &&
                       charge.description &&
                       typeof charge.amount === 'number';

        if (!isValid) {
          console.log(`[TARIFF_MAPPING] Skipping invalid charge:`, charge);
        }

        return isValid;
      }).map(charge => ({
        ...charge,
        id: charge.id || uuidv4()
      }));

      console.log('[TARIFF_MAPPING] Valid charges:', validCharges);
      setExtraCharges(validCharges);
    }
  }, [extractedData, hotelRooms]);

  // Helper function to validate date strings
  const isValidDateString = (dateStr: string): boolean => {
    if (!dateStr) return false;

    try {
      // Try parsing as ISO date
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return false;
      }
      return true;
    } catch (e) {
      return false;
    }
  };

  // Helper function to validate and normalize meal plan
  const validateMealPlan = (mealPlan: string): string => {
    if (!mealPlan) return 'cp'; // Default to CP

    // Convert to lowercase
    const normalizedMealPlan = mealPlan.toLowerCase();

    // Check if it's a valid meal plan
    const validMealPlans = ['cp', 'map', 'ap', 'ep'];
    if (validMealPlans.includes(normalizedMealPlan)) {
      return normalizedMealPlan;
    }

    // Try to match based on partial strings
    if (normalizedMealPlan.includes('cp') || normalizedMealPlan.includes('continental')) {
      return 'cp';
    } else if (normalizedMealPlan.includes('map') || normalizedMealPlan.includes('modified')) {
      return 'map';
    } else if (normalizedMealPlan.includes('ap') || normalizedMealPlan.includes('american')) {
      return 'ap';
    } else if (normalizedMealPlan.includes('ep') || normalizedMealPlan.includes('european')) {
      return 'ep';
    }

    // Default to CP if no match
    return 'cp';
  };

  // Handle room mapping
  const handleMapRoom = (extractedRoomType: string, dbRoomId: string) => {
    setRoomMappings(prev => ({
      ...prev,
      [extractedRoomType]: dbRoomId
    }));
  };

  // Handle new room creation
  const handleCreateNewRoom = (extractedRoomType: string) => {
    // In a real implementation, this would create a new room in the database
    // For now, we'll just update the mapping
    setRoomMappings(prev => ({
      ...prev,
      [extractedRoomType]: 'new'
    }));
  };

  // Handle date range update
  const handleUpdateDateRange = (originalRange: DateRange, updatedRange: DateRange) => {
    setDateRanges(prev =>
      prev.map(range =>
        (range.startDate === originalRange.startDate && range.endDate === originalRange.endDate)
          ? updatedRange
          : range
      )
    );

    // Update date range mapping
    setDateRangeMappings(prev => ({
      ...prev,
      [`${originalRange.startDate}_${originalRange.endDate}`]: updatedRange
    }));
  };

  // Handle new date range addition
  const handleAddDateRange = (newRange: DateRange) => {
    setDateRanges(prev => [...prev, newRange]);
  };

  // Handle date range removal
  const handleRemoveDateRange = (range: DateRange) => {
    setDateRanges(prev =>
      prev.filter(r => !(r.startDate === range.startDate && r.endDate === range.endDate))
    );

    // Remove from mappings
    const key = `${range.startDate}_${range.endDate}`;
    setDateRangeMappings(prev => {
      const newMappings = { ...prev };
      delete newMappings[key];
      return newMappings;
    });
  };

  // Handle price update
  const handleUpdatePrice = (originalPrice: Price, updatedPrice: Price) => {
    setPriceMappings(prev => ({
      ...prev,
      [originalPrice.id]: updatedPrice
    }));
  };

  // Handle price removal
  const handleRemovePrice = (price: Price) => {
    setPrices(prev => prev.filter(p => p.id !== price.id));

    // Remove from mappings
    setPriceMappings(prev => {
      const newMappings = { ...prev };
      delete newMappings[price.id];
      return newMappings;
    });
  };

  // Handle extra charge update
  const handleUpdateCharge = (originalCharge: ExtraCharge, updatedCharge: ExtraCharge) => {
    setChargeMappings(prev => ({
      ...prev,
      [originalCharge.id]: updatedCharge
    }));
  };

  // These functions are currently unused but kept for potential future use
  // const handleAddCharge = (newCharge: ExtraCharge) => {
  //   setExtraCharges(prev => [...prev, newCharge]);
  // };

  // const handleRemoveCharge = (charge: ExtraCharge) => {
  //   setExtraCharges(prev => prev.filter(c => c.id !== charge.id));

  //   // Remove from mappings
  //   setChargeMappings(prev => {
  //     const newMappings = { ...prev };
  //     delete newMappings[charge.id];
  //     return newMappings;
  //   });
  // };

  // Handle save
  const handleSave = async () => {
    try {
      setSaveLoading(true);
      setError(null);

      console.log('[TARIFF_MAPPING] Preparing data for save...');

      // Validate room mappings
      const validRoomMappings: Record<string, string> = {};
      let hasValidRoomMappings = false;

      Object.entries(roomMappings).forEach(([roomType, roomId]) => {
        if (roomId) {
          validRoomMappings[roomType] = roomId;
          hasValidRoomMappings = true;
        }
      });

      if (!hasValidRoomMappings) {
        throw new Error('Please map at least one room type before saving');
      }

      // Validate date ranges
      if (dateRanges.length === 0) {
        throw new Error('Please add at least one date range before saving');
      }

      // Validate prices
      if (prices.length === 0) {
        throw new Error('No valid prices found. Please check your data');
      }

      // Prepare final price data with proper room IDs
      const finalPrices = prices.map(price => {
        // Get the mapped room ID for this room type
        const mappedRoomId = roomMappings[price.roomType] || '';

        // If this price has been edited, use the mapped version
        const mappedPrice = priceMappings[price.id] || price;

        return {
          ...mappedPrice,
          // Ensure mealPlan is lowercase
          mealPlan: validateMealPlan(mappedPrice.mealPlan),
          // Use the mapped room ID
          roomId: mappedRoomId,
          // Add hotelId
          hotelId
        };
      }).filter(price => price.roomId); // Only include prices with valid room IDs

      // Prepare final charge data
      const finalCharges = extraCharges.map(charge => {
        // If this charge has been edited, use the mapped version
        const mappedCharge = chargeMappings[charge.id] || charge;

        return {
          ...mappedCharge,
          // Add hotelId
          hotelId
        };
      });

      // Prepare mapped data
      const mappedData = {
        hotelId,
        roomId: roomId || Object.values(validRoomMappings)[0], // Use first mapped room if no roomId provided
        roomMappings: validRoomMappings,
        dateRangeMappings,
        priceMappings,
        chargeMappings,
        filePath: extractedData.filePath || '',
        originalFile: extractedData.originalFile,
        extractedData: {
          roomTypes: extractedData.roomTypes || [],
          dateRanges,
          prices: finalPrices,
          extraCharges: finalCharges
        }
      };

      console.log('[TARIFF_MAPPING] Mapped data prepared with file path:', {
        file_path: extractedData.filePath,
        has_original_file: !!extractedData.originalFile
      });

      console.log('[TARIFF_MAPPING] Saving mapped data:', mappedData);

      // Call the save function
      await onSave(mappedData);

      // Success!
      setSaveLoading(false);
      console.log('[TARIFF_MAPPING] Data saved successfully');
    } catch (error) {
      console.error('[TARIFF_MAPPING] Error saving data:', error);
      setSaveLoading(false);
      setError(error instanceof Error ? error.message : 'An error occurred while saving');
    }
  };

  // Render current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <RoomTypeMapping
            extractedRoomTypes={extractedData.roomTypes}
            hotelRooms={hotelRooms}
            roomMappings={roomMappings}
            onMapRoom={handleMapRoom}
            onCreateNewRoom={handleCreateNewRoom}
            onNext={() => setCurrentStep(2)}
          />
        );
      case 2:
        return (
          <DateRangeMapping
            extractedDateRanges={extractedData.dateRanges}
            dateRangeMappings={dateRangeMappings}
            onUpdateDateRange={handleUpdateDateRange}
            onAddDateRange={handleAddDateRange}
            onRemoveDateRange={handleRemoveDateRange}
            onNext={() => setCurrentStep(3)}
            onBack={() => setCurrentStep(1)}
          />
        );
      case 3:
        return (
          <PriceMapping
            extractedPrices={prices}
            roomMappings={roomMappings}
            hotelRooms={hotelRooms}
            dateRanges={dateRanges}
            priceMappings={priceMappings}
            onUpdatePrice={handleUpdatePrice}
            onRemovePrice={handleRemovePrice}
            onNext={() => setCurrentStep(4)}
            onBack={() => setCurrentStep(2)}
          />
        );
      case 4:
        return (
          <ExtraChargeMapping
            extractedData={extraCharges}
            mappingData={{}}
            onMappingChange={handleUpdateCharge}
            onNext={() => setCurrentStep(5)}
            onPrevious={() => setCurrentStep(3)}
            isProcessing={saveLoading || isLoading}
            onSave={handleSave}
          />
        );
      case 5:
        return (
          <ReviewStep
            hotelRooms={hotelRooms}
            roomMappings={roomMappings}
            dateRanges={dateRanges}
            prices={prices}
            extraCharges={extraCharges}
            priceMappings={priceMappings}
            chargeMappings={chargeMappings}
            onSave={handleSave}
            onBack={() => setCurrentStep(4)}
            isLoading={saveLoading || isLoading}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="flex items-center justify-between bg-white p-4 rounded-lg border shadow-sm">
        <div className="flex items-center space-x-2">
          <Badge
            className={`${
              currentStep >= 1
                ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            } cursor-pointer`}
            onClick={() => currentStep > 1 && setCurrentStep(1)}
          >
            1. Room Types
          </Badge>
          <div className="w-4 h-0.5 bg-gray-200"></div>
          <Badge
            className={`${
              currentStep >= 2
                ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            } cursor-pointer`}
            onClick={() => currentStep > 2 && setCurrentStep(2)}
          >
            2. Date Ranges
          </Badge>
          <div className="w-4 h-0.5 bg-gray-200"></div>
          <Badge
            className={`${
              currentStep >= 3
                ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            } cursor-pointer`}
            onClick={() => currentStep > 3 && setCurrentStep(3)}
          >
            3. Prices
          </Badge>
          <div className="w-4 h-0.5 bg-gray-200"></div>
          <Badge
            className={`${
              currentStep >= 4
                ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            } cursor-pointer`}
            onClick={() => currentStep > 4 && setCurrentStep(4)}
          >
            4. Extra Charges
          </Badge>
          <div className="w-4 h-0.5 bg-gray-200"></div>
          <Badge
            className={`${
              currentStep >= 5
                ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            } cursor-pointer`}
            onClick={() => currentStep > 5 && setCurrentStep(5)}
          >
            5. Review
          </Badge>
        </div>

        <Button
          variant="outline"
          size="sm"
          className="text-red-600 border-red-200 hover:bg-red-50"
          onClick={onCancel}
          disabled={saveLoading || isLoading}
        >
          <X size={16} className="mr-1" />
          Cancel
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Current Step */}
      {renderStep()}
    </div>
  );
};

export default MappingWorkflow;

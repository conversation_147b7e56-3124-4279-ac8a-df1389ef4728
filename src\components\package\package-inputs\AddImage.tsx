/* eslint-disable @typescript-eslint/no-explicit-any */
import { handleImageUpload } from "@/utils/api-functions/upload-image"
import PackageContext from "@/utils/context/PackageContext";
import { useContext, useState } from "react"
import toast from "react-hot-toast";

export default function AddImage() {
    const {setPackageImages,handleDeleteImage,packageImages}:any = useContext(PackageContext)
    const [image,setImage] = useState<File | null>()

    async function handleUpload(){
        if(image){
            const resp = await handleImageUpload("package",image.type,image.name,image);
            setPackageImages((prev:string[])=>[...prev,resp])
            setImage(null)
        }
    }
    function handleClose(url:string){
        handleDeleteImage(url)
    }

    function handleImage(img:File){
      const supportedFormats = ['jpg','jpeg','png','webp']
      const format = img?.type.split('/')[1]
      if (supportedFormats?.includes(format as string)) {
        setImage(img)
      }else{
        if(format){

          toast.error(format+" is not supported")
          setImage(null)
        }
      }
    }
  return <>
  {
    packageImages?.length >0 && packageImages?.map((img:string)=>(
        <div key={img} className="px-4 py-2 bg-gray-100 border border-gray-200 rounded-md flex justify-between items-center m-1 shadow-sm">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <a
              target="_blank"
              href={"https://tripemilestone.in-maa-1.linodeobjects.com/"+img}
              className="text-blue-600 hover:text-blue-800 hover:underline text-sm truncate max-w-[200px]"
            >
              {img.split("-").pop()}
            </a>
          </div>
          <button
            onClick={() => handleClose(img)}
            className="w-6 h-6 rounded-full flex items-center justify-center bg-red-500 hover:bg-red-600 text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-400"
          >
            ×
          </button>
        </div>
    ))

  }

  <div className="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-md m-1 shadow-sm">
    <div className="flex-1">
      <label
        htmlFor="package-img"
        className="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md cursor-pointer transition-colors duration-200 shadow-sm inline-block"
      >
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
        </svg>
        <span className="text-xs font-medium">Choose Image</span>
      </label>

      {image?.name && (
        <div className="mt-2 flex items-center text-sm text-gray-600">
          <svg className="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="truncate max-w-[200px]">{image?.name}</span>
        </div>
      )}

      <input type="file" name="" className="hidden" id="package-img" onChange={(e:any) => handleImage(e.target.files[0])} />
    </div>

    <button
      onClick={handleUpload}
      disabled={!image}
      className={`
        flex items-center px-3 py-2 rounded-md text-white shadow-sm transition-colors duration-200
        ${image
          ? "bg-green-600 hover:bg-green-700"
          : "bg-gray-400 cursor-not-allowed"}
      `}
    >
      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
      </svg>
      <span className="text-xs font-medium">Upload</span>
    </button>
  </div>


  </>




}

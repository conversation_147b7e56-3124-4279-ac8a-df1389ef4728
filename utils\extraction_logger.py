#!/usr/bin/env python3
"""
Enhanced Logging and Error Handling for PDF Extraction

This module provides a robust logging and error handling system for the PDF extraction process.
It categorizes errors, provides detailed logging, and supports alerting for critical issues.
"""

import os
import sys
import json
import logging
import traceback
import time
import smtplib
import socket
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple, Union
from functools import wraps
from collections import defaultdict, deque

# Configure base logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Error categories
ERROR_CATEGORIES = {
    "PDF_ACCESS": "PDF Access Error",
    "PDF_PARSING": "PDF Parsing Error",
    "TABLE_EXTRACTION": "Table Extraction Error",
    "TEXT_EXTRACTION": "Text Extraction Error",
    "DATE_PARSING": "Date Parsing Error",
    "PRICE_EXTRACTION": "Price Extraction Error",
    "MEAL_PLAN": "Meal Plan Error",
    "OCR": "OCR Error",
    "NER": "NER Error",
    "VALIDATION": "Validation Error",
    "MEMORY": "Memory Error",
    "TIMEOUT": "Timeout Error",
    "CONFIG": "Configuration Error",
    "PATTERN_MATCH": "Pattern Matching Error",
    "COLUMN_IDENTIFICATION": "Column Identification Error",
    "DATA_CLEANING": "Data Cleaning Error",
    "HOTEL_SPECIFIC": "Hotel-Specific Handler Error",
    "UNKNOWN": "Unknown Error"
}

# Severity levels
SEVERITY = {
    "INFO": 0,
    "WARNING": 1,
    "ERROR": 2,
    "CRITICAL": 3
}

# In-memory metrics storage
extraction_metrics = {
    "total_pdfs": 0,
    "successful_extractions": 0,
    "failed_extractions": 0,
    "total_processing_time": 0,
    "errors_by_category": defaultdict(int),
    "errors_by_hotel": defaultdict(int),
    "processing_times": [],
    "recent_errors": deque(maxlen=100),  # Keep last 100 errors
    "start_time": datetime.now()
}

# Alert thresholds
ALERT_THRESHOLDS = {
    "error_rate": 0.2,  # Alert if error rate exceeds 20%
    "critical_errors": 5,  # Alert after 5 critical errors
    "processing_time": 300,  # Alert if processing time exceeds 5 minutes
    "memory_usage": 1024  # Alert if memory usage exceeds 1GB
}

# Alert configuration
ALERT_CONFIG = {
    "enabled": False,
    "email": {
        "enabled": False,
        "smtp_server": "smtp.example.com",
        "smtp_port": 587,
        "username": "<EMAIL>",
        "password": "password",
        "from_address": "<EMAIL>",
        "to_addresses": ["<EMAIL>"]
    },
    "slack": {
        "enabled": False,
        "webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz"
    }
}

class ExtractionError(Exception):
    """Custom exception for extraction errors"""

    def __init__(self, message: str, category: str = "UNKNOWN",
                 severity: str = "ERROR", details: Dict[str, Any] = None):
        """
        Initialize extraction error

        Args:
            message: Error message
            category: Error category from ERROR_CATEGORIES
            severity: Error severity from SEVERITY
            details: Additional error details
        """
        self.message = message
        self.category = category if category in ERROR_CATEGORIES else "UNKNOWN"
        self.severity = severity if severity in SEVERITY else "ERROR"
        self.details = details or {}
        self.timestamp = datetime.now()

        # Include stack trace
        self.stack_trace = traceback.format_exc()

        super().__init__(self.message)

class ExtractionLogger:
    """Enhanced logger for PDF extraction"""

    def __init__(self, name: str, log_dir: str = "logs",
                 console_level: int = logging.INFO,
                 file_level: int = logging.DEBUG):
        """
        Initialize logger

        Args:
            name: Logger name
            log_dir: Directory for log files
            console_level: Logging level for console output
            file_level: Logging level for file output
        """
        self.name = name
        self.log_dir = log_dir

        # Create log directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)

        # Create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)

        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(console_level)
        console_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_format)
        self.logger.addHandler(console_handler)

        # Create file handler for general logs
        log_file = os.path.join(log_dir, f"{name}.log")
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(file_level)
        file_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_format)
        self.logger.addHandler(file_handler)

        # Create file handler for error logs
        error_log_file = os.path.join(log_dir, f"{name}_errors.log")
        error_file_handler = logging.FileHandler(error_log_file)
        error_file_handler.setLevel(logging.ERROR)
        error_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        error_file_handler.setFormatter(error_format)
        self.logger.addHandler(error_file_handler)

        # Create JSON handler for structured logging
        json_log_file = os.path.join(log_dir, f"{name}.json")
        self.json_file = json_log_file

    def log_extraction_start(self, pdf_path: str, hotel_name: Optional[str] = None,
                            room_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Log the start of an extraction process

        Args:
            pdf_path: Path to the PDF file
            hotel_name: Name of the hotel
            room_id: ID of the room

        Returns:
            Dictionary with extraction context
        """
        context = {
            "pdf_path": pdf_path,
            "hotel_name": hotel_name,
            "room_id": room_id,
            "start_time": datetime.now(),
            "extraction_id": f"extract_{int(time.time())}_{os.path.basename(pdf_path)}",
            "hostname": socket.gethostname()
        }

        self.logger.info(f"Starting extraction for {pdf_path}")

        # Update metrics
        extraction_metrics["total_pdfs"] += 1

        return context

    def log_extraction_end(self, context: Dict[str, Any], success: bool,
                          results: Optional[List[Dict[str, Any]]] = None,
                          error: Optional[ExtractionError] = None) -> None:
        """
        Log the end of an extraction process

        Args:
            context: Extraction context from log_extraction_start
            success: Whether the extraction was successful
            results: Extraction results if successful
            error: Error if extraction failed
        """
        # Calculate processing time
        end_time = datetime.now()
        start_time = context.get("start_time", end_time)
        processing_time = (end_time - start_time).total_seconds()

        # Update context
        context.update({
            "end_time": end_time,
            "processing_time": processing_time,
            "success": success,
            "results_count": len(results) if results else 0
        })

        # Log success or failure
        if success:
            self.logger.info(f"Extraction completed successfully for {context['pdf_path']} in {processing_time:.2f} seconds")
            extraction_metrics["successful_extractions"] += 1
        else:
            error_msg = f"Extraction failed for {context['pdf_path']}: {error.message}" if error else f"Extraction failed for {context['pdf_path']}"
            self.logger.error(error_msg)
            extraction_metrics["failed_extractions"] += 1

            if error:
                # Log error details
                error_details = {
                    "message": error.message,
                    "category": error.category,
                    "severity": error.severity,
                    "details": error.details,
                    "stack_trace": error.stack_trace,
                    "timestamp": error.timestamp.isoformat(),
                    "pdf_path": context.get("pdf_path"),
                    "hotel_name": context.get("hotel_name"),
                    "extraction_id": context.get("extraction_id")
                }

                # Update error metrics
                extraction_metrics["errors_by_category"][error.category] += 1
                if context.get("hotel_name"):
                    extraction_metrics["errors_by_hotel"][context.get("hotel_name")] += 1

                # Add to recent errors
                extraction_metrics["recent_errors"].append(error_details)

                # Check if we should send an alert
                if ALERT_CONFIG["enabled"]:
                    self._check_alert_conditions(error, context)

        # Update processing time metrics
        extraction_metrics["total_processing_time"] += processing_time
        extraction_metrics["processing_times"].append(processing_time)

        # Write to JSON log
        self._write_json_log(context, results, error)

    def log_error(self, message: str, category: str = "UNKNOWN",
                 severity: str = "ERROR", details: Dict[str, Any] = None,
                 context: Optional[Dict[str, Any]] = None) -> ExtractionError:
        """
        Log an error

        Args:
            message: Error message
            category: Error category
            severity: Error severity
            details: Additional error details
            context: Extraction context

        Returns:
            ExtractionError object
        """
        error = ExtractionError(message, category, severity, details)

        # Log based on severity
        if severity == "CRITICAL":
            self.logger.critical(f"{ERROR_CATEGORIES[category]}: {message}")
        elif severity == "ERROR":
            self.logger.error(f"{ERROR_CATEGORIES[category]}: {message}")
        elif severity == "WARNING":
            self.logger.warning(f"{ERROR_CATEGORIES[category]}: {message}")
        else:
            self.logger.info(f"{ERROR_CATEGORIES[category]}: {message}")

        # If we have context, update error metrics
        if context:
            extraction_metrics["errors_by_category"][category] += 1
            if context.get("hotel_name"):
                extraction_metrics["errors_by_hotel"][context.get("hotel_name")] += 1

            # Add to recent errors
            error_details = {
                "message": error.message,
                "category": error.category,
                "severity": error.severity,
                "details": error.details,
                "stack_trace": error.stack_trace,
                "timestamp": error.timestamp.isoformat(),
                "pdf_path": context.get("pdf_path"),
                "hotel_name": context.get("hotel_name"),
                "extraction_id": context.get("extraction_id")
            }
            extraction_metrics["recent_errors"].append(error_details)

            # Check if we should send an alert
            if ALERT_CONFIG["enabled"]:
                self._check_alert_conditions(error, context)

        return error

    def log_pattern_match(self, pattern_name: str, pattern_string: str, matched_text: str,
                         context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log a successful pattern match

        Args:
            pattern_name: Name of the pattern
            pattern_string: Regex pattern string
            matched_text: Text that matched the pattern
            context: Extraction context
        """
        self.logger.debug(f"Pattern match: {pattern_name}")
        self.logger.debug(f"  Pattern: {pattern_string}")
        self.logger.debug(f"  Matched: {matched_text}")

        # Add to JSON log if we have context
        if context:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "type": "pattern_match",
                "pattern_name": pattern_name,
                "pattern_string": pattern_string,
                "matched_text": matched_text,
                "context": {k: str(v) if isinstance(v, datetime) else v for k, v in context.items()}
            }

            # Append to JSON log file
            with open(self.json_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')

    def log_column_identification(self, table_index: int, column_index: int, role: str,
                                confidence: float, header_text: str = None,
                                context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log column role identification

        Args:
            table_index: Index of the table
            column_index: Index of the column
            role: Identified role of the column
            confidence: Confidence score (0.0-1.0)
            header_text: Optional header text of the column
            context: Extraction context
        """
        if header_text:
            self.logger.debug(f"Column identification: Table {table_index}, Column {column_index} -> {role} (confidence: {confidence:.2f})")
            self.logger.debug(f"  Header: {header_text}")
        else:
            self.logger.debug(f"Column identification: Table {table_index}, Column {column_index} -> {role} (confidence: {confidence:.2f})")

        # Add to JSON log if we have context
        if context:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "type": "column_identification",
                "table_index": table_index,
                "column_index": column_index,
                "role": role,
                "confidence": confidence,
                "header_text": header_text,
                "context": {k: str(v) if isinstance(v, datetime) else v for k, v in context.items()}
            }

            # Append to JSON log file
            with open(self.json_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')

    def log_data_cleaning(self, original_value: str, cleaned_value: str, cleaning_method: str,
                        context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log data cleaning operation

        Args:
            original_value: Original value before cleaning
            cleaned_value: Value after cleaning
            cleaning_method: Method used for cleaning
            context: Extraction context
        """
        self.logger.debug(f"Data cleaning: {cleaning_method}")
        self.logger.debug(f"  Original: '{original_value}'")
        self.logger.debug(f"  Cleaned:  '{cleaned_value}'")

        # Add to JSON log if we have context
        if context:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "type": "data_cleaning",
                "original_value": original_value,
                "cleaned_value": cleaned_value,
                "cleaning_method": cleaning_method,
                "context": {k: str(v) if isinstance(v, datetime) else v for k, v in context.items()}
            }

            # Append to JSON log file
            with open(self.json_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')

    def log_hotel_specific_handler(self, hotel_name: str, handler_name: str, action: str,
                                 details: Dict[str, Any] = None,
                                 context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log hotel-specific handler action

        Args:
            hotel_name: Name of the hotel
            handler_name: Name of the handler
            action: Action performed by the handler
            details: Additional details about the action
            context: Extraction context
        """
        self.logger.info(f"Hotel-specific handler: {handler_name} for '{hotel_name}'")
        self.logger.info(f"  Action: {action}")

        if details:
            for key, value in details.items():
                self.logger.info(f"  {key}: {value}")

        # Add to JSON log if we have context
        if context:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "type": "hotel_specific_handler",
                "hotel_name": hotel_name,
                "handler_name": handler_name,
                "action": action,
                "details": details or {},
                "context": {k: str(v) if isinstance(v, datetime) else v for k, v in context.items()}
            }

            # Append to JSON log file
            with open(self.json_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')

    def log_extraction_decision(self, decision_type: str, decision: str, reason: str,
                              alternatives: List[str] = None,
                              context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log an extraction decision

        Args:
            decision_type: Type of decision (e.g., 'meal_plan_selection', 'date_range_association')
            decision: The decision that was made
            reason: Reason for the decision
            alternatives: Alternative options that were considered
            context: Extraction context
        """
        self.logger.info(f"Extraction decision: {decision_type}")
        self.logger.info(f"  Decision: {decision}")
        self.logger.info(f"  Reason: {reason}")

        if alternatives:
            self.logger.info(f"  Alternatives: {', '.join(alternatives)}")

        # Add to JSON log if we have context
        if context:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "type": "extraction_decision",
                "decision_type": decision_type,
                "decision": decision,
                "reason": reason,
                "alternatives": alternatives or [],
                "context": {k: str(v) if isinstance(v, datetime) else v for k, v in context.items()}
            }

            # Append to JSON log file
            with open(self.json_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')

    def _write_json_log(self, context: Dict[str, Any],
                       results: Optional[List[Dict[str, Any]]] = None,
                       error: Optional[ExtractionError] = None) -> None:
        """Write structured log entry to JSON file"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "context": {k: str(v) if isinstance(v, datetime) else v for k, v in context.items()},
            "success": context.get("success", False),
            "processing_time": context.get("processing_time", 0)
        }

        if results:
            log_entry["results_count"] = len(results)

        if error:
            log_entry["error"] = {
                "message": error.message,
                "category": error.category,
                "severity": error.severity,
                "details": error.details
            }

        # Append to JSON log file
        with open(self.json_file, 'a') as f:
            f.write(json.dumps(log_entry) + '\n')

    def _check_alert_conditions(self, error: ExtractionError, context: Dict[str, Any]) -> None:
        """Check if we should send an alert based on error conditions"""
        should_alert = False
        alert_message = ""

        # Check error rate
        total = extraction_metrics["successful_extractions"] + extraction_metrics["failed_extractions"]
        if total > 10:  # Only check after we have some data
            error_rate = extraction_metrics["failed_extractions"] / total
            if error_rate > ALERT_THRESHOLDS["error_rate"]:
                should_alert = True
                alert_message += f"High error rate: {error_rate:.2%}\n"

        # Check for critical errors
        if error.severity == "CRITICAL":
            should_alert = True
            alert_message += f"Critical error: {error.message}\n"

        # Check processing time
        if context.get("processing_time", 0) > ALERT_THRESHOLDS["processing_time"]:
            should_alert = True
            alert_message += f"Long processing time: {context['processing_time']:.2f} seconds\n"

        # Send alert if needed
        if should_alert:
            self._send_alert(alert_message, error, context)

    def _send_alert(self, message: str, error: ExtractionError, context: Dict[str, Any]) -> None:
        """Send an alert via configured channels"""
        # Build full alert message
        full_message = f"""
PDF Extraction Alert

{message}

Error Details:
- Category: {ERROR_CATEGORIES[error.category]}
- Severity: {error.severity}
- Message: {error.message}

Context:
- PDF: {context.get('pdf_path')}
- Hotel: {context.get('hotel_name')}
- Extraction ID: {context.get('extraction_id')}
- Processing Time: {context.get('processing_time', 0):.2f} seconds

Timestamp: {datetime.now().isoformat()}
        """

        # Send email alert
        if ALERT_CONFIG["email"]["enabled"]:
            try:
                self._send_email_alert(full_message, error, context)
            except Exception as e:
                self.logger.error(f"Failed to send email alert: {str(e)}")

        # Send Slack alert
        if ALERT_CONFIG["slack"]["enabled"]:
            try:
                self._send_slack_alert(full_message, error, context)
            except Exception as e:
                self.logger.error(f"Failed to send Slack alert: {str(e)}")

    def _send_email_alert(self, message: str, error: ExtractionError, context: Dict[str, Any]) -> None:
        """Send an email alert"""
        config = ALERT_CONFIG["email"]

        # Create message
        msg = MIMEMultipart()
        msg['From'] = config["from_address"]
        msg['To'] = ", ".join(config["to_addresses"])
        msg['Subject'] = f"PDF Extraction Alert: {ERROR_CATEGORIES[error.category]}"

        # Add message body
        msg.attach(MIMEText(message, 'plain'))

        # Send email
        with smtplib.SMTP(config["smtp_server"], config["smtp_port"]) as server:
            server.starttls()
            server.login(config["username"], config["password"])
            server.send_message(msg)

    def _send_slack_alert(self, message: str, error: ExtractionError, context: Dict[str, Any]) -> None:
        """Send a Slack alert"""
        import requests

        # Create Slack message
        slack_message = {
            "text": f"PDF Extraction Alert: {ERROR_CATEGORIES[error.category]}",
            "attachments": [
                {
                    "fallback": message,
                    "color": "danger" if error.severity in ["ERROR", "CRITICAL"] else "warning",
                    "title": f"Error in {context.get('pdf_path', 'Unknown PDF')}",
                    "text": message,
                    "fields": [
                        {
                            "title": "Category",
                            "value": ERROR_CATEGORIES[error.category],
                            "short": True
                        },
                        {
                            "title": "Severity",
                            "value": error.severity,
                            "short": True
                        },
                        {
                            "title": "Hotel",
                            "value": context.get("hotel_name", "Unknown"),
                            "short": True
                        },
                        {
                            "title": "Processing Time",
                            "value": f"{context.get('processing_time', 0):.2f} seconds",
                            "short": True
                        }
                    ],
                    "footer": f"Extraction ID: {context.get('extraction_id', 'Unknown')}",
                    "ts": int(time.time())
                }
            ]
        }

        # Send to Slack
        requests.post(ALERT_CONFIG["slack"]["webhook_url"], json=slack_message)

# Function decorator for error handling
def handle_extraction_errors(logger: ExtractionLogger):
    """Decorator for handling extraction errors"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get context from kwargs or create empty context
            context = kwargs.get("context", {})

            try:
                return func(*args, **kwargs)
            except ExtractionError as e:
                # Already logged, just pass through
                logger.logger.error(f"Caught ExtractionError: {e.message}")
                return None
            except Exception as e:
                # Unexpected error, log and convert to ExtractionError
                error = logger.log_error(
                    message=str(e),
                    category="UNKNOWN",
                    severity="ERROR",
                    details={"args": str(args), "kwargs": str(kwargs)},
                    context=context
                )
                return None
        return wrapper
    return decorator

# Get extraction metrics
def get_extraction_metrics() -> Dict[str, Any]:
    """Get current extraction metrics"""
    metrics = extraction_metrics.copy()

    # Calculate derived metrics
    total = metrics["successful_extractions"] + metrics["failed_extractions"]
    metrics["success_rate"] = metrics["successful_extractions"] / total if total > 0 else 0
    metrics["error_rate"] = metrics["failed_extractions"] / total if total > 0 else 0
    metrics["avg_processing_time"] = metrics["total_processing_time"] / total if total > 0 else 0

    # Calculate uptime
    metrics["uptime_seconds"] = (datetime.now() - metrics["start_time"]).total_seconds()

    # Convert defaultdict to dict for JSON serialization
    metrics["errors_by_category"] = dict(metrics["errors_by_category"])
    metrics["errors_by_hotel"] = dict(metrics["errors_by_hotel"])

    # Convert deque to list for JSON serialization
    metrics["recent_errors"] = list(metrics["recent_errors"])

    return metrics

# Configure alerting
def configure_alerting(enabled: bool = True, email_config: Dict[str, Any] = None,
                      slack_config: Dict[str, Any] = None) -> None:
    """Configure alerting settings"""
    global ALERT_CONFIG

    ALERT_CONFIG["enabled"] = enabled

    if email_config:
        ALERT_CONFIG["email"]["enabled"] = True
        ALERT_CONFIG["email"].update(email_config)

    if slack_config:
        ALERT_CONFIG["slack"]["enabled"] = True
        ALERT_CONFIG["slack"].update(slack_config)

# Reset metrics (for testing)
def reset_metrics() -> None:
    """Reset extraction metrics"""
    global extraction_metrics

    extraction_metrics = {
        "total_pdfs": 0,
        "successful_extractions": 0,
        "failed_extractions": 0,
        "total_processing_time": 0,
        "errors_by_category": defaultdict(int),
        "errors_by_hotel": defaultdict(int),
        "processing_times": [],
        "recent_errors": deque(maxlen=100),
        "start_time": datetime.now()
    }

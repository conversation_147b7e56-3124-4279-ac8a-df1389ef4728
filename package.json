{"name": "tripmilestone-admin-fronend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --port 3001"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@hookform/resolvers": "^3.3.4", "@mobiscroll/react": "npm:@mobiscroll/react-next-trial@^5.34.1", "@mui/material": "^5.15.15", "@mui/x-date-pickers": "^7.1.0", "@mui/x-date-pickers-pro": "^7.1.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-slot": "^1.0.2", "@reduxjs/toolkit": "^2.2.3", "@tanstack/react-query": "^5.28.9", "@tanstack/react-table": "^8.15.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "history": "^5.3.0", "lodash": "^4.17.21", "lucide-react": "^0.363.0", "multiselect-react-dropdown": "^2.0.25", "react": "^18.2.0", "react-date-range": "^2.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.3", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-query": "^3.39.3", "react-redux": "^9.1.1", "react-router-dom": "^6.22.3", "react-tag-input-component": "^2.0.2", "redux-persist": "^6.0.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.30", "@types/react": "^18.2.74", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^18.2.24", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0"}}
import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { deletePackage } from '@/utils/api-functions/deletePackage';
import toast from 'react-hot-toast';

interface PackageDeleteProps {
    packageName:string
    packageId:string
}
export default function PackageDelete(prop:PackageDeleteProps) {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  async function handleDelete(){
    try {
    const resp = await deletePackage(prop.packageId)
    console.log(resp)
    handleClose()
    window.location.href = "/packages"
    } catch (error) {
        toast.error("Error Occurred")
        handleClose()
    }

  }

  return (
    <React.Fragment>
      <button
        onClick={handleClickOpen}
        className="flex items-center justify-center px-4 py-2 rounded-md shadow-sm transition-all duration-200 bg-red-600 hover:bg-red-700 text-white text-sm"
      >
        <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        <span className="font-medium">Delete</span>
      </button>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Delete Package
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you Sure? {prop.packageName} will be deleted
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} autoFocus color='primary'>Cancel</Button>
          <Button onClick={handleDelete}  color='error'>
            Delete Package
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}

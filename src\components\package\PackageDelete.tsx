import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { deletePackage } from '@/utils/api-functions/deletePackage';
import toast from 'react-hot-toast';

interface PackageDeleteProps { 
    packageName:string
    packageId:string
}
export default function PackageDelete(prop:PackageDeleteProps) {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  async function handleDelete(){
    try {
    const resp = await deletePackage(prop.packageId)
    console.log(resp)
    handleClose()
    window.location.href = "/packages"   
    } catch (error) {
        toast.error("Error Occurred")
        handleClose()
    }
    
  }

  return (
    <React.Fragment>
      <Button variant="contained" color='error' onClick={handleClickOpen}>
        Delete
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Delete Package
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you Sure? {prop.packageName} will be deleted
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} autoFocus color='primary'>Cancel</Button>
          <Button onClick={handleDelete}  color='error'>
            Delete Package
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}

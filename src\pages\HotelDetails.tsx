import AddRooms from "@/components/page-components/hotel-details/room/AddRooms";
import AllRooms from "@/components/page-components/hotel-details/room/rooms-table/AllRooms";
import HotelDetailsHeader from "@/components/page-components/hotel-details/HotelDetailsHeader";
import { ReactElement, useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { HotelsType } from "@/types/types";
import { Link, useParams } from "react-router-dom";
import { fetchHotelById } from "@/utils/api-functions/getHotelById";
import { Loader2 } from "lucide-react";

interface Button {
  text: string;
  element: ReactElement;
}

export default function HotelDetails() {
  const { id } = useParams();
  const buttons = [
    { text: "All Rooms", element: <AllRooms/> },
    { text: "Add Rooms", element: <AddRooms /> },
  ];
  const [selectedButton, setSelectedButton] = useState<Button>(buttons[0]);
  const [hotel, setHotel] = useState<HotelsType | undefined>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  async function fetchHotelData() {
    setIsLoading(true);
    setError(null);
    
    try {
      const data: HotelsType | undefined = await fetchHotelById(id as string);
      setHotel(data);
      
      if (!data) {
        setError("Hotel not found");
      }
    } catch (err) {
      console.error("Error fetching hotel:", err);
      setError("Failed to load hotel details");
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    fetchHotelData();
  }, [id]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin text-blue-600 mb-4" />
        <p className="text-gray-600 text-lg">Loading hotel details...</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md w-full text-center">
          <h2 className="text-red-600 text-xl font-semibold mb-2">
            {error}
          </h2>
          <p className="text-gray-600 mb-6">
            The hotel you're looking for might have been removed or is unavailable.
          </p>
          <Link 
            to="/hotels" 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Return to Hotels List
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div>
      <>
        <HotelDetailsHeader hotel={hotel}/>
        <div className="flex">
          {buttons?.map((k) => (
            <button
              onClick={() => setSelectedButton(k)}
              className={cn("m-2 px-2 py-1 rounded-lg ", selectedButton.text === k.text && "underline underline-offset-[14px] decoration-[#27B182] bg-[#F3FFFB] rounded-sm")}
              key={k.text}
            >
              {k.text}
            </button>
          ))}
          <Link to={`/hotels/edit/${hotel?.hotelId}`}>
            <button className="m-2 px-2 py-1 rounded-lg">Update Hotel</button>
          </Link>
        </div>
        <div className="">{selectedButton.element}</div>
      </>
    </div>
  );
}

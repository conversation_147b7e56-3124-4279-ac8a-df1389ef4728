import { Pencil } from "lucide-react";
import DeleteMp from "./DeleteMp";
import { useState } from "react";
import EditMp from "./EditMp";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export interface MealPlanGetData{
    mealPlan: string;
    roomPrice: number;
    adultPrice: number;
    childPrice: number;
    seasonType: string;
    startDate: string[];
    endDate: string[];
    gstPer: number;
    hotelId:string;
    hotelMealId:string;
    hotelRoomId:string;
}
interface MealPlanItemProps {
    roomId: string;
    mealPlanId: string;
    mealPlanType: string;
    startDate?: string[];
    endDate?: string[];
    mp: MealPlanGetData;
}

const MealPlanItem = ({ mealPlanType, roomId, mealPlanId, mp }: MealPlanItemProps) => {
    const [dialogOpen, setDialogOpen] = useState(false);

    // Helper to format date
    const formatDate = (dateStr?: string) => {
        if (!dateStr) return '-';
        const d = new Date(dateStr);
        return d.toLocaleDateString('en-IN', { year: 'numeric', month: 'short', day: 'numeric' });
    };

    return (
        <>
            {mealPlanType ? (
                <div className="">
                    <div className="border w-full rounded p-3 bg-gray-50">
                        <div className="flex justify-between items-center mb-2">
                            <div className="flex items-center space-x-3">
                                <h1 className="text-slate-600 font-medium text-base">
                                    {mealPlanType.toUpperCase()}
                                </h1>
                            </div>
                            <div className="flex items-center gap-2">
                                <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                                    <DialogTrigger asChild>
                                        <Button 
                                            variant="outline" 
                                            size="sm"
                                            className="flex items-center gap-1.5 text-blue-600 border-blue-200 hover:bg-blue-50"
                                        >
                                            <Pencil size={14} /> Edit
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
                                        <DialogHeader>
                                            <DialogTitle className="text-xl font-semibold mb-4">
                                                Edit {mealPlanType.toUpperCase()} Meal Plan
                                            </DialogTitle>
                                        </DialogHeader>
                                        <EditMp mp={mp} setEdit={setDialogOpen} />
                                    </DialogContent>
                                </Dialog>
                                <DeleteMp roomId={roomId} mealPlanId={mealPlanId} />
                            </div>
                        </div>
                        {/* Date Ranges Table */}
                        {mp.startDate && mp.endDate && mp.startDate.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="min-w-full text-sm border rounded">
                                    <thead className="bg-blue-50">
                                        <tr>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Start Date</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">End Date</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Room Price</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Adult Price</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Child Price</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Season</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">GST %</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {mp.startDate.map((start, idx) => (
                                            <tr key={idx} className="border-b last:border-b-0">
                                                <td className="px-3 py-2">{formatDate(start)}</td>
                                                <td className="px-3 py-2">{formatDate(mp.endDate[idx])}</td>
                                                <td className="px-3 py-2">₹{mp.roomPrice}</td>
                                                <td className="px-3 py-2">{mp.adultPrice ? `₹${mp.adultPrice}` : '-'}</td>
                                                <td className="px-3 py-2">{mp.childPrice ? `₹${mp.childPrice}` : '-'}</td>
                                                <td className="px-3 py-2">{mp.seasonType || '-'}</td>
                                                <td className="px-3 py-2">{mp.gstPer ? `${mp.gstPer}%` : '-'}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-gray-500 text-sm italic">No date ranges set for this meal plan.</div>
                        )}
                    </div>
                </div>
            ) : (
                <>
                    No meal plans
                </>
            )}
        </>
    );
};

export default MealPlanItem;

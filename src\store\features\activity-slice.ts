import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ActivityFormState {
  activityName: string;
  description: string;
  destinationId: string;
  destinationName: string;
  allTags: string[];
  price: number;
  duration: number;
  maxParticipants: number;
  minParticipants: number;
  ageRestriction: number;
  dayType: string;
  level: string;
  isPrivate: string;
  loading: boolean;
}

const initialState: ActivityFormState = {
  activityName: "",
  description: "",
  destinationId: "",
  destinationName: "",
  allTags: [],
  price: 0,
  duration: 0,
  maxParticipants: 0,
  minParticipants: 0,
  ageRestriction: 0,
  dayType: "",
  level: "",
  isPrivate: "0", 
  loading: false,
};

const activityFormSlice = createSlice({
  name: "activityForm",
  initialState,
  reducers: {
    setField: <K extends keyof ActivityFormState>(
      state: ActivityFormState,
      action: PayloadAction<{ field: K; value: ActivityFormState[K] }>
    ) => {
      state[action.payload.field] = action.payload.value;
    },
  },
});

export const { setField } = activityFormSlice.actions;
export default activityFormSlice.reducer;

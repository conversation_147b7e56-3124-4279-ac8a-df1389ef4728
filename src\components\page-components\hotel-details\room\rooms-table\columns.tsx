
import { ColumnDef } from "@tanstack/react-table";

import { Eye, MoreHorizontal, Pencil, Trash2Icon, User } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from "@radix-ui/react-alert-dialog";
import { AlertDialogFooter } from "@/components/ui/alert-dialog";
import { RoomData } from "../AddRoom";

export const columns:ColumnDef<RoomData>[] = [
    {
        accessorKey : "hotelRoomType",
        header : () =>{
            return (
                <h1>
                     Room Name
                </h1>
            )
        },
 
    cell: ({row})=>{
        const roomName = row.getValue("hotelRoomType")
        return (
            <>
            {roomName}
            </>
        )

    }
},
{
    accessorKey : "hotelName",
    header : () =>{
        return (
            <h1>
                 Hotel Name
            </h1>
        )
    },

cell: ({row})=>{
    const hotelName = row.getValue("hotelName")
    return (
        <>
        {hotelName}
        </>
    )

}
},
{
    accessorKey : "hotelRoomType",
    header : () =>{
        return (
            <h1>
                 Room Type
            </h1>
        )
    },

cell: ({row})=>{
    const hotelRoomType = row.getValue("hotelRoomType")
    return (
        <>
        {hotelRoomType}
        </>
    )

},

},
{
    accessorKey : "roomCapacity",
    header : () =>{
        return (
            <h1 className="text-center">
                 Room Capacity
            </h1>
        )
    },

cell: ({row})=>{
    const roomCapacity : number = row.getValue("roomCapacity")
    return (
        <>
       <div className="flex justify-center items-center gap-2">{roomCapacity}<User size={15}/> </div>
        </>
    )

},


},
{
    id : 'Actions',
    header : () =>{
        return (
            <>
            Actions
            </>
        )
    },
    cell : ({row  }) =>{
        const {hotelRoomId,hotelId} = row.original
        return ( <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant={"ghost"} className="h-4 w-8 p-0 bg-white">
              <span className="sr-only"> Open menu</span>
              <MoreHorizontal className="h-4 w-4"/>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
          <Link to={`/hotels/${hotelId}/room/${hotelRoomId}`}>
                <DropdownMenuItem className="flex items-center gap-2">
                  <Eye className="h-4 w-4 mr-2 text-green-500" />
                  View
                </DropdownMenuItem>
              </Link>
              <Link to={`/hotels/edit/${hotelRoomId}`}>
                <DropdownMenuItem className="flex items-center gap-2">
                  <Pencil className="h-4 w-4 mr-2 text-slate-700" />
                  Edit
                </DropdownMenuItem>
              </Link>
              <DropdownMenuItem itemScope={false} >
            
           <AlertDialog >
               <AlertDialogTrigger onClick={(event) => {
   event.stopPropagation();
 }} className="flex items-center gap-2">
               <Trash2Icon className="h-4 w-4 mr-2 text-red-600" />Delete
               </AlertDialogTrigger>
               <AlertDialogContent>
                   <h1 className="text-slate-600 font-medium">Are you sure ?</h1>
                   <AlertDialogFooter>
                   <AlertDialogCancel>
                       Cancel
                   </AlertDialogCancel>
                   <AlertDialogAction>
                      Yes , Delete
                   </AlertDialogAction>
               </AlertDialogFooter>
               </AlertDialogContent>
             
           </AlertDialog>
              
              
                       
                   </DropdownMenuItem>


             
            </DropdownMenuContent>
  
        </DropdownMenu>)
       }
    
}

]
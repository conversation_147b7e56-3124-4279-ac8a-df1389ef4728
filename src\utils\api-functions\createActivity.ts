/* eslint-disable @typescript-eslint/no-explicit-any */
import toast from "react-hot-toast";
import api from './auth'

export async function createActivity( data : any) {
    try {
        const response = await api.post(
            "admin/activity",
          data
        );
        return Promise.resolve(response);
      } catch (error) {
        toast.error('An Error occurred');
        return Promise.reject('error');
      }
}
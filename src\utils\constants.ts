// API URL - Environment-aware configuration
const isLocalDevelopment = typeof window !== 'undefined' && 
  (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

export const API_URL = isLocalDevelopment 
  ? 'http://localhost:3001'  // Local development API
  : (import.meta.env.VITE_API_URL || 'https://api.tripxplo.com/v1');

// Default pagination
export const DEFAULT_PAGE_SIZE = 10;

// Date formats
export const DATE_FORMAT = 'yyyy-MM-dd';
export const DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';
export const DISPLAY_DATE_FORMAT = 'MMM d, yyyy';

// Status colors
export const STATUS_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  approved: 'bg-green-100 text-green-800 border-green-200',
  rejected: 'bg-red-100 text-red-800 border-red-200',
  active: 'bg-green-100 text-green-800 border-green-200',
  inactive: 'bg-gray-100 text-gray-800 border-gray-200',
  draft: 'bg-blue-100 text-blue-800 border-blue-200',
  published: 'bg-green-100 text-green-800 border-green-200'
};

// Meal plan types
export const MEAL_PLAN_TYPES = [
  { id: 'cp', name: 'CP - Continental Plan (Breakfast only)' },
  { id: 'map', name: 'MAP - Modified American Plan (Breakfast + Dinner)' },
  { id: 'ap', name: 'AP - American Plan (All meals)' },
  { id: 'ep', name: 'EP - European Plan (Room only)' }
];

// Extra charge types
export const EXTRA_CHARGE_TYPES = [
  { id: 'extra_adult_cp', name: 'Extra Adult (CP)' },
  { id: 'extra_adult_map', name: 'Extra Adult (MAP)' },
  { id: 'extra_adult_ap', name: 'Extra Adult (AP)' },
  { id: 'extra_child_with_bed', name: 'Extra Child with Bed' },
  { id: 'extra_child_without_bed', name: 'Extra Child without Bed' },
  { id: 'map_supplement', name: 'MAP Supplement' },
  { id: 'ap_supplement', name: 'AP Supplement' }
];

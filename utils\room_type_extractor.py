#!/usr/bin/env python3
"""
Room Type Extractor for Tariff Extraction

This module provides enhanced room type extraction functionality,
with improved cleaning and standardization of room names.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Union, Set

# Configure logging
logger = logging.getLogger('tariff_extractor')

class RoomTypeExtractor:
    """Enhanced room type extractor with improved cleaning and standardization"""
    
    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the room type extractor
        
        Args:
            config: Optional configuration object
        """
        self.config = config
        
        # Default patterns for room type extraction
        self.patterns = {
            'room_type': [
                r'(?:deluxe|super|premium|executive|standard|suite|cottage|villa)',
                r'(?:garden|mountain|sea|lake|pool|beach|city)\s+(?:view|facing)',
                r'(?:plantation|farm)\s+view',
                r'(?:family|presidential|royal|luxury)\s+(?:room|suite)',
                r'(?:single|double|twin|triple|quad)\s+(?:room|occupancy)'
            ],
            'room_type_exclusions': [
                r'(?:extra|additional)\s+(?:adult|child|person|infant)',
                r'(?:supplement|surcharge)',
                r'(?:meal|food|dining)\s+(?:plan|package|option)',
                r'(?:breakfast|lunch|dinner)',
                r'(?:cp|map|ap|ep)',
                r'(?:continental|modified|american|european)'
            ],
            'occupancy_info': [
                r'\(\s*\d+\s*(?:pax|person|adult|occupancy)\s*\)',
                r'\(\s*(?:single|double|twin|triple|quad)\s*(?:occupancy)?\s*\)',
                r'\(\s*(?:max|maximum)\s*\d+\s*(?:pax|person|adult|occupancy)\s*\)',
                r'\d+\s*(?:pax|person|adult|occupancy)'
            ],
            'description_info': [
                r'\(.*?\)',
                r'-.*?(?=-|$)',
                r'with\s+.*?(?=\s|$)'
            ]
        }
        
        # Common room type variations for standardization
        self.room_type_variations = {
            'deluxe': ['dlx', 'delux', 'deluxe'],
            'super deluxe': ['super dlx', 'super delux', 'super deluxe', 'superior deluxe'],
            'premium': ['premier', 'premium', 'prem'],
            'executive': ['exec', 'executive'],
            'standard': ['std', 'standard'],
            'suite': ['ste', 'suite'],
            'cottage': ['cot', 'cottage'],
            'villa': ['villa', 'vil'],
            'garden view': ['garden view', 'garden facing', 'garden side'],
            'mountain view': ['mountain view', 'mountain facing', 'hill view', 'hill facing'],
            'sea view': ['sea view', 'sea facing', 'ocean view', 'ocean facing'],
            'lake view': ['lake view', 'lake facing', 'water view', 'water facing'],
            'pool view': ['pool view', 'pool facing', 'pool side'],
            'beach view': ['beach view', 'beach facing', 'beach side'],
            'city view': ['city view', 'city facing']
        }
        
        # Load patterns from config if available
        if config and hasattr(config, 'get_full_config'):
            config_patterns = config.get_full_config().get('room_type_patterns', {})
            for category, patterns in config_patterns.items():
                if category in self.patterns:
                    self.patterns[category].extend(patterns)
                else:
                    self.patterns[category] = patterns
            
            # Load room type variations from config if available
            config_variations = config.get_full_config().get('room_type_variations', {})
            for standard, variations in config_variations.items():
                if standard in self.room_type_variations:
                    self.room_type_variations[standard].extend(variations)
                else:
                    self.room_type_variations[standard] = variations
        
        # Compile regex patterns for better performance
        self.compiled_patterns = {}
        for category, patterns in self.patterns.items():
            self.compiled_patterns[category] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        
        # Statistics for extraction operations
        self.extraction_stats = {
            'total_extractions': 0,
            'successful_extractions': 0,
            'standardized_room_types': 0,
            'cleaned_room_types': 0,
            'failed_extractions': 0
        }
    
    def extract_room_types(self, table: List[List[str]], room_col_idx: int, 
                          data_row_indices: List[int]) -> List[str]:
        """
        Extract room types from a table
        
        Args:
            table: Table to extract room types from
            room_col_idx: Index of the room type column
            data_row_indices: Indices of data rows
            
        Returns:
            List of extracted room types
        """
        room_types = []
        
        for row_idx in data_row_indices:
            if row_idx < len(table) and room_col_idx < len(table[row_idx]):
                room_text = str(table[row_idx][room_col_idx])
                
                # Skip empty cells
                if not room_text.strip():
                    continue
                
                # Skip cells that match exclusion patterns
                if any(pattern.search(room_text) for pattern in self.compiled_patterns['room_type_exclusions']):
                    continue
                
                # Clean and standardize room type
                cleaned_room_type = self.clean_room_type(room_text)
                
                # Skip empty or invalid room types
                if not cleaned_room_type:
                    continue
                
                # Add to list if not already present
                if cleaned_room_type not in room_types:
                    room_types.append(cleaned_room_type)
                    logger.debug(f"Extracted room type: '{cleaned_room_type}' from '{room_text}'")
        
        # Update statistics
        self.extraction_stats['total_extractions'] += 1
        if room_types:
            self.extraction_stats['successful_extractions'] += 1
        else:
            self.extraction_stats['failed_extractions'] += 1
        
        return room_types
    
    def extract_room_types_from_text(self, text: str) -> List[str]:
        """
        Extract room types from text
        
        Args:
            text: Text to extract room types from
            
        Returns:
            List of extracted room types
        """
        room_types = []
        
        # Look for room type patterns in text
        for pattern in self.compiled_patterns['room_type']:
            matches = pattern.findall(text)
            for match in matches:
                # Clean and standardize room type
                cleaned_room_type = self.clean_room_type(match)
                
                # Skip empty or invalid room types
                if not cleaned_room_type:
                    continue
                
                # Add to list if not already present
                if cleaned_room_type not in room_types:
                    room_types.append(cleaned_room_type)
                    logger.debug(f"Extracted room type from text: '{cleaned_room_type}' from '{match}'")
        
        # Update statistics
        self.extraction_stats['total_extractions'] += 1
        if room_types:
            self.extraction_stats['successful_extractions'] += 1
        else:
            self.extraction_stats['failed_extractions'] += 1
        
        return room_types
    
    def clean_room_type(self, room_text: str) -> str:
        """
        Clean and standardize room type
        
        Args:
            room_text: Room type text to clean
            
        Returns:
            Cleaned and standardized room type
        """
        if not room_text:
            return ""
        
        # Remove leading/trailing whitespace
        room_text = room_text.strip()
        
        # Remove bullet points and other special characters
        room_text = re.sub(r'[•\uf0b7\u2022]', '', room_text).strip()
        
        # Remove occupancy information
        for pattern in self.compiled_patterns['occupancy_info']:
            room_text = pattern.sub('', room_text).strip()
        
        # Remove description information if it doesn't contain key room type words
        for pattern in self.compiled_patterns['description_info']:
            match = pattern.search(room_text)
            if match:
                description = match.group(0)
                # Only remove if it doesn't contain key room type words
                if not any(re.search(r'deluxe|super|premium|executive|standard|suite|cottage|villa|view|facing', description, re.I)):
                    room_text = pattern.sub('', room_text).strip()
        
        # Remove extra whitespace
        room_text = re.sub(r'\s+', ' ', room_text).strip()
        
        # Standardize room type
        standardized_room_type = self._standardize_room_type(room_text)
        
        # Update statistics
        if standardized_room_type != room_text:
            self.extraction_stats['standardized_room_types'] += 1
        
        self.extraction_stats['cleaned_room_types'] += 1
        
        return standardized_room_type
    
    def _standardize_room_type(self, room_text: str) -> str:
        """
        Standardize room type based on common variations
        
        Args:
            room_text: Room type text to standardize
            
        Returns:
            Standardized room type
        """
        # Convert to lowercase for comparison
        room_text_lower = room_text.lower()
        
        # Check if the room type matches any known variation
        for standard, variations in self.room_type_variations.items():
            for variation in variations:
                # Check for exact match
                if room_text_lower == variation:
                    return standard.title()
                
                # Check for match at the beginning
                if room_text_lower.startswith(variation + ' '):
                    return (standard + room_text_lower[len(variation):]).title()
                
                # Check for match at the end
                if room_text_lower.endswith(' ' + variation):
                    return (room_text_lower[:-len(variation)] + standard).title()
        
        # If no standardization was applied, return the original text with title case
        return room_text.title()
    
    def get_extraction_stats(self) -> Dict[str, int]:
        """
        Get statistics about extraction operations
        
        Returns:
            Dictionary of extraction statistics
        """
        return self.extraction_stats

#!/usr/bin/env python3
"""
Table Structure Analyzer for Tariff Extraction

This module provides enhanced table structure analysis functionality,
with improved column role identification and data row detection.
"""

import re
import logging
from enum import Enum
from typing import Dict, List, Tuple, Any, Optional, Union, Set

# Configure logging
logger = logging.getLogger('tariff_extractor')

class ColumnRole(Enum):
    """Enum for column roles in a table"""
    UNKNOWN = 0
    ROOM_TYPE = 1
    PRICE_CP = 2
    PRICE_MAP = 3
    PRICE_AP = 4
    PRICE_EP = 5
    PRICE_RACK = 6
    PRICE_NET = 7
    PRICE_SPECIAL = 8
    PRICE_GENERIC = 9
    MEAL_PLAN = 10
    DATE_RANGE = 11
    SEASON = 12
    OCCUPANCY = 13
    EXTRA_CHARGE = 14
    SUPPLEMENT = 15
    NOTES = 16
    INDEX = 17

class RowType(Enum):
    """Enum for row types in a table"""
    UNKNOWN = 0
    HEADER = 1
    SUBHEADER = 2
    DATA = 3
    FOOTER = 4
    NOTES = 5
    EMPTY = 6
    SEPARATOR = 7

class TableStructureAnalyzer:
    """Enhanced table structure analyzer with improved column role identification"""
    
    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the table structure analyzer
        
        Args:
            config: Optional configuration object
        """
        self.config = config
        
        # Default patterns for column role identification
        self.patterns = {
            'room_type': [
                r'(?:room|accommodation)\s+(?:type|category)',
                r'(?:deluxe|super|premium|executive|standard|suite|cottage|villa)',
                r'(?:garden|mountain|sea|lake|pool|beach|city)\s+(?:view|facing)',
                r'(?:type|category|room|accommodation)'
            ],
            'price_cp': [
                r'(?:cp|continental|bed\s*&?\s*breakfast)\s+(?:rate|price|tariff|cost)',
                r'(?:rate|price|tariff|cost).*?(?:cp|continental|bed\s*&?\s*breakfast)',
                r'(?:cp|continental|bed\s*&?\s*breakfast)'
            ],
            'price_map': [
                r'(?:map|modified|half\s*board)\s+(?:rate|price|tariff|cost)',
                r'(?:rate|price|tariff|cost).*?(?:map|modified|half\s*board)',
                r'(?:map|modified|half\s*board)'
            ],
            'price_ap': [
                r'(?:ap|american|full\s*board|all\s*inclusive)\s+(?:rate|price|tariff|cost)',
                r'(?:rate|price|tariff|cost).*?(?:ap|american|full\s*board|all\s*inclusive)',
                r'(?:ap|american|full\s*board|all\s*inclusive)'
            ],
            'price_ep': [
                r'(?:ep|european|room\s*only)\s+(?:rate|price|tariff|cost)',
                r'(?:rate|price|tariff|cost).*?(?:ep|european|room\s*only)',
                r'(?:ep|european|room\s*only)'
            ],
            'price_rack': [
                r'(?:rack|published|standard|full|display|list|brochure)\s+(?:rate|price|tariff|cost)',
                r'(?:rate|price|tariff|cost).*?(?:rack|published|standard|full|display|list|brochure)',
                r'(?:rack|published|standard|full|display|list|brochure)'
            ],
            'price_net': [
                r'(?:net|special|spl|contract|agent|corporate|wholesale|discounted|tour|operator|travel|negotiated)\s+(?:rate|price|tariff|cost)',
                r'(?:rate|price|tariff|cost).*?(?:net|special|spl|contract|agent|corporate|wholesale|discounted|tour|operator|travel|negotiated)',
                r'(?:net|special|spl|contract|agent|corporate|wholesale|discounted|tour|operator|travel|negotiated)'
            ],
            'price_generic': [
                r'(?:rate|price|tariff|cost)',
                r'(?:rs\.?|inr|₹)'
            ],
            'meal_plan': [
                r'(?:meal|food|dining)\s+(?:plan|package|option)',
                r'(?:cp|map|ap|ep)',
                r'(?:continental|modified|american|european)',
                r'(?:breakfast|lunch|dinner)'
            ],
            'date_range': [
                r'(?:date|period|validity|from|to)',
                r'(?:valid|applicable)',
                r'(?:\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4})'
            ],
            'season': [
                r'(?:season|period)',
                r'(?:summer|winter|monsoon|peak|off-peak|shoulder)'
            ],
            'occupancy': [
                r'(?:occupancy|sharing|pax)',
                r'(?:single|double|twin|triple|quad)'
            ],
            'extra_charge': [
                r'(?:extra|additional)\s+(?:charge|cost|fee)',
                r'(?:extra|additional)\s+(?:adult|child|person|infant)',
                r'(?:supplement|surcharge)'
            ]
        }
        
        # Load patterns from config if available
        if config and hasattr(config, 'get_full_config'):
            config_patterns = config.get_full_config().get('column_role_patterns', {})
            for category, patterns in config_patterns.items():
                if category in self.patterns:
                    self.patterns[category].extend(patterns)
                else:
                    self.patterns[category] = patterns
        
        # Compile regex patterns for better performance
        self.compiled_patterns = {}
        for category, patterns in self.patterns.items():
            self.compiled_patterns[category] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        
        # Statistics for analysis operations
        self.analysis_stats = {
            'total_tables_analyzed': 0,
            'total_columns_analyzed': 0,
            'total_rows_analyzed': 0,
            'room_type_columns_found': 0,
            'price_columns_found': 0,
            'meal_plan_columns_found': 0,
            'date_columns_found': 0,
            'header_rows_found': 0,
            'data_rows_found': 0
        }
    
    def analyze_table(self, table: List[List[str]]) -> Dict[str, Any]:
        """
        Analyze table structure to identify column roles and row types
        
        Args:
            table: Table to analyze (list of rows, each row is a list of cells)
            
        Returns:
            Dictionary containing analysis results
        """
        if not table or len(table) < 2:  # Need at least header + one data row
            return {
                'column_roles': {},
                'row_types': {},
                'header_row_index': -1,
                'data_row_indices': [],
                'confidence': 0.0
            }
        
        self.analysis_stats['total_tables_analyzed'] += 1
        
        # Analyze column roles
        column_roles = self._identify_column_roles(table)
        
        # Analyze row types
        row_types = self._identify_row_types(table, column_roles)
        
        # Find header row
        header_row_index = -1
        for i, row_type in row_types.items():
            if row_type == RowType.HEADER:
                header_row_index = i
                break
        
        # Find data rows
        data_row_indices = []
        for i, row_type in row_types.items():
            if row_type == RowType.DATA:
                data_row_indices.append(i)
        
        # Calculate confidence score
        confidence = self._calculate_confidence(column_roles, row_types)
        
        # Update statistics
        self.analysis_stats['total_columns_analyzed'] += len(column_roles)
        self.analysis_stats['total_rows_analyzed'] += len(row_types)
        self.analysis_stats['header_rows_found'] += 1 if header_row_index >= 0 else 0
        self.analysis_stats['data_rows_found'] += len(data_row_indices)
        
        # Count column types for statistics
        for role in column_roles.values():
            if role == ColumnRole.ROOM_TYPE:
                self.analysis_stats['room_type_columns_found'] += 1
            elif role in [ColumnRole.PRICE_CP, ColumnRole.PRICE_MAP, ColumnRole.PRICE_AP, 
                         ColumnRole.PRICE_EP, ColumnRole.PRICE_RACK, ColumnRole.PRICE_NET, 
                         ColumnRole.PRICE_SPECIAL, ColumnRole.PRICE_GENERIC]:
                self.analysis_stats['price_columns_found'] += 1
            elif role == ColumnRole.MEAL_PLAN:
                self.analysis_stats['meal_plan_columns_found'] += 1
            elif role == ColumnRole.DATE_RANGE:
                self.analysis_stats['date_columns_found'] += 1
        
        return {
            'column_roles': {col: role.name for col, role in column_roles.items()},
            'row_types': {row: type.name for row, type in row_types.items()},
            'header_row_index': header_row_index,
            'data_row_indices': data_row_indices,
            'confidence': confidence
        }
    
    def _identify_column_roles(self, table: List[List[str]]) -> Dict[int, ColumnRole]:
        """
        Identify the role of each column in the table
        
        Args:
            table: Table to analyze
            
        Returns:
            Dictionary mapping column indices to column roles
        """
        column_roles = {}
        
        # Assume first row is header
        if len(table) > 0:
            header_row = table[0]
            
            # First pass: identify columns based on header text
            for col_idx, header in enumerate(header_row):
                header_text = str(header).lower()
                role = self._get_column_role_from_header(header_text)
                column_roles[col_idx] = role
            
            # Second pass: if we couldn't identify some columns from headers,
            # try to infer from column content
            for col_idx, role in list(column_roles.items()):
                if role == ColumnRole.UNKNOWN:
                    inferred_role = self._infer_column_role_from_content(table, col_idx)
                    column_roles[col_idx] = inferred_role
            
            # Third pass: if we have multiple price columns but couldn't differentiate them,
            # try to differentiate based on context
            price_columns = [col_idx for col_idx, role in column_roles.items() 
                            if role == ColumnRole.PRICE_GENERIC]
            
            if len(price_columns) > 1:
                # Try to differentiate price columns
                self._differentiate_price_columns(table, column_roles, price_columns)
            
            # Fourth pass: if we have multiple unknown columns, try to identify them based on position
            unknown_columns = [col_idx for col_idx, role in column_roles.items() 
                              if role == ColumnRole.UNKNOWN]
            
            if unknown_columns:
                # First column is often room type
                if 0 in unknown_columns and not any(role == ColumnRole.ROOM_TYPE for role in column_roles.values()):
                    column_roles[0] = ColumnRole.ROOM_TYPE
                    unknown_columns.remove(0)
                
                # Columns next to price columns are often price columns too
                for col_idx in list(unknown_columns):
                    if col_idx - 1 in column_roles and column_roles[col_idx - 1] in [
                        ColumnRole.PRICE_CP, ColumnRole.PRICE_MAP, ColumnRole.PRICE_AP, 
                        ColumnRole.PRICE_EP, ColumnRole.PRICE_GENERIC
                    ]:
                        column_roles[col_idx] = ColumnRole.PRICE_GENERIC
                        unknown_columns.remove(col_idx)
        
        return column_roles
    
    def _get_column_role_from_header(self, header_text: str) -> ColumnRole:
        """
        Determine column role based on header text
        
        Args:
            header_text: Header text to analyze
            
        Returns:
            Column role
        """
        # Check for room type column
        for pattern in self.compiled_patterns['room_type']:
            if pattern.search(header_text):
                return ColumnRole.ROOM_TYPE
        
        # Check for price columns with specific meal plans
        for pattern in self.compiled_patterns['price_cp']:
            if pattern.search(header_text):
                return ColumnRole.PRICE_CP
        
        for pattern in self.compiled_patterns['price_map']:
            if pattern.search(header_text):
                return ColumnRole.PRICE_MAP
        
        for pattern in self.compiled_patterns['price_ap']:
            if pattern.search(header_text):
                return ColumnRole.PRICE_AP
        
        for pattern in self.compiled_patterns['price_ep']:
            if pattern.search(header_text):
                return ColumnRole.PRICE_EP
        
        # Check for rack rate column
        for pattern in self.compiled_patterns['price_rack']:
            if pattern.search(header_text):
                return ColumnRole.PRICE_RACK
        
        # Check for net/special rate column
        for pattern in self.compiled_patterns['price_net']:
            if pattern.search(header_text):
                return ColumnRole.PRICE_NET
        
        # Check for generic price column
        for pattern in self.compiled_patterns['price_generic']:
            if pattern.search(header_text):
                return ColumnRole.PRICE_GENERIC
        
        # Check for meal plan column
        for pattern in self.compiled_patterns['meal_plan']:
            if pattern.search(header_text):
                return ColumnRole.MEAL_PLAN
        
        # Check for date range column
        for pattern in self.compiled_patterns['date_range']:
            if pattern.search(header_text):
                return ColumnRole.DATE_RANGE
        
        # Check for season column
        for pattern in self.compiled_patterns['season']:
            if pattern.search(header_text):
                return ColumnRole.SEASON
        
        # Check for occupancy column
        for pattern in self.compiled_patterns['occupancy']:
            if pattern.search(header_text):
                return ColumnRole.OCCUPANCY
        
        # Check for extra charge column
        for pattern in self.compiled_patterns['extra_charge']:
            if pattern.search(header_text):
                return ColumnRole.EXTRA_CHARGE
        
        # If we couldn't identify the column, return unknown
        return ColumnRole.UNKNOWN
    
    def _infer_column_role_from_content(self, table: List[List[str]], col_idx: int) -> ColumnRole:
        """
        Infer column role based on column content
        
        Args:
            table: Table to analyze
            col_idx: Column index
            
        Returns:
            Inferred column role
        """
        # Skip header row
        data_rows = table[1:min(6, len(table))]
        
        # Extract column values
        column_values = []
        for row in data_rows:
            if col_idx < len(row):
                column_values.append(str(row[col_idx]))
        
        if not column_values:
            return ColumnRole.UNKNOWN
        
        # Join values for pattern matching
        column_text = ' '.join(column_values).lower()
        
        # Check for room type column
        if re.search(r'deluxe|super|premium|executive|standard|suite|cottage|villa|view', column_text, re.I):
            return ColumnRole.ROOM_TYPE
        
        # Check for price column
        if all(re.search(r'\d{3,}', val) for val in column_values if val.strip()):
            return ColumnRole.PRICE_GENERIC
        
        # Check for meal plan column
        if re.search(r'cp|map|ap|ep|continental|modified|american|european', column_text, re.I):
            return ColumnRole.MEAL_PLAN
        
        # Check for date column
        if re.search(r'\d{1,2}[./\-]\d{1,2}[./\-]\d{2,4}', column_text, re.I):
            return ColumnRole.DATE_RANGE
        
        # If we couldn't identify the column, return unknown
        return ColumnRole.UNKNOWN
    
    def _differentiate_price_columns(self, table: List[List[str]], column_roles: Dict[int, ColumnRole], 
                                    price_columns: List[int]) -> None:
        """
        Differentiate between different types of price columns
        
        Args:
            table: Table to analyze
            column_roles: Dictionary mapping column indices to column roles
            price_columns: List of column indices identified as price columns
        """
        if len(price_columns) <= 1:
            return
        
        # Check if we can differentiate based on header text
        if len(table) > 0:
            header_row = table[0]
            
            # Look for meal plan indicators in header text
            for col_idx in price_columns:
                if col_idx < len(header_row):
                    header_text = str(header_row[col_idx]).lower()
                    
                    # Check for specific meal plans
                    if re.search(r'cp|continental|bed\s*&?\s*breakfast', header_text, re.I):
                        column_roles[col_idx] = ColumnRole.PRICE_CP
                    elif re.search(r'map|modified|half\s*board', header_text, re.I):
                        column_roles[col_idx] = ColumnRole.PRICE_MAP
                    elif re.search(r'ap|american|full\s*board|all\s*inclusive', header_text, re.I):
                        column_roles[col_idx] = ColumnRole.PRICE_AP
                    elif re.search(r'ep|european|room\s*only', header_text, re.I):
                        column_roles[col_idx] = ColumnRole.PRICE_EP
                    
                    # Check for rack vs. net/special rates
                    if re.search(r'rack|published|standard|full|display|list|brochure', header_text, re.I):
                        column_roles[col_idx] = ColumnRole.PRICE_RACK
                    elif re.search(r'net|special|spl|contract|agent|corporate|wholesale|discounted|tour|operator|travel|negotiated', header_text, re.I):
                        column_roles[col_idx] = ColumnRole.PRICE_NET
        
        # If we still have generic price columns, try to differentiate based on values
        generic_price_columns = [col_idx for col_idx in price_columns 
                               if column_roles[col_idx] == ColumnRole.PRICE_GENERIC]
        
        if len(generic_price_columns) > 1:
            # Compare price values - rack rates are typically higher than net rates
            avg_prices = {}
            for col_idx in generic_price_columns:
                prices = []
                for row_idx in range(1, min(6, len(table))):
                    if row_idx < len(table) and col_idx < len(table[row_idx]):
                        cell_text = str(table[row_idx][col_idx])
                        price_match = re.search(r'[\d,]+\.?\d*', cell_text)
                        if price_match:
                            try:
                                price = float(price_match.group(0).replace(',', ''))
                                prices.append(price)
                            except ValueError:
                                pass
                
                if prices:
                    avg_prices[col_idx] = sum(prices) / len(prices)
            
            # If we have at least two columns with prices
            if len(avg_prices) >= 2:
                # Sort columns by average price
                sorted_cols = sorted(avg_prices.keys(), key=lambda x: avg_prices[x], reverse=True)
                
                # Highest price column is likely rack rate
                column_roles[sorted_cols[0]] = ColumnRole.PRICE_RACK
                
                # Lower price columns are likely net/special rates
                for col_idx in sorted_cols[1:]:
                    column_roles[col_idx] = ColumnRole.PRICE_NET
    
    def _identify_row_types(self, table: List[List[str]], column_roles: Dict[int, ColumnRole]) -> Dict[int, RowType]:
        """
        Identify the type of each row in the table
        
        Args:
            table: Table to analyze
            column_roles: Dictionary mapping column indices to column roles
            
        Returns:
            Dictionary mapping row indices to row types
        """
        row_types = {}
        
        # First row is usually header
        if len(table) > 0:
            row_types[0] = RowType.HEADER
        
        # Analyze remaining rows
        for row_idx in range(1, len(table)):
            row = table[row_idx]
            
            # Skip empty rows
            if not row or all(not str(cell).strip() for cell in row):
                row_types[row_idx] = RowType.EMPTY
                continue
            
            # Check if this is a separator row
            if all(not str(cell).strip() or re.match(r'^[-=_*]+$', str(cell).strip()) for cell in row):
                row_types[row_idx] = RowType.SEPARATOR
                continue
            
            # Check if this is a subheader row
            if self._is_subheader_row(row, column_roles):
                row_types[row_idx] = RowType.SUBHEADER
                continue
            
            # Check if this is a footer row
            if row_idx == len(table) - 1 and self._is_footer_row(row):
                row_types[row_idx] = RowType.FOOTER
                continue
            
            # Check if this is a notes row
            if self._is_notes_row(row):
                row_types[row_idx] = RowType.NOTES
                continue
            
            # If none of the above, assume it's a data row
            row_types[row_idx] = RowType.DATA
        
        return row_types
    
    def _is_subheader_row(self, row: List[str], column_roles: Dict[int, ColumnRole]) -> bool:
        """
        Check if a row is a subheader row
        
        Args:
            row: Row to check
            column_roles: Dictionary mapping column indices to column roles
            
        Returns:
            True if the row is a subheader row, False otherwise
        """
        # Subheader rows often have text in the first column but no prices in price columns
        if not row:
            return False
        
        # Check if the first cell has text
        first_cell_has_text = len(row) > 0 and str(row[0]).strip() and not re.match(r'^\d+$', str(row[0]).strip())
        
        # Check if price columns are empty or don't contain prices
        price_columns_empty = True
        for col_idx, role in column_roles.items():
            if role in [ColumnRole.PRICE_CP, ColumnRole.PRICE_MAP, ColumnRole.PRICE_AP, 
                       ColumnRole.PRICE_EP, ColumnRole.PRICE_RACK, ColumnRole.PRICE_NET, 
                       ColumnRole.PRICE_SPECIAL, ColumnRole.PRICE_GENERIC]:
                if col_idx < len(row) and re.search(r'\d{3,}', str(row[col_idx])):
                    price_columns_empty = False
                    break
        
        return first_cell_has_text and price_columns_empty
    
    def _is_footer_row(self, row: List[str]) -> bool:
        """
        Check if a row is a footer row
        
        Args:
            row: Row to check
            
        Returns:
            True if the row is a footer row, False otherwise
        """
        # Footer rows often contain terms like "Note", "Terms", "Conditions", etc.
        row_text = ' '.join([str(cell) for cell in row]).lower()
        return re.search(r'note|term|condition|remark|n\.b\.|nb:|disclaimer', row_text, re.I) is not None
    
    def _is_notes_row(self, row: List[str]) -> bool:
        """
        Check if a row is a notes row
        
        Args:
            row: Row to check
            
        Returns:
            True if the row is a notes row, False otherwise
        """
        # Notes rows often start with "*", "•", "Note:", etc.
        if not row:
            return False
        
        first_cell = str(row[0]).strip()
        return first_cell.startswith('*') or first_cell.startswith('•') or first_cell.startswith('Note:')
    
    def _calculate_confidence(self, column_roles: Dict[int, ColumnRole], row_types: Dict[int, RowType]) -> float:
        """
        Calculate confidence score for the analysis
        
        Args:
            column_roles: Dictionary mapping column indices to column roles
            row_types: Dictionary mapping row indices to row types
            
        Returns:
            Confidence score (0.0 to 1.0)
        """
        # Count identified columns
        identified_columns = sum(1 for role in column_roles.values() if role != ColumnRole.UNKNOWN)
        total_columns = len(column_roles)
        
        # Count data rows
        data_rows = sum(1 for type in row_types.values() if type == RowType.DATA)
        total_rows = len(row_types)
        
        # Check if we found essential columns
        has_room_type = any(role == ColumnRole.ROOM_TYPE for role in column_roles.values())
        has_price = any(role in [ColumnRole.PRICE_CP, ColumnRole.PRICE_MAP, ColumnRole.PRICE_AP, 
                                ColumnRole.PRICE_EP, ColumnRole.PRICE_RACK, ColumnRole.PRICE_NET, 
                                ColumnRole.PRICE_SPECIAL, ColumnRole.PRICE_GENERIC] 
                        for role in column_roles.values())
        
        # Calculate column identification score
        column_score = identified_columns / total_columns if total_columns > 0 else 0.0
        
        # Calculate row identification score
        row_score = data_rows / (total_rows - 1) if total_rows > 1 else 0.0
        
        # Calculate essential column score
        essential_score = 0.0
        if has_room_type and has_price:
            essential_score = 1.0
        elif has_room_type or has_price:
            essential_score = 0.5
        
        # Combine scores with weights
        confidence = 0.4 * column_score + 0.3 * row_score + 0.3 * essential_score
        
        return min(1.0, confidence)
    
    def get_analysis_stats(self) -> Dict[str, int]:
        """
        Get statistics about analysis operations
        
        Returns:
            Dictionary of analysis statistics
        """
        return self.analysis_stats

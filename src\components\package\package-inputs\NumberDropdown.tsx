// No React import needed
import { label_css } from '../PackageForm';

interface NumberDropdownProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  defaultValue?: number;
  className?: string;
  id?: string;
}

export default function NumberDropdown({
  label,
  value,
  onChange,
  min = 0,
  max = 10,
  defaultValue = 0,
  className = '',
  id
}: NumberDropdownProps) {
  // Generate options from min to max
  const options = [];
  for (let i = min; i <= max; i++) {
    options.push(i);
  }

  // Removed unused function

  // Handle increment/decrement
  const handleIncrement = () => {
    const currentValue = isNaN(value) ? defaultValue : value;
    if (currentValue < max) {
      onChange(currentValue + 1);
    }
  };

  const handleDecrement = () => {
    const currentValue = isNaN(value) ? defaultValue : value;
    if (currentValue > min) {
      onChange(currentValue - 1);
    }
  };

  return (
    <div className={`${className}`}>
      <label htmlFor={id || label.toLowerCase().replace(/\s+/g, '-')} className={`${label_css} block mb-1`}>
        {label}
      </label>

      <div className="relative">
        {/* Custom styled dropdown */}
        <div className="flex">
          <div className="relative flex-1">
            <select
              id={id || label.toLowerCase().replace(/\s+/g, '-')}
              value={isNaN(value) ? defaultValue : value}
              onChange={(e) => onChange(parseInt(e.target.value))}
              className="w-full py-2 px-3 border border-gray-300 bg-white rounded-l-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              {options.map((num) => (
                <option key={num} value={num}>{num}</option>
              ))}
            </select>

            {/* Custom dropdown arrow */}
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>

          {/* Increment/decrement buttons */}
          <div className="flex flex-col border-t border-r border-b border-gray-300 rounded-r-md overflow-hidden">
            <button
              type="button"
              onClick={handleIncrement}
              disabled={(isNaN(value) ? defaultValue : value) >= max}
              className="px-2 py-1 bg-gray-50 hover:bg-gray-100 text-gray-600 focus:outline-none border-b border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
              </svg>
            </button>
            <button
              type="button"
              onClick={handleDecrement}
              disabled={(isNaN(value) ? defaultValue : value) <= min}
              className="px-2 py-1 bg-gray-50 hover:bg-gray-100 text-gray-600 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

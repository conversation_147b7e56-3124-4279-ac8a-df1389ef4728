import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { HotelRoom } from "@/types/types";
import { fetchAllRooms } from "@/utils/api-functions/fetch-rooms";

import { Eye, MoreHorizontal, PencilIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface EditMealPlanProps{
    hotelId : string
}

const EditMealPlan = (
    {hotelId} : EditMealPlanProps
) => {
    const [hotelRooms,setHotelRooms] = useState<HotelRoom[]>([])

    useEffect(()=>{
        async function fetchRooms(){
            const response = await fetchAllRooms(hotelId);
            setHotelRooms(response)
            console.log(response);
        }
        fetchRooms();
    },[hotelId])
    console.log('State',hotelRooms)
  return (
    <div className="flex flex-col gap-10 p-10">
       {
        hotelRooms?.map((r)=>(
            <div key={r.hotelRoomId} className="border p-4 rounded flex justify-between items-center ">
              <h1>  {r.hotelRoomType}</h1>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                <Button variant={'ghost'}>
                <MoreHorizontal size={17} className="cursor-pointer"/>

                </Button>

                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                    <DropdownMenuItem className=" flex items-center gap-2 cursor-pointer">
                     <Eye size={15} className="text-green-600"/>   View Meal Plans
                    </DropdownMenuItem>

                    <DropdownMenuItem className=" flex items-center gap-2 cursor-pointer">
                     <PencilIcon size={15} className="text-slate-500"/>   Edit Meal Plan
                    </DropdownMenuItem>

                </DropdownMenuContent>

            </DropdownMenu>
            </div>
        ))
       }
    </div>
  )
}

export default EditMealPlan
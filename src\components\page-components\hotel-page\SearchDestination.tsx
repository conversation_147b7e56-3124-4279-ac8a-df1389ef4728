/* eslint-disable @typescript-eslint/no-explicit-any */
import { fetchApiData } from '@/utils/api-functions/fetchApiData';
import { DESTINATION_QUERY_URL, DESTINATION_URL } from '@/utils/urls/urls';
import { useEffect, useState } from 'react'


export default function SearchDestination(props:any) {
  console.log(props.default)
  
    const extBorder2Class =
    "absolute top-[50px] w-full shadow-2xl bg-white  max-h-[200px] overflow-y-auto  border border-2 border-gray-400 rounded-lg";
    const allInputs =
    " h-10 border border-gray-400 text-xl mt-0  outline-none rounded relative px-2 my-2 flex items-center gap-2 text-black hover:bg-slate-100";
    const [inputText, setInputText] = useState("");
    const [isInputFocused, setInputFocused] = useState(false);
    const [inputDestinations, setInputDestinations] = useState([]);
    const handleInputDestinations = async (input:string) => {
        setInputFocused(true);
        setInputText(input);
        const filteredData = await fetchApiData(DESTINATION_QUERY_URL + input);
        setInputDestinations(filteredData|| []);
      };
      function handleInputText(text:any) {
        setInputText(text.destinationName);
        props.setDestinationId(text.destinationId);
        setInputFocused(false);
      }
      const fetchData = async () => {
        const data = await fetchApiData(DESTINATION_URL)
        setInputDestinations(data);
      };
      useEffect(() => {
        fetchData();
      }, []);
  return (
    <div className='relative'>
      <input
            placeholder="Travel destination"
            className={allInputs}
            value={inputText}
            type="text"
            id="dest"
            onFocus={() => setInputFocused(true)}
            onInput={(e:any) => handleInputDestinations(e.target.value)}
          />

          {isInputFocused && (
            <div className={extBorder2Class}>
              {inputDestinations?.length > 0 ? (
                inputDestinations?.map((dest:any) => (
                  <div key={dest.destinationId}>
                    <div
                      onMouseDown={() => handleInputText(dest)}
                      className=" px-4 py-2 flex items-center gap-3"
                    >
                      {dest.destinationName}
                    </div>
                  </div>
                ))
                
              ) : (
                <div className="text-red-500 px-4 py-2">No results found</div>
              )}
            </div>
          )}
              </div>
  )

}


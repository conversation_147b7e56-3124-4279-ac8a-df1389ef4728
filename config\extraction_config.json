{"version": "1.0.0", "patterns": {"date": {"date_range": "(?:valid(?:ity)?|applicable)(?:\\s+from|\\s+for|\\s+during)?\\s+([^\\n.]+)|(?:from|between)\\s+(\\d{1,2}[./-]\\d{1,2}[./-]\\d{2,4})\\s*(?:to|-|till|until|through)\\s*(\\d{1,2}[./-]\\d{1,2}[./-]\\d{2,4})", "date_format": "\\d{1,2}[./-]\\d{1,2}[./-]\\d{2,4}|\\d{1,2}\\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\\s+\\d{2,4}", "weekday_weekend": "(?:weekday|weekend|monday|tuesday|wednesday|thursday|friday|saturday|sunday)", "blackout_dates": "(?:blackout|block(?:ed)?|not\\s+valid|excluded)\\s+(?:date|period|on)s?\\s*:?\\s*([^\\n.]+)"}, "price": {"price_format": "(?:₹|rs\\.?|inr|\\$)?\\s*\\d[\\d,]*\\.?\\d*\\s*(?:\\+?(?:tax|gst))?", "rack_rate": "rack\\s+rate|published\\s+rate|gross\\s+rate|rack|published|gross|mrp|maximum\\s+retail\\s+price|list\\s+price", "net_rate": "special\\s+rate|net\\s+rate|agent\\s+rate|contract\\s+rate|offer\\s+price|special\\s+tariff|net\\s+tariff|special|net"}, "meal_plan": {"cp_supplement": "(?:MAP|Modified American Plan|Modified American)(?:\\s+supplement|\\s+extra|\\s+additional)(?:\\s+charge)?(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)", "ap_supplement": "(?:AP|American Plan|American)(?:\\s+supplement|\\s+extra|\\s+additional)(?:\\s+charge)?(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)", "meal_plan_definition": "(?:CP|Continental Plan|Continental|MAP|Modified American Plan|Modified American|AP|American Plan|American|EP|European Plan|European)(?:\\s+includes|\\s+means|\\s+-|\\s+:)?\\s+([^\\n.]+)"}, "extra_charges": {"extra_adult": "(?:extra|additional)(?:\\s+adult|\\s+person)(?:\\s+charge)?(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)", "extra_adult_meal_plan": "(?:extra|additional)(?:\\s+adult|\\s+person)(?:\\s+on)?\\s+(?:CP|Continental Plan|Continental|MAP|Modified American Plan|Modified American|AP|American Plan|American)(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)", "extra_child": "(?:extra|additional)(?:\\s+child)(?:\\s+charge)?(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)", "extra_child_with_bed": "(?:extra|additional)(?:\\s+child)(?:\\s+with)?\\s+(?:bed)(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)", "extra_child_without_bed": "(?:extra|additional)(?:\\s+child)(?:\\s+without)?\\s+(?:bed)(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)", "gala_dinner": "(?:gala|new\\s+year|christmas|xmas|diwali|special)\\s+(?:dinner|lunch|event|celebration)(?:\\s+charge)?(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)"}, "room_type": {"room_indicators": "room|suite|cottage|villa|apartment|chalet|bungalow|tent|cabin|deluxe|standard|executive|premium|luxury|super"}}, "keywords": {"meal_plans": {"ep": ["ep", "european plan", "room only", "without meals"], "cp": ["cp", "continental plan", "breakfast", "bed and breakfast", "b&b", "cpai"], "map": ["map", "modified american plan", "half board", "breakfast and dinner", "dinner and breakfast", "mapai"], "ap": ["ap", "american plan", "full board", "all meals", "apai"]}, "room_types": {"standard": ["standard", "classic", "regular", "normal", "base"], "deluxe": ["deluxe", "superior", "premium", "luxury"], "suite": ["suite", "apartment", "family room", "junior suite", "executive suite"], "cottage": ["cottage", "villa", "bungalow", "chalet", "cabin"], "tent": ["tent", "camp", "glamping"]}, "view_types": {"garden": ["garden", "lawn", "park"], "pool": ["pool", "swimming pool"], "sea": ["sea", "ocean", "beach", "water", "lake", "river"], "mountain": ["mountain", "hill", "valley", "forest"], "city": ["city", "town", "street", "urban"]}, "occupancy": {"single": ["single", "1 person", "one person", "1 pax", "one pax"], "double": ["double", "2 person", "two person", "2 pax", "two pax"], "triple": ["triple", "3 person", "three person", "3 pax", "three pax"], "quad": ["quad", "4 person", "four person", "4 pax", "four pax"]}}, "defaults": {"meal_plan": "cp", "occupancy": "double", "currency": "INR", "min_confidence_score": 0.6, "min_price_threshold": 100, "max_price_threshold": 100000}, "hotel_specific_handlers": {"gokulam_park": {"trigger_keywords": ["gokulam", "park", "kochi", "cochin"], "meal_plan_columns": true, "default_meal_plan": "cp"}, "dew_drop": {"trigger_keywords": ["dew", "drop", "munnar"], "separate_season_tables": true, "default_meal_plan": "cp"}, "spice_jungle": {"trigger_keywords": ["spice", "jungle", "thekkady", "periyar"], "separate_season_tables": true, "default_meal_plan": "cp"}}, "ocr_settings": {"enabled": true, "language": "eng", "page_segmentation_mode": 1, "oem": 3, "dpi": 300, "use_for_scanned_only": true}, "ner_settings": {"enabled": true, "model": "en_core_web_sm", "use_for_validation": true, "custom_entities": {"ROOM_TYPE": ["Deluxe Room", "Super Deluxe", "Suite", "Cottage"], "MEAL_PLAN": ["CP", "MAP", "AP", "EP"], "EXTRA_CHARGE_TYPE": ["Extra Adult", "Extra Child", "Child with Bed", "Child without Bed"]}}}
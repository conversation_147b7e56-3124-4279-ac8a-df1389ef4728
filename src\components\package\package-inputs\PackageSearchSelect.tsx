/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useState } from "react";
import { input_field_css, label_css } from "../PackageForm";

export interface PackageSearchSelectProp {
  dataName: string;
  dataId: string;
  allData: any[];
  pHolder: string;
  inputName:string;
  setDataName:React.Dispatch<React.SetStateAction<string>>
  setData: React.Dispatch<React.SetStateAction<string>>;
}
export default function PackageSearchSelect(props: PackageSearchSelectProp) {
  const [view, setView] = useState(false);
  const [restData, setRestData] = useState<any[]>([]);
  function handleInput(inp: string) {
    props.setDataName(inp);
    setView(true);
    const data = props.allData.filter((k) => k[props.dataName].toLowerCase()?.includes(inp.toLowerCase()));
    setRestData(data);
  }
  function handleClick(k: any) {
    props.setDataName(k[props.dataName]);
    props.setData(k[props.dataId]);
    setView(false);
  }
  useEffect(()=>{
    setRestData(props.allData)
  },[props.allData])

 
  return (
    <div className="relative">
      <label htmlFor={props.dataName} className={label_css}>{props.pHolder}</label>
      <input
        type="text"
        value={props.inputName}
        className={input_field_css}
        placeholder={props.pHolder}
        onFocus={()=>setView(true)}
        onInput={(e: any) => handleInput(e.target.value)}
        onBlur={() => setView(false)}
      />
      {view ? (
        <div className="absolute z-10 bg-white w-full border shadow-xl top-[60px] max-h-[200px] overflow-y-auto p-2">
          {restData.length >0 ?restData?.map((k) => (
            <div key={k[props.dataId]} onMouseDown={() => handleClick(k)} className="w-full py-1 hover:bg-slate-100 cursor-pointer">
              {k[props.dataName]}
            </div>
          )):<div className="text-red-500">No data found</div>}
        </div>
      ) : (
        <></>
      )}
    </div>
  );
}

//

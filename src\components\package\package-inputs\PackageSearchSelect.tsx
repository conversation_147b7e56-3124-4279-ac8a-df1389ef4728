/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useState, useRef } from "react";
import { label_css } from "../PackageForm";

export interface PackageSearchSelectProp {
  dataName: string;
  dataId: string;
  allData: any[];
  pHolder: string;
  inputName: string;
  setDataName: React.Dispatch<React.SetStateAction<string>>;
  setData: React.Dispatch<React.SetStateAction<string>>;
}

export default function PackageSearchSelect(props: PackageSearchSelectProp) {
  const [view, setView] = useState(false);
  const [restData, setRestData] = useState<any[]>([]);
  const [focused, setFocused] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  function handleInput(inp: string) {
    props.setDataName(inp);
    setView(true);
    const data = props.allData.filter((k) =>
      k[props.dataName]?.toLowerCase()?.includes(inp.toLowerCase())
    );
    setRestData(data);
  }

  function handleClick(k: any) {
    props.setDataName(k[props.dataName]);
    props.setData(k[props.dataId]);
    setView(false);
    if (inputRef.current) {
      inputRef.current.blur();
    }
  }

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setView(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setRestData(props.allData);
  }, [props.allData]);

  return (
    <div className="relative" ref={dropdownRef}>
      <label htmlFor={props.dataName} className={`${label_css} block mb-1`}>
        {props.pHolder}
      </label>

      <div className={`relative ${focused ? 'ring-2 ring-blue-500 rounded-md' : ''}`}>
        <input
          ref={inputRef}
          type="text"
          id={props.dataName}
          value={props.inputName}
          className="w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
          placeholder={`Search ${props.pHolder}...`}
          onFocus={() => {
            setView(true);
            setFocused(true);
          }}
          onBlur={() => {
            setFocused(false);
          }}
          onInput={(e: any) => handleInput(e.target.value)}
        />

        {props.inputName && (
          <button
            type="button"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            onClick={() => {
              props.setDataName('');
              props.setData('');
              if (inputRef.current) {
                inputRef.current.focus();
              }
            }}
          >
            ×
          </button>
        )}
      </div>

      {view && (
        <div className="absolute z-50 bg-white w-full border border-gray-200 shadow-lg rounded-md mt-1 max-h-[250px] overflow-y-auto">
          {restData.length > 0 ? (
            restData.map((k) => (
              <div
                key={k[props.dataId]}
                onMouseDown={() => handleClick(k)}
                className="px-3 py-2 hover:bg-blue-50 cursor-pointer transition-colors duration-150 border-b border-gray-100 last:border-b-0"
              >
                {k[props.dataName]}
              </div>
            ))
          ) : (
            <div className="px-3 py-2 text-red-500 italic">No matches found</div>
          )}
        </div>
      )}
    </div>
  );
}

//

import {  RoomData } from '@/components/page-components/hotel-details/room/AddRoom';
import api from "./auth"

export async function fetchAllRooms(hotelId : string) {
  try {
    const URL = `admin/hotel/${hotelId}/hotelRoom/get`;
    const response = await api.get(URL);

    // Get the result from the API response
    const rooms = response.data.result;

    // Verify that all rooms belong to the specified hotel
    const verifiedRooms = rooms.filter((room: any) => room.hotelId === hotelId);

    // Log verification for debugging
    console.log('[HOTEL_VERIFICATION] fetchAllRooms - API response verification', {
      hotel_id: hotelId,
      total_rooms_from_api: rooms.length,
      verified_rooms: verifiedRooms.length,
      all_rooms_belong_to_hotel: rooms.length === verifiedRooms.length,
      rooms_with_different_hotel_id: rooms.length - verifiedRooms.length
    });

    // If there are rooms with different hotel IDs, log a warning
    if (rooms.length !== verifiedRooms.length) {
      console.warn('[HOTEL_VERIFICATION] fetchAllRooms - WARNING: API returned rooms from different hotels', {
        hotel_id: hotelId,
        different_hotel_ids: [...new Set(rooms.filter((r: any) => r.hotelId !== hotelId).map((r: any) => r.hotelId))]
      });
    }

    // Return only rooms that belong to the specified hotel
    return Promise.resolve(verifiedRooms);
  } catch (error) {
    console.log(error);
    return Promise.reject('error');
  }
}

export async function fetchHotelRoom(hotelId: string,roomId:string) {
  try {
    const response = await fetchAllRooms(hotelId);
    const filteredRooms = response.find((room:RoomData) => room.hotelRoomId === roomId);

    return filteredRooms;
  } catch (error) {
    return Promise.reject('error');
  }
}


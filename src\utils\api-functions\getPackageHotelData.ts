import api from './auth';

/**
 * Fetches hotel data for a package with all required parameters
 * Uses the same endpoint as production: /user/package/{packageId}/hotel/get
 */
export const getPackageHotelData = async (packageId: string, params = {}) => {
  // Default parameters based on production usage
  const defaultParams = {
    noOfNight: 1,
    startDate: new Date().toISOString().split('T')[0], // today's date
    noOfChild: 0,
    noRoomCount: 1,
    noExtraAdult: 0,
    ...params
  };
  
  const queryParams = new URLSearchParams(defaultParams as any).toString();
  
  try {
    // Using api.get which handles the base URL correctly and avoids double slash issues
    const response = await api.get(`user/package/${packageId}/hotel/get?${queryParams}`);
    
    if (response.data) {
      // Handle different response formats
      return response.data.data || response.data.result || [];
    }
    return [];
  } catch (error) {
    console.error("Error fetching package hotel data:", error);
    return [];
  }
};

/**
 * Extracts meal plans for a specific room from package hotel data
 */
export const getMealPlansForRoom = (hotelData: any[], hotelId: string, roomId: string) => {
  if (!hotelData || !Array.isArray(hotelData)) return [];
  
  // Find the hotel
  const hotel = hotelData.find(h => h.hotelId === hotelId);
  if (!hotel || !hotel.hotelRoomDetails) return [];
  
  // Find the room
  const room = hotel.hotelRoomDetails.find((r: any) => r.hotelRoomId === roomId);
  if (!room) return [];
  
  // Return meal plans (different APIs might use different property names)
  return room.mealPlanDetails || room.mealPlans || [];
}; 
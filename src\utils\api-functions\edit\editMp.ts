import { MealPlanData } from '@/components/page-components/hotel-details/room/mealPlan/AddMealPlan';
import toast from 'react-hot-toast';
import api from '../auth';

export const editMealPlanApi = async (roomId: string,mealPlanId:string, mealPlan: MealPlanData) => {
  try {
    const response = await api.put(
      `admin/hotel/hotelRoom/${roomId}/${mealPlanId}/mealPlan/edit`,
      mealPlan
    );
    return Promise.resolve(response);
  } catch (error) {
    toast.error('An Error occurred');
    return Promise.reject('error');
  }
};

import { PackageType } from "@/types/types";
import toast from "react-hot-toast";
import api from './auth'

export async function editPackage( oldId:string,data : PackageType) {
    try {
        const response = await api.put(
            "admin/package/"+oldId,
          data
        );
        return Promise.resolve(response);
      } catch (error) {
        toast.error('An Error occurred');
        return Promise.reject('error');
      }
}
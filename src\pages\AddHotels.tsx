import * as z from "zod";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import toast from "react-hot-toast";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { HotelUploadData } from "@/types/types";
import { Textarea } from "@/components/ui/textarea";
import { ChangeEvent, useEffect, useState } from "react";
import { cn } from "@/lib/utils";

import { Check } from "lucide-react";
import { handleCreateHotel } from "@/utils/api-functions/create-hotel";
import SearchDestination from "@/components/page-components/hotel-page/SearchDestination";
import AddAmenities from "@/components/page-components/hotel-page/AddAmenities";
import { countries, statesData } from "@/utils/constants/optionsData";
import AddViewPoints from "@/components/page-components/hotel-page/AddViewPoints";
import { useNavigate } from "react-router-dom";

// import SelectAmenities from "@/components/api-components/select-amenities";

const basicFormSchema = z.object({
  name: z.string().min(4, {
    message: "Please enter name of the Hotel ",
  }),
  address: z.string().min(4, {
    message: "Please enter name of the Hotel ",
  }),
  lat: z.string(),
  businessEmail: z.string().min(4, {
    message: "Please enter name of the Hotel ",
  }),
  additionalEmail: z.string().optional(),
  phoneNumber: z.string(),
});

const AddHotels = () => {
  const router = useNavigate();
  const [hotelId , setHotelId] = useState<string>();

  const [viewpoints, setViewpoints] = useState<string[]>([]);
  const [hotelUploadData, setHotelUploadData] = useState<HotelUploadData>();
  const [destinationId, setDestinationId] = useState<string>("");
  const [amenitiesId, setAmenetiesId] = useState<string[]>([]);
  const [country, setCountry] = useState<string>("India");
  const [state, setState] = useState<string>("");
  const [allStates, setAllStates] = useState<string[]>(statesData);
  const [isInputFocused, setInputFocused] = useState(false);


  const basicForm = useForm<z.infer<typeof basicFormSchema>>({
    resolver: zodResolver(basicFormSchema),
    defaultValues: {
      name: "",
      address: "",
      lat: "",
      businessEmail: "",
      additionalEmail: "",
      phoneNumber: "",
    },
  });
  const { isSubmitting, isValid } = basicForm.formState;

  async function onsubmit(values: z.infer<typeof basicFormSchema>) {
    try {
      if (!Number(values.phoneNumber)) {
        toast.error("Enter valid Phone Number");
        return;
      }
      const latLong  = values.lat.split(",")
      const lattitude = latLong[0].trim()
      const Longitude = latLong[1].trim()

      const updatedData = {
        hotelName: values.name.trim(),
        location: {
          address: values.address.trim(),
          country: country.trim(),
          destinationId: destinationId,
          lat: lattitude,
          lon:Longitude,
          state: state.trim(),
        },

        viewPoint: viewpoints,
        review : values.additionalEmail as string,
        contract: {
          businessEmail: values.businessEmail as string,
          additionalEmail: values.additionalEmail as string,
          maintainerPhoneNo: values.phoneNumber,
        },
        amenities: amenitiesId,
      };
    

      const response = await handleCreateHotel(updatedData);
    const responseHotelid = response.data.result.hotelId;
    const loading = toast.loading('Adding data ')


   
      if (responseHotelid !== undefined) {
        setHotelId(responseHotelid);
        toast.dismiss(loading)
        toast.success('Hotel Added , Add Remaining data') 
  
        router(`/hotels/${responseHotelid.toString()}`); 
       
      }
      setHotelUploadData(updatedData);

      console.log("uploadData", hotelUploadData);

     
      window.location.reload();
    } catch {
      toast.error("Something went wrong");
    }
  }

  function handleChangeInputState(inp: string) {
    setInputFocused(true);
    const data = statesData.filter((k) => {
      return k.toLowerCase()?.includes(inp.toLowerCase());
    });
    setAllStates(data);
    setState(inp);
  }
  function handleInputText(type: string) {
    setState(type);
    setInputFocused(false);
  }

  useEffect(()=>{
    console.log(hotelId)
  },[])
  return (
    <>
      <div className="pt-5 px-5">
        <div className="flex items-center gap-10">
          <h1 className="text-slate-700 font-medium text-xl">Add a hotel</h1>
        </div>
        <div className="">
          <Form {...basicForm}>
            <form
              onSubmit={(e: React.FormEvent<HTMLFormElement>) =>
                e.preventDefault()
              }
              className="space-y-2"
            >
              <div className="flex items-start space-x-24 px-20">
                <div>
                  <div className="pt-5">
                    <h1 className="pt-3  text-lg">Name and Destination </h1>
                    <h1 className="text-muted-foreground pb-4 ">
                      Enter the name and destination of the hotel
                    </h1>
                  </div>
                  <div className="space-y-5">
                    <FormField
                      control={basicForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem className="">
                          <FormControl>
                            <Input
                              className="w-64"
                              disabled={isSubmitting}
                              placeholder="Name of the Hotel"
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <SearchDestination setDestinationId={setDestinationId} />
                  </div>
                </div>
                <div>
                  <div className="pt-5">
                    <h1 className="pt-3  text-lg">
                      Address details of the Hotel
                    </h1>
                  </div>
                  <div className="space-y-5">
                    <div className="flex items-center space-x-10">
                      <FormField
                        control={basicForm.control}
                        name="lat"
                        render={({ field }) => (
                          <FormItem className="">
                            <FormControl>
                              <Input
                                className="w-[18.5rem]"
                                disabled={isSubmitting}
                                placeholder="Latitude and Longitude"
                                type="text"
                                {...field}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                    </div>

                    <FormField
                      control={basicForm.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem className="">
                          <FormControl>
                            <Textarea
                              className="w-[18.5rem]"
                              disabled={isSubmitting}
                              placeholder="Address of the Hotel"
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <div className="flex items-center space-x-10">
                      <div className="w-32 relative">
                        <label htmlFor="country" className="text-sm font-bold">
                          State
                        </label>
                        <input
                          type="text"
                          value={state}
                          className=" bg-white border text-sm px-1 py-2 w-[160px]"
                          onInput={(e: ChangeEvent<HTMLInputElement>) =>
                            handleChangeInputState(e.target.value)
                          }
                        />
                        <div className="shadow-2xl rounded-lg max-h-[300px] w-[200px] overflow-y-auto p-1 absolute top-[70px] z-30 bg-white">
                          {isInputFocused && allStates?.length > 0
                            ? allStates?.map((type) => {
                                return (
                                  <div
                                    key={type}
                                    onMouseDown={() => handleInputText(type)}
                                    className="px-2 py-1 w-[200px]  hover:bg-gray-200 cursor-pointer rounded-md"
                                  >
                                    {type}
                                  </div>
                                );
                              })
                            : ""}
                        </div>
                      </div>

                      <div className="w-32">
                        <label htmlFor="country" className="text-sm font-bold">
                          Country
                        </label>
                        <select
                          name=""
                          id="country"
                          onChange={(e: ChangeEvent<HTMLSelectElement>) =>
                            setCountry(e.target.value)
                          }
                          className="w-32 bg-white border px-4 py-2"
                        >
                          {countries?.map((k) => (
                            <option key={k} value={k}>
                              {k}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-24 px-20">
                {/* Contact Details */}
                <div>
                  <div className="pt-3">
                    <h1 className="pt-3  text-lg">Contact Details</h1>
                    <h1 className="text-muted-foreground pb-4 ">
                      Enter the phone number and email of the hotel
                    </h1>
                  </div>
                  <div className="space-y-5">
                    <FormField
                      control={basicForm.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem className="">
                          <FormControl>
                            <Input
                              className="w-[18.5rem]"
                              disabled={isSubmitting}
                              type="text"
                              placeholder="Phone Number  of the hotel"
                              {...field}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <div className="flex items-center space-x-10">
                      <FormField
                        control={basicForm.control}
                        name="businessEmail"
                        render={({ field }) => (
                          <FormItem className="">
                            <FormControl>
                              <Input
                                className="w-32"
                                disabled={isSubmitting}
                                placeholder="business Email of the Hotel"
                                {...field}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={basicForm.control}
                        name="additionalEmail"
                        render={({ field }) => (
                          <FormItem className="">
                            <FormControl>
                              <Input
                                className="w-32"
                                disabled={isSubmitting}
                                placeholder="Star Rating"
                                {...field}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
                <div>
                  <div className="">
                    <div className="text-lg">Amenities</div>
                  </div>
                  <div className="space-y-1">
                    <AddAmenities setAmenetiesId={setAmenetiesId} />
                    <div className="flex flex-col space-y-1">
                      <h1 className="text-muted-foreground">
                        Select to add{" "}
                        <span className="text-slate-800">ViewPoints</span>
                      </h1>
                      <AddViewPoints setViewPoints={setViewpoints} />
                    </div>
                  </div>
                </div>
              </div>

              <Button
                type="submit"
                disabled={
                  !isValid ||
                  isSubmitting ||
                  !imageUrl ||
                  viewpoints?.length === 0
                }
                onClick={basicForm.handleSubmit(onsubmit)}
              >
                Submit
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </>
  );
};

export default AddHotels;

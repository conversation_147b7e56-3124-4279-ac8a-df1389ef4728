/* eslint-disable @typescript-eslint/no-explicit-any */

import PackageContext from "@/utils/context/PackageContext"
import { useContext } from "react"

export default function SEDates() {
   const {setStartDate,setEndDate}:any = useContext(PackageContext)
  return (
    <div className={"relative m-1 border"}>
        <div className="flex p-2 gap-2">         
              <div className="">
                <label htmlFor="sDate" className="mt-2 text-sm font-bold ">
                  Start Date
                </label>
                <input type="date" id="sDate" onChange={(e)=> setStartDate(e.target.value)}/>
              </div>
              <div className="">
                <label htmlFor="eDate" className="mt-2 text-sm font-bold ">
                  End Date
                </label>
                <input type="date" id="eDate" onChange={(e)=> setEndDate(e.target.value)}/>
              </div>
            </div>
        
    </div>
  )
}

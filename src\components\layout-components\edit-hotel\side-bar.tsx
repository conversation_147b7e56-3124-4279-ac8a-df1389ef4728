

import { BedIcon, ChevronLeft, HotelIcon, } from 'lucide-react'


import HotelSlideBar from './sidebar-routes'
import { Button } from '@/components/ui/button'
import { useNavigate } from 'react-router-dom'
const hotelSlideBar = [
    {
        label : 'Hotel',
        icon : HotelIcon,
        tab : 'hotel'

    },
    {
        label : 'Room',
        icon : BedIcon,
      
        tab : 'room'

    },
  
   

]

const HotelSideBar = () => {
    const router = useNavigate();
 
  return (
    <div className='w-1/6 border-r fixed z-10 shadow h-full  '>
        <Button onClick={()=>{router(-1)}} variant={'outline'} className=' m-5 flex items-center gap-1'>
          <ChevronLeft size={18}/>  Back
        </Button>
       
        <div className=' flex flex-col items-center '>
        {
            hotelSlideBar?.map((menus)=>(
                <HotelSlideBar  label={menus.label} tab={menus.tab} icon={menus.icon} key={menus.label}/>
                 
               
            ))
        }
        </div>
        
    
      
    </div>
  )
}

export default HotelSideBar
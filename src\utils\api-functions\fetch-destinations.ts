import api from './auth'

export async function fetchAllDestinations() {
  try {
    const response = await api.get("user/package/destination/search");

    return Promise.resolve(response.data.result);
  } catch (error) {
    console.log(error);
    throw new Error('Failed to fetch hotels');
  }
}

export async function fetchDestinationById(id: string) {
  try {
    const response = await api.get("user/package/destination/search", {
      params: { id } 
    });
    

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const destination = response.data.result.find((destination : any) => destination.id === id);

    if (!destination) {
      throw new Error("Destination not found");
    }

    return Promise.resolve(destination);
    
  } catch (error) {
    console.error(error);
    throw new Error("Failed to fetch destination");
  }
}

/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeEvent, useContext, useState, useId, useEffect } from 'react';
import toast from 'react-hot-toast';
import AppContext from '@/utils/context/AppContext';
import { mealPlanData, seasonTypesData } from '@/utils/constants/optionsData';
import { AlertCircle, Calendar, Save, Trash2, PlusCircle } from 'lucide-react';
import { DateRange as DateRangeComp, Range, RangeKeyDict } from 'react-date-range';
import "react-date-range/dist/styles.css"; 
import "react-date-range/dist/theme/default.css";

// Array of colors for different date ranges
const colors = ["#3d91ff", "#ff6347", "#32cd32", "#ffa500", "#8a2be2", "#ff1493"];

export interface MealPlanData {
  mealPlan: string;
  roomPrice: number;
  adultPrice: number;
  childPrice: number;
  seasonType: string;
  startDate: string[];
  endDate: string[];
  gstPer: number;
}

export default function AddMealPlan({ onClose }: { onClose?: (shouldRefresh?: boolean) => void }) {
  const { handleMealPlanData, handleMealPlanDelete, setDates }: any = useContext(AppContext);
  
  // Form state
  const [formData, setFormData] = useState({
    mealPlan: "MAP",
    roomPrice: "",
    adultPrice: "",
    childPrice: "",
    seasonType: "Off Season",
    gstPer: "0"
  });

  // Unique IDs for form fields
  const mealPlanId = useId();
  const roomPriceId = useId();
  const adultPriceId = useId();
  const childPriceId = useId();
  const seasonTypeId = useId();
  const gstId = useId();

  const [dateRanges, setDateRanges] = useState<Range[]>([]);
  const [previousRanges, setPreviousRanges] = useState<Range[][]>([]);
  const [focusedRange, setFocusedRange] = useState<[number, 0 | 1]>([0, 0]);
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [isSaved, setSaved] = useState(false);
  const [view, setView] = useState(true);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});

  // Initialize with a default empty date range
  useEffect(() => {
    if (dateRanges.length === 0) {
      addDateRange();
    }
  }, []);

  // Add a new date range
  const addDateRange = () => {
    // Choose start = today or one day after the latest existing endDate if any
    let base = new Date();
    if (dateRanges.length > 0) {
      const endDates: Date[] = dateRanges
        .filter(r => r.endDate !== undefined)
        .map(r => r.endDate as Date);
      if (endDates.length > 0) {
        const maxEnd = endDates.reduce((a, b) => (b.getTime() > a.getTime() ? b : a));
        base = new Date(maxEnd.getTime());
        base.setDate(base.getDate() + 1);
      }
    }
    
    const newStart = base;
    const newEnd = new Date(base.getTime());
    newEnd.setDate(newEnd.getDate() + 7); // Default to a 7-day range
    
    const newKey = `selection${dateRanges.length + 1}`;
    const colorIndex = dateRanges.length % colors.length;
    
    setDateRanges(prevRanges => [
      ...prevRanges, 
      { 
        startDate: newStart,
        endDate: newEnd,
        key: newKey,
        color: colors[colorIndex]
      }
    ]);
    
    // Focus the newly added range
    setFocusedRange([dateRanges.length, 0]);
    const lastRangeStart = dateRanges[dateRanges.length - 1]?.startDate;
    if (lastRangeStart) {
      setCurrentMonth(new Date(lastRangeStart));
    }
  };

  // Handle date range selection
  const handleSelect = (ranges: RangeKeyDict) => {
    // Save current state for undo
    setPreviousRanges([...previousRanges, [...dateRanges]]);
    
    const selectedKey = Object.keys(ranges)[0];
    const segment = focusedRange[1]; // Remember which side was being edited
    
    const updatedRanges = dateRanges.map(range => {
      if (range.key === selectedKey) {
        return { ...range, ...ranges[selectedKey] };
      }
      return range;
    });
    
    setDateRanges(updatedRanges);
    
    // Keep focus on the current range
    const rangeIndex = parseInt(selectedKey.replace('selection', '')) - 1;
    if (rangeIndex >= 0 && rangeIndex < updatedRanges.length) {
      setFocusedRange([rangeIndex, segment]);
      const lastRangeStart = dateRanges[dateRanges.length - 1]?.startDate;
      if (lastRangeStart) {
        setCurrentMonth(new Date(lastRangeStart));
      }
    }

    // Update AppContext dates for compatibility
    const allStartDates = updatedRanges
      .filter(range => range.startDate)
      .map(range => toISODate(range.startDate!));
    
    const allEndDates = updatedRanges
      .filter(range => range.endDate)
      .map(range => toISODate(range.endDate!));
    
    if (setDates) {
      setDates(allStartDates, allEndDates);
    }
  };

  // Handle deleting a date range
  const handleDeleteRange = (rangeKey: string | undefined) => {
    if (!rangeKey) return;
    
    // Save current state for undo
    setPreviousRanges([...previousRanges, [...dateRanges]]);
    
    // Remove the specified range
    const updatedRanges = dateRanges.filter(range => range.key !== rangeKey);
    setDateRanges(updatedRanges);
    
    // Update AppContext dates for compatibility
    const allStartDates = updatedRanges
      .filter(range => range.startDate)
      .map(range => toISODate(range.startDate!));
    
    const allEndDates = updatedRanges
      .filter(range => range.endDate)
      .map(range => toISODate(range.endDate!));
    
    if (setDates) {
      setDates(allStartDates, allEndDates);
    }
  };

  // Undo the last change to date ranges
  const handleUndo = () => {
    if (previousRanges.length > 0) {
      const lastState = previousRanges[previousRanges.length - 1];
      setDateRanges(lastState);
      setPreviousRanges(previousRanges.slice(0, -1));
      
      // Update AppContext dates for compatibility
      const allStartDates = lastState
        .filter(range => range.startDate)
        .map(range => toISODate(range.startDate!));
      
      const allEndDates = lastState
        .filter(range => range.endDate)
        .map(range => toISODate(range.endDate!));
      
      if (setDates) {
        setDates(allStartDates, allEndDates);
      }
    }
  };

  // Format date to ISO string without timezone
  const toISODate = (date: Date): string => {
    if (!date) return '';
    const tzOffset = date.getTimezoneOffset() * 60000; // offset in ms
    return new Date(date.getTime() - tzOffset).toISOString().split('T')[0];
  };

  // Format date for display
  const formatDate = (date: Date | undefined): string => {
    if (!date) return 'Select date';
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).replace(/\//g, '/');
  };

  // Form validation
  const validateForm = (): boolean => {
    const errors: {[key: string]: string} = {};
    
    if (!formData.roomPrice || parseFloat(formData.roomPrice) <= 0) {
      errors.roomPrice = "Room price is required";
    }
    
    if (!formData.adultPrice || parseFloat(formData.adultPrice) <= 0) {
      errors.adultPrice = "Adult price is required";
    }
    
    if (!formData.childPrice || parseFloat(formData.childPrice) <= 0) {
      errors.childPrice = "Child price is required";
    }
    
    if (dateRanges.length === 0 || !dateRanges.some(range => range.startDate && range.endDate)) {
      errors.dateRanges = "At least one date range is required";
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form input changes
  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Clear validation error
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: ""
      });
    }
    
    // For price fields, only allow numbers and decimal points
    if (name === "roomPrice" || name === "adultPrice" || name === "childPrice") {
      if (value === "" || /^\d*\.?\d*$/.test(value)) {
        setFormData(prev => ({
          ...prev,
          [name]: value
        }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Save the meal plan
  function handleSave() {
    if (!validateForm()) {
      toast.error("Please fix the validation errors");
      return;
    }
    
    // Extract start and end dates from the dateRanges
    const startDatesForSave = dateRanges
      .filter(range => range.startDate && range.endDate)
      .map(range => toISODate(range.startDate!));
      
    const endDatesForSave = dateRanges
      .filter(range => range.startDate && range.endDate)
      .map(range => toISODate(range.endDate!));
    
    const mealPlanData: MealPlanData = {
      mealPlan: formData.mealPlan,
      roomPrice: parseFloat(formData.roomPrice),
      adultPrice: parseFloat(formData.adultPrice),
      childPrice: parseFloat(formData.childPrice),
      seasonType: formData.seasonType,
      startDate: startDatesForSave,
      endDate: endDatesForSave,
      gstPer: parseInt(formData.gstPer),
    };
    
    if (
      mealPlanData.mealPlan &&
      mealPlanData.roomPrice &&
      mealPlanData.adultPrice &&
      mealPlanData.childPrice &&
      mealPlanData.seasonType &&
      mealPlanData.startDate.length > 0 &&
      mealPlanData.endDate.length > 0 &&
      mealPlanData.gstPer >= 0
    ) {
      handleMealPlanData(mealPlanData);
      setSaved(true);
      
      // Notify parent component if callback provided
      if (onClose) {
        onClose(true);
      }
    } else {
      toast.error("Enter all fields to save");
    }
  }

  // Close/cancel meal plan creation
  function handleClose(roomType: string) {
    handleMealPlanDelete(roomType);
    setView(false);
    
    // Notify parent component if callback provided
    if (onClose) {
      onClose(false);
    }
  }


  return view ? (
    !isSaved ? (
      <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto border border-blue-100">
        <h2 className="text-xl font-bold text-blue-900 mb-6 pb-3 border-b border-blue-100">Add New Meal Plan</h2>
        
        <div className="flex flex-col md:flex-row gap-6">
          {/* Left: Meal Plan & Price Fields */}
          <div className="flex-1 space-y-4">
            <div>
              <label htmlFor={mealPlanId} className="block text-base font-semibold text-blue-900 mb-1">Meal Plan</label>
              <select
                id={mealPlanId}
                name="mealPlan"
                value={formData.mealPlan}
                onChange={handleInputChange}
                className="w-full px-3 py-2 text-lg bg-white border border-blue-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 font-medium text-blue-900"
              >
                {mealPlanData?.map(k => <option key={k} value={k}>{k.toUpperCase()}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor={roomPriceId} className="block text-base font-semibold text-blue-900 mb-1">Room Price</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500">₹</span>
                </div>
                <input
                  type="text"
                  id={roomPriceId}
                  name="roomPrice"
                  placeholder="0.00"
                  value={formData.roomPrice}
                  onChange={handleInputChange}
                  className={`w-full border pl-8 px-3 py-2 text-lg ${validationErrors.roomPrice ? 'border-red-500 focus:ring-red-500' : 'border-blue-200 focus:ring-blue-400'} rounded-lg focus:ring-2 focus:border-blue-400 text-blue-900`}
                />
              </div>
              {validationErrors.roomPrice && (
                <div className="text-xs text-red-500 flex items-center gap-1 mt-1">
                  <AlertCircle size={12} />
                  {validationErrors.roomPrice}
                </div>
              )}
            </div>
            <div className="flex gap-4">
              <div className="flex-1">
                <label htmlFor={adultPriceId} className="block text-base font-semibold text-blue-900 mb-1">Adult Price</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500">₹</span>
                  </div>
                  <input
                    type="text"
                    id={adultPriceId}
                    name="adultPrice"
                    placeholder="0.00"
                    value={formData.adultPrice}
                    onChange={handleInputChange}
                    className={`w-full border pl-8 px-3 py-2 text-lg ${validationErrors.adultPrice ? 'border-red-500 focus:ring-red-500' : 'border-blue-200 focus:ring-blue-400'} rounded-lg focus:ring-2 focus:border-blue-400 text-blue-900`}
                  />
                </div>
                {validationErrors.adultPrice && (
                  <div className="text-xs text-red-500 flex items-center gap-1 mt-1">
                    <AlertCircle size={12} />
                    {validationErrors.adultPrice}
                  </div>
                )}
              </div>
              <div className="flex-1">
                <label htmlFor={childPriceId} className="block text-base font-semibold text-blue-900 mb-1">Child Price</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500">₹</span>
                  </div>
                  <input
                    type="text"
                    id={childPriceId}
                    name="childPrice"
                    placeholder="0.00"
                    value={formData.childPrice}
                    onChange={handleInputChange}
                    className={`w-full border pl-8 px-3 py-2 text-lg ${validationErrors.childPrice ? 'border-red-500 focus:ring-red-500' : 'border-blue-200 focus:ring-blue-400'} rounded-lg focus:ring-2 focus:border-blue-400 text-blue-900`}
                  />
                </div>
                {validationErrors.childPrice && (
                  <div className="text-xs text-red-500 flex items-center gap-1 mt-1">
                    <AlertCircle size={12} />
                    {validationErrors.childPrice}
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* Right: Season, GST */}
          <div className="flex-1 space-y-4">
            <div>
              <label htmlFor={seasonTypeId} className="block text-base font-semibold text-blue-900 mb-1">Season Type</label>
              <select
                id={seasonTypeId}
                name="seasonType"
                value={formData.seasonType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 text-lg bg-white border border-blue-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 font-medium text-blue-900"
              >
                {seasonTypesData?.map(k => <option key={k.value} value={k.value}>{k.text}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor={gstId} className="block text-base font-semibold text-blue-900 mb-1">GST Percentage</label>
              <select
                id={gstId}
                name="gstPer"
                value={formData.gstPer}
                onChange={handleInputChange}
                className="w-full px-3 py-2 text-lg bg-white border border-blue-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 font-medium text-blue-900"
              >
                <option value="0">0%</option>
                <option value="10">12%</option>
                <option value="12">18%</option>
              </select>
            </div>
          </div>
        </div>
        
        {/* Date Range Section */}
        <div className="mt-6 pt-4 border-t border-blue-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-blue-900 flex items-center gap-2">
              <Calendar className="w-5 h-5 text-blue-500" />
              Select Date Ranges
            </h3>
            <div className="flex gap-2">
              <button 
                onClick={handleUndo}
                disabled={previousRanges.length === 0}
                className={`inline-flex items-center gap-1.5 px-3 py-1.5 text-sm rounded-lg transition-all duration-200 ${
                  previousRanges.length === 0 
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed" 
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200 active:transform active:scale-95"
                }`}
                title="Undo last action"
              >
                <Trash2 className="w-3.5 h-3.5" />
                Undo
              </button>
              <button 
                onClick={addDateRange}
                className="inline-flex items-center gap-1.5 bg-blue-50 text-blue-600 px-3 py-1.5 text-sm rounded-lg hover:bg-blue-100 transition-all duration-200 active:transform active:scale-95"
                title="Add new date range"
              >
                <PlusCircle className="w-3.5 h-3.5" />
                Add Range
              </button>
            </div>
          </div>
          
          {/* Calendar container */}
          <div className="overflow-hidden mb-5 shadow-sm border border-gray-100 rounded-lg">
            <div className="rdrDateRangePickerWrapper custom-calendar-wrapper max-h-[360px] overflow-y-auto">
              <DateRangeComp
                ranges={dateRanges}
                onChange={handleSelect}
                focusedRange={focusedRange}
                onRangeFocusChange={setFocusedRange}
                moveRangeOnFirstSelection={false}
                months={1}
                direction="horizontal"
                showDateDisplay={true}
                rangeColors={dateRanges.map(range => range.color || colors[0])}
                date={currentMonth}
                minDate={new Date(new Date().getFullYear() - 2, 0, 1)}
                maxDate={new Date(new Date().getFullYear() + 2, 11, 31)}
                showMonthAndYearPickers={true}
                preventSnapRefocus={true}
                calendarFocus="forwards"
              />
            </div>
          </div>
          
          {/* Selected Ranges Display */}
          {dateRanges.length > 0 && (
            <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1.5">
                Selected Ranges ({dateRanges.length})
              </h4>
              <div className="space-y-2 max-h-[150px] overflow-y-auto">
                {dateRanges.map((range, index) => (
                  range.startDate && range.endDate && (
                    <div 
                      key={range.key} 
                      className="rounded-md overflow-hidden border-l-4 shadow-sm"
                      style={{ borderLeftColor: range.color || colors[0] }}
                    >
                      <div className="flex justify-between items-center px-3 py-1.5 bg-gray-100">
                        <span className="text-xs font-semibold" style={{ color: range.color || colors[0] }}>
                          {`Range ${index + 1}`}
                        </span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteRange(range.key!);
                          }}
                          className="text-red-500 hover:text-red-700 p-1 rounded"
                          title="Remove this date range"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                      <div className="bg-white p-2 flex items-center">
                        <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-700">
                          {formatDate(range.startDate)} — {formatDate(range.endDate)}
                        </span>
                      </div>
                    </div>
                  )
                ))}
              </div>
              {validationErrors.dateRanges && (
                <div className="text-xs text-red-500 flex items-center gap-1 mt-2">
                  <AlertCircle size={12} />
                  {validationErrors.dateRanges}
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* Action Buttons */}
        <div className="flex justify-end gap-3 mt-8">
          <button
            type="button"
            onClick={() => handleClose(formData.mealPlan)}
            className="bg-white text-gray-700 px-5 py-2.5 rounded-lg hover:bg-gray-100 transition border border-gray-200 font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="bg-blue-600 text-white px-6 py-2.5 rounded-lg text-base font-semibold shadow hover:bg-blue-700 transition flex items-center gap-2"
          >
            <Save size={18} />
            Save Meal Plan
          </button>
        </div>
      </div>
    ) : (
      <div className="shadow-lg px-6 py-4 border rounded-lg mb-4 flex justify-between items-center bg-green-50 border-green-100">
        <div className="flex flex-col gap-1">
          <div className="font-semibold text-green-800">
            Meal Plan: <span className="font-bold text-green-700">{formData.mealPlan.toUpperCase()}</span>
          </div>
          <div className="text-sm text-green-700">
            Room price: <span className="font-semibold">₹{formData.roomPrice}</span>
          </div>
        </div>
        <button
          onClick={() => handleClose(formData.mealPlan)}
          className="border h-8 w-8 rounded-full flex items-center justify-center bg-red-500 text-white hover:bg-red-600 transition shadow-sm"
        >
          ×
        </button>
      </div>
    )
  ) : (
    <></>
  );
}

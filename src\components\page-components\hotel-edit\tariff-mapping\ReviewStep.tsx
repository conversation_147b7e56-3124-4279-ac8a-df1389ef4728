import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Calendar,
  CheckCircle,
  ChevronLeft,
  Save
} from 'lucide-react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';
import { format, isValid } from 'date-fns';
import { HotelRoom } from '@/types/types';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface Price {
  id: string;
  roomType: string;
  mealPlan: string;
  price: number;
  startDate: string;
  endDate: string;
  confidence?: number;
  isDerived?: boolean;
}

interface ExtraCharge {
  id: string;
  type: string;
  description: string;
  amount: number;
  confidence?: number;
  isDerived?: boolean;
}

interface ReviewStepProps {
  hotelRooms: HotelRoom[];
  roomMappings: Record<string, string>;
  dateRanges: DateRange[];
  prices: Price[];
  extraCharges: ExtraCharge[];
  priceMappings: Record<string, Price>;
  chargeMappings: Record<string, ExtraCharge>;
  onSave: () => void;
  onBack: () => void;
  isLoading?: boolean;
}

const MEAL_PLANS = [
  { id: 'cp', name: 'CP - Continental Plan' },
  { id: 'map', name: 'MAP - Modified American Plan' },
  { id: 'ap', name: 'AP - American Plan' },
  { id: 'ep', name: 'EP - European Plan' }
];

const CHARGE_TYPES = [
  { id: 'extra_adult_cp', name: 'Extra Adult (CP)' },
  { id: 'extra_adult_map', name: 'Extra Adult (MAP)' },
  { id: 'extra_adult_ap', name: 'Extra Adult (AP)' },
  { id: 'extra_child_with_bed', name: 'Extra Child with Bed' },
  { id: 'extra_child_without_bed', name: 'Extra Child without Bed' },
  { id: 'map_supplement', name: 'MAP Supplement' },
  { id: 'ap_supplement', name: 'AP Supplement' }
];

const ReviewStep: React.FC<ReviewStepProps> = ({
  hotelRooms,
  roomMappings,
  dateRanges,
  prices,
  extraCharges,
  priceMappings,
  chargeMappings,
  onSave,
  onBack,
  isLoading = false
}) => {
  const [expandedSections, setExpandedSections] = useState({
    rooms: true,
    dates: true,
    prices: true,
    charges: true
  });

  // Format date for display
  const formatDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      if (isValid(date)) {
        return format(date, 'MMM d, yyyy');
      }
      return dateStr;
    } catch (e) {
      return dateStr;
    }
  };

  // Format price for display
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(price);
  };

  // Get room name from ID
  const getRoomName = (roomId: string) => {
    const room = hotelRooms.find(r => r.hotelRoomId === roomId);
    return room ? room.hotelRoomType : 'Unknown Room';
  };

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Count mapped items
  const getMappedCounts = () => {
    const roomCount = Object.keys(roomMappings).length;
    const dateCount = dateRanges.length;
    const priceCount = prices.length;
    const chargeCount = extraCharges.length;
    
    return {
      roomCount,
      dateCount,
      priceCount,
      chargeCount,
      total: roomCount + dateCount + priceCount + chargeCount
    };
  };

  const counts = getMappedCounts();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">
          Step 5: Review and Save
        </h3>
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          {counts.total} Items Total
        </Badge>
      </div>

      <Alert className="bg-green-50 border-green-200">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertTitle className="text-green-800">Ready to Save</AlertTitle>
        <AlertDescription className="text-green-700">
          Review the mapped data below and click "Save to Database" when you're ready to update your hotel tariff information.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        {/* Room Types Section */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div 
            className="bg-gray-50 p-4 border-b flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection('rooms')}
          >
            <div>
              <h4 className="font-medium text-gray-800">Room Types</h4>
              <div className="text-sm text-gray-500">{counts.roomCount} room types mapped</div>
            </div>
            <Button variant="ghost" size="sm">
              {expandedSections.rooms ? 'Hide' : 'Show'}
            </Button>
          </div>
          
          {expandedSections.rooms && (
            <div className="p-4 divide-y">
              {Object.entries(roomMappings).map(([extractedRoom, roomId]) => (
                <div key={extractedRoom} className="py-3 flex justify-between items-center">
                  <div>
                    <div className="text-sm font-medium text-gray-500">From PDF:</div>
                    <div className="font-medium text-gray-800">{extractedRoom}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-500">Mapped to:</div>
                    <div className="font-medium text-blue-600">
                      {roomId === 'new' ? 'New Room Type' : getRoomName(roomId)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Date Ranges Section */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div 
            className="bg-gray-50 p-4 border-b flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection('dates')}
          >
            <div>
              <h4 className="font-medium text-gray-800">Date Ranges</h4>
              <div className="text-sm text-gray-500">{counts.dateCount} date ranges</div>
            </div>
            <Button variant="ghost" size="sm">
              {expandedSections.dates ? 'Hide' : 'Show'}
            </Button>
          </div>
          
          {expandedSections.dates && (
            <div className="p-4 divide-y">
              {dateRanges.map((range, index) => (
                <div key={`${range.startDate}_${range.endDate}_${index}`} className="py-3 flex justify-between items-center">
                  <div className="flex items-center">
                    <Calendar size={16} className="mr-2 text-blue-500" />
                    <div className="font-medium text-gray-800">
                      {formatDate(range.startDate)} - {formatDate(range.endDate)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Prices Section */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div 
            className="bg-gray-50 p-4 border-b flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection('prices')}
          >
            <div>
              <h4 className="font-medium text-gray-800">Prices</h4>
              <div className="text-sm text-gray-500">{counts.priceCount} prices</div>
            </div>
            <Button variant="ghost" size="sm">
              {expandedSections.prices ? 'Hide' : 'Show'}
            </Button>
          </div>
          
          {expandedSections.prices && (
            <div className="p-4">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room Type</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Meal Plan</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Range</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {prices.map((price) => {
                    const mappedPrice = priceMappings[price.id] || price;
                    const roomId = roomMappings[price.roomType] || '';
                    
                    if (!roomId) return null;
                    
                    return (
                      <tr key={price.id}>
                        <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-800">
                          {getRoomName(roomId)}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">
                          {MEAL_PLANS.find(p => p.id === mappedPrice.mealPlan)?.name || mappedPrice.mealPlan.toUpperCase()}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">
                          {formatPrice(mappedPrice.price)}
                          {mappedPrice.isDerived && (
                            <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200 text-xs">
                              Derived
                            </Badge>
                          )}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">
                          {formatDate(mappedPrice.startDate)} - {formatDate(mappedPrice.endDate)}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Extra Charges Section */}
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <div 
            className="bg-gray-50 p-4 border-b flex justify-between items-center cursor-pointer"
            onClick={() => toggleSection('charges')}
          >
            <div>
              <h4 className="font-medium text-gray-800">Extra Charges</h4>
              <div className="text-sm text-gray-500">{counts.chargeCount} extra charges</div>
            </div>
            <Button variant="ghost" size="sm">
              {expandedSections.charges ? 'Hide' : 'Show'}
            </Button>
          </div>
          
          {expandedSections.charges && (
            <div className="p-4">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Charge Type</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {extraCharges.map((charge) => {
                    const mappedCharge = chargeMappings[charge.id] || charge;
                    
                    return (
                      <tr key={charge.id}>
                        <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-800">
                          {CHARGE_TYPES.find(t => t.id === mappedCharge.type)?.name || mappedCharge.description}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">
                          {formatPrice(mappedCharge.amount)}
                          {mappedCharge.isDerived && (
                            <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200 text-xs">
                              Derived
                            </Badge>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex items-center"
          disabled={isLoading}
        >
          <ChevronLeft size={16} className="mr-1" />
          Back
        </Button>
        <Button
          onClick={onSave}
          className="flex items-center"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-opacity-50 border-t-transparent rounded-full"></div>
              Saving...
            </>
          ) : (
            <>
              <Save size={16} className="mr-1" />
              Save to Database
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default ReviewStep;

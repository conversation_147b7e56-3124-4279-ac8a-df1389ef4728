#!/bin/bash

echo "🚀 Setting up production environment for admin.tripxplo.com..."

# Set production environment variables
export VITE_API_SERVER_URL="https://api.tripxplo.com/v1/api/"
export VITE_API_URL="https://api.tripxplo.com/v1"
export VITE_FRONTEND_URL="https://admin.tripxplo.com"
export VITE_TARIFF_API_URL="https://api.tripxplo.com/v1"
export VITE_LINODE_STORAGE_URL="https://tripemilestone.in-maa-1.linodeobjects.com"

echo "✅ Environment variables set:"
echo "  - VITE_API_SERVER_URL: $VITE_API_SERVER_URL"
echo "  - VITE_API_URL: $VITE_API_URL"
echo "  - VITE_FRONTEND_URL: $VITE_FRONTEND_URL"
echo "  - VITE_TARIFF_API_URL: $VITE_TARIFF_API_URL"
echo "  - VITE_LINODE_STORAGE_URL: $VITE_LINODE_STORAGE_URL"

# Navigate to project directory
cd Tripmilestone-admin-fronend

echo "📦 Installing dependencies..."
export NVM_DIR=~/.nvm
source ~/.nvm/nvm.sh
npm install

echo "🏗️  Building application with production environment..."
npm run build

echo "🔄 Restarting PM2 process..."
pm2 restart admin-frontend

echo "📊 PM2 Status:"
pm2 list

echo "✅ Production setup completed!"
echo "🎉 Your application is now configured for production!"
echo ""
echo "🌐 Frontend URL: https://admin.tripxplo.com"
echo "🔌 API URL: https://api.tripxplo.com/v1"
echo ""
echo "Note: Make sure your backend API at https://api.tripxplo.com allows CORS from https://admin.tripxplo.com" 
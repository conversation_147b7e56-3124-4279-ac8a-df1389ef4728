import { ColumnDef } from "@tanstack/react-table"

import { Button } from "@/components/ui/button"
interface locationData {
   
        address: string;
        country: string;
        destinationId: string;
        lat: string;
        lon: string;
        state: string;
        _id: string;
 
}
interface Contact {
    additionalEmail: string;
    businessEmail: string;
    maintainerPhoneNo: number;
  }

import {
    DropdownMenu,
   
    DropdownMenuContent,
 
    DropdownMenuItem,

    DropdownMenuTrigger,
  } from "@/components/ui/dropdown-menu"
import {   BedSingleIcon, Eye, MoreHorizontal, Pencil, Trash,} from "lucide-react";

import { Hotel } from "@/types/types";
import { Link} from "react-router-dom";
import Delete from "./DeleteHotel";

export const columns:ColumnDef<Hotel>[] = [
    {
        accessorKey : "hotelName",
        header : ()=>{
            return(
                <h1  className="text-center">
                    Hotel Name
                </h1>
            )
        },
        cell : ({row})=>{

            const hotelName : string =  row.getValue("hotelName")
     
            
            return(
            
             
             <div className="text-center">
             {hotelName}
             </div>
              

            )
        }
    },
    {
        accessorKey : "location",
        header : ()=>{
            return(
                <h1 >
                    Location
                </h1>
            )
        },
        cell : ({row})=>{
            const locationData:locationData =  row.getValue("location")
            
            return(
            
                <>
                {locationData.state} , {locationData.country}
                </>
              

            )
        }
    },
    {
        accessorKey : "roomCount",
        header : ()=>{
            return(
                <h1 >
                    Rooms
                </h1>
            )
        },
        cell : ({row})=>{
            const roomCount:number =  row.getValue("roomCount")
            
            return(
            
                <div className="flex items-center gap-2">
                      {roomCount} <BedSingleIcon size={18}/>
              
                </div>
              

            )
        }
    },
    {
        accessorKey : "contract",
        header : ()=>{
            return(
                <h1 >
                    Contact Email
                </h1>
            )
        },
        cell : ({row})=>{
            const contact:Contact =  row.getValue("contract")
            
            return(
            
                <>
                {contact.businessEmail}
                
                </>
              

            )
        }
    },
    {
        accessorKey : 'Actions',
        header : ()=>{
            return(
                <h1 >
                    Actions
                </h1>
            )
        },
    
        cell : ({row}) =>{
         const {hotelId} = row.original
         
        
         return ( <DropdownMenu>
           <DropdownMenuTrigger asChild>
             <Button variant={"ghost"} className="h-4 w-8 p-0">
               <span className="sr-only"> Open menu</span>
               <MoreHorizontal className="h-4 w-4"/>
             </Button>
           </DropdownMenuTrigger>
           <DropdownMenuContent align="end">
           <Link to={`/hotels/${hotelId}`}>
                 <DropdownMenuItem className="flex items-center gap-2">
                   <Eye className="h-4 w-4 mr-2 text-green-500" />
                   View
                 </DropdownMenuItem>
               </Link>
               <Link to={`/hotels/edit/${hotelId}`}>
                 <DropdownMenuItem className="flex items-center gap-2">
                   <Pencil className="h-4 w-4 mr-2 text-slate-700" />
                   Edit
                 </DropdownMenuItem>
               </Link>
               <DropdownMenuItem className="flex items-center gap-2">
                   <Trash className="h-4 w-4 mr-2 text-red-500" />
                   <Delete hotelId={hotelId} />
                 </DropdownMenuItem>


              
             </DropdownMenuContent>
   
         </DropdownMenu>)
        }
     }
   
]
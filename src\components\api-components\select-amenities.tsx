import { Amenity } from '@/types/types';
import React from 'react';

interface SelectDestinationProps {
  options: Amenity[];
  placeholder: string;
  disabled?: boolean;
  onChange: (value: string) => void;
}
const SelectAmenities: React.FC<SelectDestinationProps> = ({
  options,
  placeholder,
  disabled = false,
  onChange,
}) => {
  return (
   <div className=''>
     <select 
      disabled={disabled}
      onChange={(e) => onChange(e.target.value)}
      className=" border rounded-md p-2  w-64"
      defaultValue=""
    >
      <option value="" className='text-slate-400' disabled hidden>{placeholder}</option>
      {options?.map((option) => (
        <option key={option._id} value={option._id}>
          {option.name}
        </option>
      ))}
    </select>
   </div>
  );
};

export default SelectAmenities;

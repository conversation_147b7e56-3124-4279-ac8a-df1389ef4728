#!/usr/bin/env python3
"""
Price Extractor for Tariff Extraction

This module provides enhanced price extraction functionality,
with improved meal plan identification and date range association.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Union, Set

from utils.table_structure_analyzer import ColumnRole, RowType

# Configure logging
logger = logging.getLogger('tariff_extractor')

class PriceExtractor:
    """Enhanced price extractor with improved meal plan identification and date range association"""
    
    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the price extractor
        
        Args:
            config: Optional configuration object
        """
        self.config = config
        
        # Default meal plan mapping
        self.meal_plan_mapping = {
            'ep': ['ep', 'european', 'room only', 'without meals'],
            'cp': ['cp', 'continental', 'breakfast', 'bed and breakfast', 'b&b'],
            'map': ['map', 'modified american', 'half board', 'breakfast and dinner'],
            'ap': ['ap', 'american', 'full board', 'all meals']
        }
        
        # Load meal plan mapping from config if available
        if config and hasattr(config, 'get_full_config'):
            config_mapping = config.get_full_config().get('meal_plan_mapping', {})
            for plan, keywords in config_mapping.items():
                if plan in self.meal_plan_mapping:
                    self.meal_plan_mapping[plan].extend(keywords)
                else:
                    self.meal_plan_mapping[plan] = keywords
        
        # Statistics for extraction operations
        self.extraction_stats = {
            'total_extractions': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'cp_prices_extracted': 0,
            'map_prices_extracted': 0,
            'ap_prices_extracted': 0,
            'ep_prices_extracted': 0,
            'derived_map_prices': 0,
            'derived_ap_prices': 0
        }
    
    def extract_prices(self, table: List[List[str]], column_roles: Dict[int, str], 
                      row_types: Dict[int, str], room_types: List[str], 
                      date_ranges: List[Dict[str, Any]], 
                      extra_charges: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract prices from a table
        
        Args:
            table: Table to extract prices from
            column_roles: Dictionary mapping column indices to column roles
            row_types: Dictionary mapping row indices to row types
            room_types: List of room types
            date_ranges: List of date ranges
            extra_charges: Dictionary of extra charges
            
        Returns:
            List of dictionaries containing extracted prices
        """
        results = []
        
        # Convert column roles from strings to ColumnRole enum
        enum_column_roles = {}
        for col_idx, role_name in column_roles.items():
            try:
                enum_column_roles[int(col_idx)] = ColumnRole[role_name]
            except (KeyError, ValueError):
                logger.warning(f"Invalid column role: {role_name}")
        
        # Convert row types from strings to RowType enum
        enum_row_types = {}
        for row_idx, type_name in row_types.items():
            try:
                enum_row_types[int(row_idx)] = RowType[type_name]
            except (KeyError, ValueError):
                logger.warning(f"Invalid row type: {type_name}")
        
        # Get data row indices
        data_row_indices = [int(row_idx) for row_idx, row_type in enum_row_types.items() 
                           if row_type == RowType.DATA]
        
        # Get room type column index
        room_col_idx = -1
        for col_idx, role in enum_column_roles.items():
            if role == ColumnRole.ROOM_TYPE:
                room_col_idx = col_idx
                break
        
        # If we couldn't find a room type column, use the first column
        if room_col_idx == -1 and len(table) > 0 and len(table[0]) > 0:
            room_col_idx = 0
            logger.warning("Room type column not found, using first column")
        
        # Get price column indices by meal plan
        price_cols = {
            'cp': [],
            'map': [],
            'ap': [],
            'ep': [],
            'net': [],
            'rack': [],
            'generic': []
        }
        
        for col_idx, role in enum_column_roles.items():
            if role == ColumnRole.PRICE_CP:
                price_cols['cp'].append(col_idx)
            elif role == ColumnRole.PRICE_MAP:
                price_cols['map'].append(col_idx)
            elif role == ColumnRole.PRICE_AP:
                price_cols['ap'].append(col_idx)
            elif role == ColumnRole.PRICE_EP:
                price_cols['ep'].append(col_idx)
            elif role == ColumnRole.PRICE_NET:
                price_cols['net'].append(col_idx)
            elif role == ColumnRole.PRICE_RACK:
                price_cols['rack'].append(col_idx)
            elif role == ColumnRole.PRICE_GENERIC:
                price_cols['generic'].append(col_idx)
        
        # Prioritize net rates over rack rates
        prioritized_price_cols = []
        
        # First, add specific meal plan columns
        for meal_plan in ['cp', 'map', 'ap', 'ep']:
            if price_cols[meal_plan]:
                prioritized_price_cols.extend([(col_idx, meal_plan) for col_idx in price_cols[meal_plan]])
        
        # Then, add net rate columns
        if price_cols['net']:
            # Default net rates to CP if no meal plan is specified
            prioritized_price_cols.extend([(col_idx, 'cp') for col_idx in price_cols['net']])
        
        # Then, add generic price columns
        if price_cols['generic']:
            # Default generic prices to CP if no meal plan is specified
            prioritized_price_cols.extend([(col_idx, 'cp') for col_idx in price_cols['generic']])
        
        # Finally, add rack rate columns if no other price columns are available
        if not prioritized_price_cols and price_cols['rack']:
            # Default rack rates to CP if no meal plan is specified
            prioritized_price_cols.extend([(col_idx, 'cp') for col_idx in price_cols['rack']])
        
        # Process each data row
        for row_idx in data_row_indices:
            if row_idx >= len(table):
                continue
            
            row = table[row_idx]
            
            # Skip rows that don't have enough columns
            if room_col_idx >= len(row):
                continue
            
            # Get room type
            room_type = str(row[room_col_idx]).strip()
            
            # Skip empty room types
            if not room_type:
                continue
            
            # Match room type to standardized room types
            matched_room_type = None
            for std_room_type in room_types:
                if room_type.lower() == std_room_type.lower():
                    matched_room_type = std_room_type
                    break
            
            # If no match found, skip this row
            if not matched_room_type:
                logger.warning(f"Room type '{room_type}' not found in standardized room types")
                continue
            
            # Extract prices for each prioritized price column
            for col_idx, meal_plan in prioritized_price_cols:
                if col_idx >= len(row):
                    continue
                
                # Extract price
                price_text = str(row[col_idx])
                price = self.extract_price(price_text)
                
                # Skip invalid prices
                if price <= 0:
                    continue
                
                # Create entries for each date range
                if date_ranges:
                    for date_range in date_ranges:
                        results.append({
                            "room_type": matched_room_type,
                            "meal_plan": meal_plan,
                            "price": price,
                            "start_date": date_range['start_date'],
                            "end_date": date_range['end_date'],
                            "is_derived": False,
                            "source": "table",
                            "confidence": 1.0
                        })
                        
                        # Update statistics
                        self._update_meal_plan_stats(meal_plan)
                else:
                    # No date ranges, create entry without dates
                    results.append({
                        "room_type": matched_room_type,
                        "meal_plan": meal_plan,
                        "price": price,
                        "is_derived": False,
                        "source": "table",
                        "confidence": 0.8  # Lower confidence without date ranges
                    })
                    
                    # Update statistics
                    self._update_meal_plan_stats(meal_plan)
        
        # Derive MAP and AP prices from CP prices and supplements
        if extra_charges:
            map_supplement = extra_charges.get('map_supplement', 0)
            ap_supplement = extra_charges.get('ap_supplement', 0)
            
            # If we have CP prices and MAP supplement, derive MAP prices
            if map_supplement > 0:
                cp_entries = [entry for entry in results if entry['meal_plan'] == 'cp']
                for cp_entry in cp_entries:
                    # Check if we already have a MAP price for this room type and date range
                    has_map = any(entry['meal_plan'] == 'map' and 
                                 entry['room_type'] == cp_entry['room_type'] and 
                                 (not date_ranges or 
                                  (entry.get('start_date') == cp_entry.get('start_date') and 
                                   entry.get('end_date') == cp_entry.get('end_date')))
                                 for entry in results)
                    
                    if not has_map:
                        # Create derived MAP entry
                        map_entry = cp_entry.copy()
                        map_entry['meal_plan'] = 'map'
                        map_entry['price'] = cp_entry['price'] + map_supplement
                        map_entry['is_derived'] = True
                        map_entry['confidence'] = 0.9  # Slightly lower confidence for derived prices
                        results.append(map_entry)
                        
                        # Update statistics
                        self.extraction_stats['derived_map_prices'] += 1
                        self.extraction_stats['map_prices_extracted'] += 1
            
            # If we have CP prices and AP supplement, derive AP prices
            if ap_supplement > 0:
                cp_entries = [entry for entry in results if entry['meal_plan'] == 'cp']
                for cp_entry in cp_entries:
                    # Check if we already have an AP price for this room type and date range
                    has_ap = any(entry['meal_plan'] == 'ap' and 
                                entry['room_type'] == cp_entry['room_type'] and 
                                (not date_ranges or 
                                 (entry.get('start_date') == cp_entry.get('start_date') and 
                                  entry.get('end_date') == cp_entry.get('end_date')))
                                for entry in results)
                    
                    if not has_ap:
                        # Create derived AP entry
                        ap_entry = cp_entry.copy()
                        ap_entry['meal_plan'] = 'ap'
                        ap_entry['price'] = cp_entry['price'] + ap_supplement
                        ap_entry['is_derived'] = True
                        ap_entry['confidence'] = 0.9  # Slightly lower confidence for derived prices
                        results.append(ap_entry)
                        
                        # Update statistics
                        self.extraction_stats['derived_ap_prices'] += 1
                        self.extraction_stats['ap_prices_extracted'] += 1
        
        # Update statistics
        self.extraction_stats['total_extractions'] += 1
        if results:
            self.extraction_stats['successful_extractions'] += 1
        else:
            self.extraction_stats['failed_extractions'] += 1
        
        return results
    
    def extract_price(self, price_text: str) -> float:
        """
        Extract numeric price from string
        
        Args:
            price_text: Text containing a price
            
        Returns:
            Extracted price as float, or 0 if no price found
        """
        if not price_text:
            return 0
        
        # Extract all numbers from the string
        numbers = re.findall(r'[\d,]+\.?\d*', str(price_text))
        if not numbers:
            return 0
        
        # Take the first number and convert to float
        price_str = numbers[0].replace(',', '')
        try:
            return float(price_str)
        except ValueError:
            return 0
    
    def normalize_meal_plan(self, meal_plan_text: str) -> str:
        """
        Normalize meal plan to standard format (ep, cp, map, ap)
        
        Args:
            meal_plan_text: Meal plan text to normalize
            
        Returns:
            Normalized meal plan
        """
        if not meal_plan_text:
            return 'cp'  # Default to CP
        
        meal_plan_text = meal_plan_text.lower()
        
        for plan, keywords in self.meal_plan_mapping.items():
            for keyword in keywords:
                if keyword in meal_plan_text:
                    return plan
        
        # Default to CP if unknown
        return 'cp'
    
    def _update_meal_plan_stats(self, meal_plan: str) -> None:
        """
        Update meal plan statistics
        
        Args:
            meal_plan: Meal plan to update statistics for
        """
        if meal_plan == 'cp':
            self.extraction_stats['cp_prices_extracted'] += 1
        elif meal_plan == 'map':
            self.extraction_stats['map_prices_extracted'] += 1
        elif meal_plan == 'ap':
            self.extraction_stats['ap_prices_extracted'] += 1
        elif meal_plan == 'ep':
            self.extraction_stats['ep_prices_extracted'] += 1
    
    def get_extraction_stats(self) -> Dict[str, int]:
        """
        Get statistics about extraction operations
        
        Returns:
            Dictionary of extraction statistics
        """
        return self.extraction_stats

/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from "react";
import { input_field_css } from "../PackageForm";
export interface PackageMultiSelectProp{
  initialData:string[],
    dataName:string,
    dataId:string,
    allData:any[],
    pHolder:string,
    setDatas:React.Dispatch<React.SetStateAction<string[]>>
}
export default function PackageMultiSelect(props:PackageMultiSelectProp) {
    const [selectedData,setSelectedData] = useState<any[]>([])
    const [restData,setRestData] = useState<any[]>(props.allData)
    const [view,setView] = useState(false)
    const [textInput,setTextInput] = useState("")
    const inputRef = useRef<HTMLInputElement>(null)
  function handleInput(inp: string) {
    props.setDatas((prev)=>[...prev,inp])
    setSelectedData((prev)=>[...prev,inp]);
    setTextInput("")
  }
  function handleClose(inp:string){
    const data = selectedData.filter((k) => {
        return k[props.dataId]!==inp
      });
      props.setDatas(data)
      setSelectedData(data)
  }
  function handleInputText(inp:string){
    setTextInput(inp)
    const data = props.allData.filter((k)=>{
     return k[props.dataName].toLowerCase()?.includes(inp.toLowerCase()) && !selectedData.some((j) => j[props.dataId] === k[props.dataId]);
    })
    setRestData(data)
  }
  useEffect(() => {
    setRestData(props.allData);
  }, [props.allData]);
  useEffect(() => {
    const data = props.allData.filter((k) => {
      return !selectedData.some((j) => j[props.dataId] === k[props.dataId]);
    });
    setRestData(data);
    
  }, [selectedData]);

  useEffect(()=>{
    setSelectedData(props.initialData)
  },[props.initialData])
  return (
    <div className="w-full border relative py-2 text-xl px-2 flex flex-wrap">
        {
            selectedData?.length>0?selectedData?.map((k)=><div key={k[props.dataId]} className="text-sm text-white bg-blue-500 m-1 p-1 rounded-lg flex items-center">{k[props.dataName]}<div onClick={()=>handleClose(k[props.dataId])} className="bg-blue-600 flex justify-center items-center cursor-pointer w-[20px] h-[20px] ml-4 rounded-full font-bold">x</div></div>):""
        }
      <div className="relative">
      <input ref={inputRef} placeholder={props.pHolder} onFocus={()=>setView(true)}  type="text" value={textInput} className={input_field_css} onInput={(e:any)=>handleInputText(e.target.value)} onBlur={()=>setView(false)}/>
    {
        view && <div  className="w-full top-10  max-h-[200px] border overflow-y-auto z-30 bg-white shadow-2xl absolute  m-1">
        { restData?.length > 0 ? restData?.map((k)=><div key={k[props.dataId]} onMouseDown={()=>handleInput(k)} className="p-1 cursor-pointer hover:bg-slate-100 text-lg">{k[props.dataName]}</div>): <div className="text-red-500">nothing found</div>}
          </div>
    }  
      </div> 
    </div>
  );
}

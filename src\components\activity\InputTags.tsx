import React, { useState } from "react";
import { label_css } from "../package/PackageForm";

interface InputTagsProps {
  allInputs: string[];
  setAllInputs: React.Dispatch<React.SetStateAction<string[]>>;
}

export default function InputTags(props: InputTagsProps) {
  const [input, setInput] = useState("");

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault(); // Prevents form submission if wrapped in a form element
      if (input.trim() !== "") {
        props.setAllInputs((prevInputs) => [...prevInputs, input.trim()]);
        setInput("");
      }
    }else if(e.key === 'Backspace' && input === ""){
        props.setAllInputs((prevInputs) => prevInputs.slice(0,prevInputs.length-1));
        setInput("");
    }
  };

  return (
    <div>
      <label htmlFor="activitytags" className={label_css}>
        Tags
      </label>
      <div className="text-black flex flex-wrap rounded-lg rounded-lg border-2 text-xl">
        {props.allInputs?.map((tag: string, index: number) => (
          <div key={index} className="mx-2 px-2 text-sm my-1 bg-lime-300  rounded-r-full rounded-l-full">
            {tag}
          </div>
        ))}
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Enter Tags"
          className="outline-none mx-2 w-[150px] border-none px-2"
        />
      </div>
    </div>
  );
}

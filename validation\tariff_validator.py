#!/usr/bin/env python3
"""
Tariff Data Validator

This module provides advanced validation rules for extracted tariff data.
It checks for logical consistency, outliers, and completeness of the data.
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional, Set
import statistics

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('tariff_validator')

# Constants
PRICE_OUTLIER_THRESHOLD = 3.0  # Standard deviations for price outlier detection
MIN_EXPECTED_ENTRIES = 3  # Minimum expected entries for a complete extraction
MAX_PRICE_RATIO = 5.0  # Maximum ratio between highest and lowest price for same room type
COMMON_ROOM_TYPES = {
    "standard", "deluxe", "super deluxe", "suite", "executive", "premium",
    "cottage", "villa", "room", "view"
}
EXPECTED_MEAL_PLANS = {"cp", "map"}  # Most hotels should have at least CP and MAP

class TariffValidator:
    """Validator for tariff data with advanced validation rules"""

    def __init__(self, hotel_name: Optional[str] = None):
        """
        Initialize the validator

        Args:
            hotel_name: Optional hotel name for hotel-specific validation rules
        """
        self.hotel_name = hotel_name
        self.validation_rules = [
            self.validate_required_fields,
            self.validate_date_ranges,
            self.validate_price_validity,
            self.validate_price_outliers,
            self.validate_meal_plan_consistency,
            self.validate_room_type_consistency,
            self.validate_completeness,
            self.validate_hotel_specific_rules
        ]

    def validate(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate the extracted tariff data

        Args:
            data: List of extracted tariff entries

        Returns:
            List of validation issues with severity levels
        """
        issues = []

        # Skip validation if no data
        if not data:
            issues.append({
                "type": "no_data",
                "message": "No data to validate",
                "severity": "error"
            })
            return issues

        # Apply all validation rules
        for rule in self.validation_rules:
            rule_issues = rule(data)
            if rule_issues:
                issues.extend(rule_issues)

        return issues

    def validate_required_fields(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Check that all entries have required fields"""
        issues = []
        required_fields = ["roomType", "mealPlanType", "roomPrice"]

        for i, entry in enumerate(data):
            missing_fields = [field for field in required_fields if field not in entry or not entry[field]]
            if missing_fields:
                issues.append({
                    "type": "missing_fields",
                    "message": f"Entry {i}: Missing required fields: {', '.join(missing_fields)}",
                    "entry_index": i,
                    "missing_fields": missing_fields,
                    "severity": "error"
                })

        return issues

    def validate_date_ranges(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate date ranges for logical consistency"""
        issues = []

        for i, entry in enumerate(data):
            # Skip entries without date ranges
            if "startDate" not in entry or "endDate" not in entry:
                continue

            try:
                start_date = datetime.strptime(entry["startDate"], "%Y-%m-%d")
                end_date = datetime.strptime(entry["endDate"], "%Y-%m-%d")

                # Check if end date is before start date
                if end_date < start_date:
                    issues.append({
                        "type": "invalid_date_range",
                        "message": f"Entry {i}: End date {entry['endDate']} is before start date {entry['startDate']}",
                        "entry_index": i,
                        "start_date": entry["startDate"],
                        "end_date": entry["endDate"],
                        "severity": "error"
                    })

                # Check if date range is too short (less than 7 days)
                days_diff = (end_date - start_date).days
                if days_diff < 7:
                    issues.append({
                        "type": "short_date_range",
                        "message": f"Entry {i}: Date range is only {days_diff} days",
                        "entry_index": i,
                        "days_diff": days_diff,
                        "severity": "warning"
                    })

                # Check if date range is too long (more than 1 year)
                if days_diff > 366:
                    issues.append({
                        "type": "long_date_range",
                        "message": f"Entry {i}: Date range is {days_diff} days (more than 1 year)",
                        "entry_index": i,
                        "days_diff": days_diff,
                        "severity": "warning"
                    })
            except (ValueError, TypeError):
                issues.append({
                    "type": "invalid_date_format",
                    "message": f"Entry {i}: Invalid date format",
                    "entry_index": i,
                    "severity": "error"
                })

        # Check for gaps or overlaps in date ranges
        if len(data) > 1:
            # Group entries by room type and meal plan
            grouped_entries = {}
            for i, entry in enumerate(data):
                if "startDate" not in entry or "endDate" not in entry:
                    continue

                key = (entry.get("roomType", ""), entry.get("mealPlanType", ""))
                if key not in grouped_entries:
                    grouped_entries[key] = []

                grouped_entries[key].append((i, entry))

            # Check each group for gaps and overlaps
            for key, entries in grouped_entries.items():
                if len(entries) <= 1:
                    continue

                # Sort entries by start date
                sorted_entries = sorted(entries, key=lambda x: x[1]["startDate"])

                # Check for gaps and overlaps
                for j in range(len(sorted_entries) - 1):
                    curr_idx, curr_entry = sorted_entries[j]
                    next_idx, next_entry = sorted_entries[j + 1]

                    curr_end = datetime.strptime(curr_entry["endDate"], "%Y-%m-%d")
                    next_start = datetime.strptime(next_entry["startDate"], "%Y-%m-%d")

                    # Check for gap
                    if (next_start - curr_end).days > 1:
                        issues.append({
                            "type": "date_range_gap",
                            "message": f"Gap of {(next_start - curr_end).days - 1} days between entries {curr_idx} and {next_idx}",
                            "entry_indices": [curr_idx, next_idx],
                            "gap_days": (next_start - curr_end).days - 1,
                            "severity": "warning"
                        })

                    # Check for overlap
                    if next_start <= curr_end:
                        issues.append({
                            "type": "date_range_overlap",
                            "message": f"Overlap of {(curr_end - next_start).days + 1} days between entries {curr_idx} and {next_idx}",
                            "entry_indices": [curr_idx, next_idx],
                            "overlap_days": (curr_end - next_start).days + 1,
                            "severity": "error"
                        })

        return issues

    def validate_price_validity(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate price values for basic validity"""
        issues = []

        for i, entry in enumerate(data):
            price = entry.get("roomPrice", 0)

            # Check for negative or zero prices
            if price < 0:
                issues.append({
                    "type": "negative_price",
                    "message": f"Entry {i}: Negative price {price}",
                    "entry_index": i,
                    "price": price,
                    "severity": "error"
                })
            elif price == 0:
                issues.append({
                    "type": "zero_price",
                    "message": f"Entry {i}: Zero price",
                    "entry_index": i,
                    "severity": "error"
                })

            # Check for unrealistically low prices (less than 500)
            elif price < 500:
                issues.append({
                    "type": "low_price",
                    "message": f"Entry {i}: Unusually low price {price}",
                    "entry_index": i,
                    "price": price,
                    "severity": "warning"
                })

            # Check for unrealistically high prices (more than 100,000)
            elif price > 100000:
                issues.append({
                    "type": "high_price",
                    "message": f"Entry {i}: Unusually high price {price}",
                    "entry_index": i,
                    "price": price,
                    "severity": "warning"
                })

        return issues

    def validate_price_outliers(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect price outliers using statistical methods"""
        issues = []

        # Group entries by room type and meal plan
        grouped_entries = {}
        for i, entry in enumerate(data):
            key = (entry.get("roomType", ""), entry.get("mealPlanType", ""))
            if key not in grouped_entries:
                grouped_entries[key] = []

            grouped_entries[key].append((i, entry))

        # Check each group for price outliers
        for key, entries in grouped_entries.items():
            if len(entries) <= 2:  # Need at least 3 entries for meaningful statistics
                continue

            prices = [entry[1].get("roomPrice", 0) for entry in entries]

            try:
                # Calculate statistics
                mean_price = statistics.mean(prices)
                stdev_price = statistics.stdev(prices)

                # Check for outliers
                for i, (idx, entry) in enumerate(entries):
                    price = entry.get("roomPrice", 0)
                    z_score = abs(price - mean_price) / stdev_price if stdev_price > 0 else 0

                    if z_score > PRICE_OUTLIER_THRESHOLD:
                        issues.append({
                            "type": "price_outlier",
                            "message": f"Entry {idx}: Price {price} is an outlier (z-score: {z_score:.2f})",
                            "entry_index": idx,
                            "price": price,
                            "mean_price": mean_price,
                            "z_score": z_score,
                            "severity": "warning"
                        })
            except statistics.StatisticsError:
                # Skip if statistics calculation fails
                pass

        # Check for inconsistent price progression across room types
        room_types_by_price = {}
        for entry in data:
            room_type = entry.get("roomType", "")
            meal_plan = entry.get("mealPlanType", "")
            price = entry.get("roomPrice", 0)

            if meal_plan not in room_types_by_price:
                room_types_by_price[meal_plan] = {}

            if room_type not in room_types_by_price[meal_plan]:
                room_types_by_price[meal_plan][room_type] = []

            room_types_by_price[meal_plan][room_type].append(price)

        # Calculate average price for each room type
        avg_prices = {}
        for meal_plan, room_types in room_types_by_price.items():
            avg_prices[meal_plan] = {}
            for room_type, prices in room_types.items():
                if prices:
                    avg_prices[meal_plan][room_type] = sum(prices) / len(prices)

        # Check for inconsistent price progression
        for meal_plan, room_prices in avg_prices.items():
            if len(room_prices) <= 1:
                continue

            # Try to identify room type hierarchy
            room_hierarchy = []
            for room_type in room_prices:
                # Look for common room type indicators
                for i, indicator in enumerate(["standard", "deluxe", "super", "suite", "premium"]):
                    if indicator in room_type.lower():
                        room_hierarchy.append((room_type, i))
                        break

            # Skip if we couldn't determine hierarchy
            if len(room_hierarchy) <= 1:
                continue

            # Sort by expected hierarchy
            room_hierarchy.sort(key=lambda x: x[1])

            # Check if prices follow the hierarchy
            prev_room, _ = room_hierarchy[0]
            prev_price = avg_prices[meal_plan][prev_room]

            for room_type, _ in room_hierarchy[1:]:
                curr_price = avg_prices[meal_plan][room_type]

                if curr_price < prev_price:
                    issues.append({
                        "type": "inconsistent_price_hierarchy",
                        "message": f"Room type '{room_type}' has lower price ({curr_price}) than '{prev_room}' ({prev_price})",
                        "room_types": [prev_room, room_type],
                        "prices": [prev_price, curr_price],
                        "meal_plan": meal_plan,
                        "severity": "warning"
                    })

                prev_room = room_type
                prev_price = curr_price

        return issues

    def validate_meal_plan_consistency(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate meal plan consistency across entries"""
        issues = []

        # Get all meal plans and room types
        meal_plans = set(entry.get("mealPlanType", "") for entry in data)
        room_types = set(entry.get("roomType", "") for entry in data)

        # Check for expected meal plans
        missing_meal_plans = EXPECTED_MEAL_PLANS - meal_plans
        if missing_meal_plans:
            issues.append({
                "type": "missing_meal_plans",
                "message": f"Missing expected meal plans: {', '.join(missing_meal_plans)}",
                "missing_meal_plans": list(missing_meal_plans),
                "severity": "warning"
            })

        # Check for consistent meal plans across room types
        meal_plans_by_room = {}
        for entry in data:
            room_type = entry.get("roomType", "")
            meal_plan = entry.get("mealPlanType", "")

            if room_type not in meal_plans_by_room:
                meal_plans_by_room[room_type] = set()

            meal_plans_by_room[room_type].add(meal_plan)

        # Find room types with inconsistent meal plans
        reference_meal_plans = None
        for room_type, plans in meal_plans_by_room.items():
            if reference_meal_plans is None:
                reference_meal_plans = plans
            elif plans != reference_meal_plans:
                issues.append({
                    "type": "inconsistent_meal_plans",
                    "message": f"Room type '{room_type}' has different meal plans ({', '.join(plans)}) than other room types",
                    "room_type": room_type,
                    "meal_plans": list(plans),
                    "reference_meal_plans": list(reference_meal_plans),
                    "severity": "warning"
                })

        # Check for consistent price differences between meal plans
        if "cp" in meal_plans and "map" in meal_plans:
            cp_map_diffs = []

            for room_type in room_types:
                cp_prices = [entry.get("roomPrice", 0) for entry in data
                            if entry.get("roomType") == room_type and entry.get("mealPlanType") == "cp"]
                map_prices = [entry.get("roomPrice", 0) for entry in data
                             if entry.get("roomType") == room_type and entry.get("mealPlanType") == "map"]

                if cp_prices and map_prices:
                    avg_cp = sum(cp_prices) / len(cp_prices)
                    avg_map = sum(map_prices) / len(map_prices)
                    diff = avg_map - avg_cp

                    if diff > 0:
                        cp_map_diffs.append((room_type, diff))

            # Check for consistent differences
            if len(cp_map_diffs) > 1:
                diffs = [diff for _, diff in cp_map_diffs]
                avg_diff = sum(diffs) / len(diffs)

                for room_type, diff in cp_map_diffs:
                    # If difference varies by more than 30% from average
                    if abs(diff - avg_diff) / avg_diff > 0.3:
                        issues.append({
                            "type": "inconsistent_meal_plan_diff",
                            "message": f"Room type '{room_type}' has unusual CP-MAP price difference ({diff}) compared to average ({avg_diff})",
                            "room_type": room_type,
                            "diff": diff,
                            "avg_diff": avg_diff,
                            "severity": "warning"
                        })

        return issues

    def validate_room_type_consistency(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate room type consistency and naming"""
        issues = []

        # Get all room types
        room_types = [entry.get("roomType", "") for entry in data]
        unique_room_types = set(room_types)

        # Check for similar room types that might be duplicates
        similar_room_types = []
        checked_pairs = set()

        for rt1 in unique_room_types:
            for rt2 in unique_room_types:
                if rt1 == rt2 or (rt1, rt2) in checked_pairs or (rt2, rt1) in checked_pairs:
                    continue

                checked_pairs.add((rt1, rt2))

                # Check for similarity
                rt1_lower = rt1.lower()
                rt2_lower = rt2.lower()

                # If one is a substring of the other
                if rt1_lower in rt2_lower or rt2_lower in rt1_lower:
                    similar_room_types.append((rt1, rt2))

        if similar_room_types:
            for rt1, rt2 in similar_room_types:
                issues.append({
                    "type": "similar_room_types",
                    "message": f"Similar room types found: '{rt1}' and '{rt2}'",
                    "room_types": [rt1, rt2],
                    "severity": "warning"
                })

        # Check for unusual room type names
        for room_type in unique_room_types:
            if not any(common in room_type.lower() for common in COMMON_ROOM_TYPES):
                issues.append({
                    "type": "unusual_room_type",
                    "message": f"Unusual room type name: '{room_type}'",
                    "room_type": room_type,
                    "severity": "info"
                })

        # Check for consistent price ratios between room types
        room_type_prices = {}
        for entry in data:
            room_type = entry.get("roomType", "")
            meal_plan = entry.get("mealPlanType", "")
            price = entry.get("roomPrice", 0)

            if price <= 0:
                continue

            key = (room_type, meal_plan)
            if key not in room_type_prices:
                room_type_prices[key] = []

            room_type_prices[key].append(price)

        # Calculate average price for each room type and meal plan
        avg_prices = {}
        for key, prices in room_type_prices.items():
            if prices:
                avg_prices[key] = sum(prices) / len(prices)

        # Check price ratios
        for meal_plan in set(key[1] for key in avg_prices.keys()):
            meal_plan_prices = {key[0]: price for key, price in avg_prices.items() if key[1] == meal_plan}

            if len(meal_plan_prices) <= 1:
                continue

            min_price = min(meal_plan_prices.values())
            max_price = max(meal_plan_prices.values())

            if min_price > 0 and max_price / min_price > MAX_PRICE_RATIO:
                min_room = [rt for rt, p in meal_plan_prices.items() if p == min_price][0]
                max_room = [rt for rt, p in meal_plan_prices.items() if p == max_price][0]

                issues.append({
                    "type": "high_price_ratio",
                    "message": f"High price ratio ({max_price/min_price:.2f}) between room types '{min_room}' and '{max_room}'",
                    "room_types": [min_room, max_room],
                    "prices": [min_price, max_price],
                    "ratio": max_price / min_price,
                    "meal_plan": meal_plan,
                    "severity": "warning"
                })

        return issues

    def validate_completeness(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate completeness of the extraction"""
        issues = []

        # Check for minimum number of entries
        if len(data) < MIN_EXPECTED_ENTRIES:
            issues.append({
                "type": "few_entries",
                "message": f"Only {len(data)} entries found, expected at least {MIN_EXPECTED_ENTRIES}",
                "entry_count": len(data),
                "expected_count": MIN_EXPECTED_ENTRIES,
                "severity": "warning"
            })

        # Check for complete date coverage
        date_ranges = []
        for entry in data:
            if "startDate" in entry and "endDate" in entry:
                date_ranges.append((entry["startDate"], entry["endDate"]))

        if date_ranges:
            # Sort date ranges
            date_ranges.sort()

            # Check for full year coverage
            start_dates = [datetime.strptime(start, "%Y-%m-%d") for start, _ in date_ranges]
            end_dates = [datetime.strptime(end, "%Y-%m-%d") for _, end in date_ranges]

            if start_dates and end_dates:
                min_start = min(start_dates)
                max_end = max(end_dates)

                # Check if coverage is less than 10 months
                coverage_days = (max_end - min_start).days
                if coverage_days < 300:
                    issues.append({
                        "type": "incomplete_date_coverage",
                        "message": f"Date coverage is only {coverage_days} days, expected at least 300 days",
                        "coverage_days": coverage_days,
                        "start_date": min_start.strftime("%Y-%m-%d"),
                        "end_date": max_end.strftime("%Y-%m-%d"),
                        "severity": "warning"
                    })
        else:
            issues.append({
                "type": "missing_date_ranges",
                "message": "No date ranges found in the extracted data",
                "severity": "error"
            })

        # Check for expected fields
        extra_fields = ["extraAdultCharge", "extraChildWithBedCharge", "extraChildWithoutBedCharge"]
        missing_extra_fields = []

        for field in extra_fields:
            if not any(field in entry for entry in data):
                missing_extra_fields.append(field)

        if missing_extra_fields:
            issues.append({
                "type": "missing_extra_fields",
                "message": f"Missing expected extra fields: {', '.join(missing_extra_fields)}",
                "missing_fields": missing_extra_fields,
                "severity": "info"
            })

        return issues

    def validate_hotel_specific_rules(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply hotel-specific validation rules"""
        issues = []

        if not self.hotel_name:
            return issues

        # Dew Drops Farm Resorts Munnar specific rules
        if "dew drops" in self.hotel_name.lower() or "farm resorts" in self.hotel_name.lower():
            # Expected room types
            expected_room_types = {"Plantation View", "Farm View", "Cottage"}
            found_room_types = set(entry.get("roomType", "") for entry in data)

            missing_room_types = expected_room_types - found_room_types
            if missing_room_types:
                issues.append({
                    "type": "missing_dew_drops_room_types",
                    "message": f"Missing expected Dew Drops room types: {', '.join(missing_room_types)}",
                    "missing_room_types": list(missing_room_types),
                    "severity": "warning"
                })

            # Expected date ranges
            expected_date_ranges = [
                ("2025-04-01", "2025-06-09"),
                ("2025-06-10", "2025-09-20"),
                ("2025-09-21", "2026-02-28"),
                ("2026-03-01", "2026-03-31")
            ]

            found_date_ranges = set((entry.get("startDate", ""), entry.get("endDate", ""))
                                   for entry in data
                                   if "startDate" in entry and "endDate" in entry)

            missing_date_ranges = [dr for dr in expected_date_ranges if dr not in found_date_ranges]
            if missing_date_ranges:
                issues.append({
                    "type": "missing_dew_drops_date_ranges",
                    "message": f"Missing expected Dew Drops date ranges: {missing_date_ranges}",
                    "missing_date_ranges": missing_date_ranges,
                    "severity": "warning"
                })

            # Check for MAP supplement
            map_entries = [entry for entry in data if entry.get("mealPlanType") == "map"]
            cp_entries = [entry for entry in data if entry.get("mealPlanType") == "cp"]

            if map_entries and cp_entries:
                # Group by room type and date range
                map_prices = {}
                cp_prices = {}

                for entry in map_entries:
                    key = (entry.get("roomType", ""), entry.get("startDate", ""), entry.get("endDate", ""))
                    map_prices[key] = entry.get("roomPrice", 0)

                for entry in cp_entries:
                    key = (entry.get("roomType", ""), entry.get("startDate", ""), entry.get("endDate", ""))
                    cp_prices[key] = entry.get("roomPrice", 0)

                # Check MAP supplement
                for key in set(map_prices.keys()) & set(cp_prices.keys()):
                    map_price = map_prices[key]
                    cp_price = cp_prices[key]
                    supplement = map_price - cp_price

                    if supplement != 1000:  # Expected MAP supplement is 500 × 2 people
                        issues.append({
                            "type": "incorrect_dew_drops_map_supplement",
                            "message": f"Incorrect MAP supplement for {key[0]}: {supplement} (expected 1000)",
                            "room_type": key[0],
                            "date_range": (key[1], key[2]),
                            "supplement": supplement,
                            "expected_supplement": 1000,
                            "severity": "warning"
                        })

        return issues
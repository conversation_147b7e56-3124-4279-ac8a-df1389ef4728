#!/usr/bin/env python3
"""
Tests for Phase 1 PDF Preparation Components

This module contains tests for the PDFLoader and TextCleaner components.
"""

import os
import sys
import re
import unittest
import logging
from typing import Dict, List, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import components to test
from utils.pdf_loader import PDFLoader
from utils.text_cleaner import TextCleaner
from utils.config_loader import ConfigLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_phase1')

class TestTextCleaner(unittest.TestCase):
    """Tests for the TextCleaner component"""

    def setUp(self):
        """Set up test environment"""
        self.config = ConfigLoader()
        self.cleaner = TextCleaner(self.config)

    def test_clean_text_general(self):
        """Test general text cleaning"""
        # Test with currency symbols and whitespace
        text = "Room rate: Rs. 5,000/- + GST per night"
        cleaned = self.cleaner.clean_text(text)
        # Remove any trailing "+" that might be left
        cleaned = re.sub(r'\s*\+\s*$', '', cleaned)
        self.assertEqual(cleaned, "Room rate: 5,000")

        # Test with multiple whitespace and special characters
        text = "Deluxe   Room  (Garden  View)  @  ₹5,000/-"
        cleaned = self.cleaner.clean_text(text)
        self.assertEqual(cleaned, "Deluxe Room (Garden View) @ 5,000")

    def test_clean_text_price(self):
        """Test price-specific text cleaning"""
        # Test with currency symbols and tax
        text = "Rs. 5,000/- + GST"
        cleaned = self.cleaner.clean_text(text, context="price")
        self.assertEqual(cleaned, "5000")

        # Test with preserving currency
        text = "₹5,000/- + GST"
        cleaned = self.cleaner.clean_text(text, context="price", preserve_currency=True)
        self.assertEqual(cleaned, "₹5000")

    def test_clean_text_room_type(self):
        """Test room type-specific text cleaning"""
        # Test with special characters
        text = "Deluxe Room (Garden View) - 2 Adults"
        cleaned = self.cleaner.clean_text(text, context="room_type")
        self.assertEqual(cleaned, "Deluxe Room (Garden View) 2 Adults")

    def test_clean_text_date(self):
        """Test date-specific text cleaning"""
        # Test with date formats
        text = "Valid from: 01/04/2025 to 30/06/2025"
        cleaned = self.cleaner.clean_text(text, context="date")
        self.assertEqual(cleaned, "Valid from 01/04/2025 to 30/06/2025")

    def test_extract_price(self):
        """Test price extraction"""
        # Test with currency symbols and commas
        self.assertEqual(self.cleaner.extract_price("Rs. 5,000/-"), 5000.0)
        self.assertEqual(self.cleaner.extract_price("₹5,000.50 + GST"), 5000.50)
        self.assertEqual(self.cleaner.extract_price("No price here"), 0.0)

    def test_clean_table(self):
        """Test table cleaning"""
        # Create a test table
        table = [
            ["Room Type", "CP Rate", "MAP Rate", "AP Rate"],
            ["Deluxe Room", "Rs. 5,000/-", "Rs. 6,000/-", "Rs. 7,000/-"],
            ["Super Deluxe", "Rs. 6,000/- + GST", "Rs. 7,000/- + GST", "Rs. 8,000/- + GST"]
        ]

        # Clean the table
        cleaned_table = self.cleaner.clean_table(table)

        # Check header is preserved
        self.assertEqual(cleaned_table[0], ["Room Type", "CP Rate", "MAP Rate", "AP Rate"])

        # Check data rows are cleaned - note that we're not removing commas in table cleaning
        self.assertEqual(cleaned_table[1][0], "Deluxe Room")
        self.assertEqual(cleaned_table[1][1], "5,000")
        self.assertEqual(cleaned_table[2][0], "Super Deluxe")
        self.assertEqual(cleaned_table[2][1], "6,000")

class TestPDFLoader(unittest.TestCase):
    """Tests for the PDFLoader component"""

    def setUp(self):
        """Set up test environment"""
        self.config = ConfigLoader()

        # Path to test PDF file
        self.test_pdf_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "Dew-Drop-Tariff-25-26.pdf"
        )

        # Skip tests if test PDF doesn't exist
        if not os.path.exists(self.test_pdf_path):
            self.skipTest(f"Test PDF file not found: {self.test_pdf_path}")

    def test_load_pdf(self):
        """Test PDF loading"""
        # Create PDF loader
        loader = PDFLoader(self.test_pdf_path, self.config)

        # Load PDF
        text, tables, metadata = loader.load_pdf()

        # Check that text was extracted
        self.assertGreater(len(text), 0, "No text extracted from PDF")

        # Check that tables were extracted
        self.assertGreater(len(tables), 0, "No tables extracted from PDF")

        # Check metadata
        self.assertIn('page_count', metadata, "Page count not in metadata")
        self.assertIn('extraction_stats', metadata, "Extraction stats not in metadata")

        # Log some information about the PDF
        logger.info(f"PDF loaded: {len(text)} chars, {len(tables)} tables, {metadata.get('page_count', 0)} pages")
        logger.info(f"PDF is {'scanned' if metadata.get('is_scanned', False) else 'digital'}")
        logger.info(f"OCR was {'used' if metadata.get('ocr_used', False) else 'not used'}")

if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python3
"""
Dynamic Table Analyzer for PDF Extraction

This module provides advanced table analysis capabilities for PDF extraction,
including column role identification, multi-header detection, and more.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Set, Union
from enum import Enum, auto
from collections import defaultdict

# Configure logging
logger = logging.getLogger('tariff_extractor')

class ColumnRole(Enum):
    """Enumeration of possible column roles in tariff tables"""
    ROOM_TYPE = auto()
    PRICE_NET = auto()
    PRICE_RACK = auto()
    MEAL_PLAN = auto()
    DATE_START = auto()
    DATE_END = auto()
    DATE_RANGE_TEXT = auto()
    DESCRIPTION = auto()
    OCCUPANCY = auto()
    NUMBER_OF_ROOMS = auto()
    EXTRA_ADULT = auto()
    EXTRA_CHILD = auto()
    UNKNOWN = auto()

class DynamicTableAnalyzer:
    """
    Analyzes tables extracted from PDFs to identify column roles and structure
    """
    
    def __init__(self, min_confidence: float = 0.6):
        """
        Initialize the analyzer
        
        Args:
            min_confidence: Minimum confidence score to assign a role (0.0-1.0)
        """
        self.min_confidence = min_confidence
        
        # Define keyword mappings for column roles
        self.role_keywords = {
            ColumnRole.ROOM_TYPE: [
                'room type', 'room category', 'accommodation', 'category', 'room', 
                'cottage', 'villa', 'suite', 'view', 'rooms'
            ],
            ColumnRole.PRICE_NET: [
                'special rate', 'net rate', 'agent rate', 'contract rate', 'offer price',
                'special tariff', 'net tariff', 'special', 'net', 'price', 'rate', 'tariff',
                'cost', 'charge', 'amount', 'inr', 'rs', '₹'
            ],
            ColumnRole.PRICE_RACK: [
                'rack rate', 'published rate', 'gross rate', 'rack', 'published', 'gross',
                'mrp', 'maximum retail price', 'list price'
            ],
            ColumnRole.MEAL_PLAN: [
                'meal plan', 'plan', 'cp', 'map', 'ap', 'ep', 'cpai', 'mapai', 'apai', 'epai',
                'continental', 'american', 'modified', 'european', 'breakfast', 'half board', 'full board'
            ],
            ColumnRole.DATE_START: [
                'start date', 'from date', 'from', 'start', 'begin', 'beginning', 'commence'
            ],
            ColumnRole.DATE_END: [
                'end date', 'to date', 'to', 'end', 'till', 'until', 'upto'
            ],
            ColumnRole.DATE_RANGE_TEXT: [
                'validity', 'period', 'season', 'date', 'dates', 'applicable', 'applicable from',
                'valid from', 'valid till', 'valid until', 'valid between'
            ],
            ColumnRole.DESCRIPTION: [
                'description', 'details', 'remarks', 'note', 'notes', 'comment', 'comments'
            ],
            ColumnRole.OCCUPANCY: [
                'occupancy', 'pax', 'person', 'persons', 'people', 'guests', 'adults', 'children'
            ],
            ColumnRole.NUMBER_OF_ROOMS: [
                'no of rooms', 'number of rooms', 'room count', 'rooms count', 'no. of rooms',
                'inventory', 'available rooms', 'total rooms'
            ],
            ColumnRole.EXTRA_ADULT: [
                'extra adult', 'additional adult', 'extra person', 'additional person'
            ],
            ColumnRole.EXTRA_CHILD: [
                'extra child', 'additional child', 'child', 'children'
            ]
        }
        
        # Regex patterns for content type detection
        self.patterns = {
            'price': r'(?:₹|rs\.?|inr|\$)?\s*\d[\d,]*\.?\d*\s*(?:\+?(?:tax|gst))?',
            'date': r'\d{1,2}[./-]\d{1,2}[./-]\d{2,4}|\d{1,2}\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+\d{2,4}',
            'room_count': r'^\s*\d{1,2}\s*$',
            'percentage': r'\d{1,2}%'
        }
        
    def analyze_table(self, table: List[List[Any]]) -> Dict[str, Any]:
        """
        Analyze a table and identify column roles
        
        Args:
            table: A table as a list of rows, each row being a list of cells
            
        Returns:
            Dictionary with analysis results including column roles, confidence scores,
            and table structure information
        """
        if not table or len(table) < 2:
            return {
                'column_roles': {},
                'confidence': {},
                'is_multi_header': False,
                'header_rows': [],
                'data_rows': []
            }
            
        # Identify header rows
        header_rows = self._identify_header_rows(table)
        
        # Analyze column roles
        column_roles, confidence = self._analyze_column_roles(table, header_rows)
        
        # Determine if this is a multi-header table
        is_multi_header = len(header_rows) > 1
        
        # Identify data rows (non-header rows)
        data_rows = [i for i in range(len(table)) if i not in header_rows]
        
        return {
            'column_roles': column_roles,
            'confidence': confidence,
            'is_multi_header': is_multi_header,
            'header_rows': header_rows,
            'data_rows': data_rows
        }
        
    def _identify_header_rows(self, table: List[List[Any]]) -> List[int]:
        """
        Identify which rows are likely to be headers
        
        Args:
            table: A table as a list of rows
            
        Returns:
            List of row indices that are likely headers
        """
        header_rows = [0]  # First row is almost always a header
        
        # Check for multi-row headers (usually within first 3-4 rows)
        for i in range(1, min(4, len(table))):
            row = table[i]
            
            # Skip empty rows
            if not any(cell for cell in row if cell is not None):
                continue
                
            # Check if this row looks like a header
            header_score = 0
            
            # Headers often have fewer numeric values
            numeric_count = sum(1 for cell in row if cell is not None and 
                               re.search(r'^\s*\d+\s*$', str(cell)))
            if numeric_count < len(row) * 0.3:
                header_score += 1
                
            # Headers often contain keywords
            keyword_count = 0
            all_keywords = set()
            for role, keywords in self.role_keywords.items():
                all_keywords.update(keywords)
                
            for cell in row:
                if cell is not None:
                    cell_text = str(cell).lower()
                    if any(keyword in cell_text for keyword in all_keywords):
                        keyword_count += 1
                        
            if keyword_count > 0:
                header_score += 1
                
            # If row looks like a header, add it
            if header_score >= 1:
                header_rows.append(i)
            else:
                # Stop checking once we find a non-header row
                break
                
        return header_rows
        
    def _analyze_column_roles(self, table: List[List[Any]], header_rows: List[int]) -> Tuple[Dict[int, ColumnRole], Dict[int, float]]:
        """
        Analyze and assign roles to columns
        
        Args:
            table: A table as a list of rows
            header_rows: List of row indices that are headers
            
        Returns:
            Tuple of (column_roles, confidence_scores)
        """
        column_roles = {}
        confidence = {}
        
        # Get combined header text for each column
        header_texts = self._get_combined_header_texts(table, header_rows)
        
        # Analyze content patterns in each column
        content_patterns = self._analyze_column_content(table, header_rows)
        
        # Score each column for each possible role
        for col_idx, header_text in header_texts.items():
            role_scores = self._score_column_roles(col_idx, header_text, content_patterns[col_idx])
            
            # Assign the role with the highest score
            if role_scores:
                best_role, best_score = max(role_scores.items(), key=lambda x: x[1])
                
                # Only assign if confidence is high enough
                if best_score >= self.min_confidence:
                    column_roles[col_idx] = best_role
                    confidence[col_idx] = best_score
                else:
                    column_roles[col_idx] = ColumnRole.UNKNOWN
                    confidence[col_idx] = best_score
            else:
                column_roles[col_idx] = ColumnRole.UNKNOWN
                confidence[col_idx] = 0.0
                
        return column_roles, confidence
        
    def _get_combined_header_texts(self, table: List[List[Any]], header_rows: List[int]) -> Dict[int, str]:
        """
        Get combined header text for each column
        
        Args:
            table: A table as a list of rows
            header_rows: List of row indices that are headers
            
        Returns:
            Dictionary mapping column index to combined header text
        """
        header_texts = {}
        
        # Get number of columns
        max_cols = max(len(table[row_idx]) for row_idx in header_rows if row_idx < len(table))
        
        for col_idx in range(max_cols):
            # Combine text from all header rows for this column
            texts = []
            for row_idx in header_rows:
                if row_idx < len(table) and col_idx < len(table[row_idx]) and table[row_idx][col_idx] is not None:
                    texts.append(str(table[row_idx][col_idx]).strip())
                    
            header_texts[col_idx] = ' '.join(texts).lower()
            
        return header_texts
        
    def _analyze_column_content(self, table: List[List[Any]], header_rows: List[int]) -> Dict[int, Dict[str, float]]:
        """
        Analyze content patterns in each column
        
        Args:
            table: A table as a list of rows
            header_rows: List of row indices that are headers
            
        Returns:
            Dictionary mapping column index to content pattern scores
        """
        content_patterns = {}
        
        # Get data rows (non-header rows)
        data_rows = [i for i in range(len(table)) if i not in header_rows]
        
        # Get number of columns
        max_cols = max(len(row) for row in table if row)
        
        for col_idx in range(max_cols):
            pattern_counts = defaultdict(int)
            total_cells = 0
            
            # Analyze cells in this column
            for row_idx in data_rows:
                if row_idx < len(table) and col_idx < len(table[row_idx]) and table[row_idx][col_idx] is not None:
                    cell_text = str(table[row_idx][col_idx]).strip()
                    if cell_text:
                        total_cells += 1
                        
                        # Check for different patterns
                        if re.search(self.patterns['price'], cell_text, re.I):
                            pattern_counts['price'] += 1
                            
                        if re.search(self.patterns['date'], cell_text, re.I):
                            pattern_counts['date'] += 1
                            
                        if re.search(self.patterns['room_count'], cell_text):
                            pattern_counts['room_count'] += 1
                            
                        if re.search(self.patterns['percentage'], cell_text):
                            pattern_counts['percentage'] += 1
                            
                        # Check for text patterns
                        if len(cell_text.split()) > 3:
                            pattern_counts['long_text'] += 1
                            
            # Calculate pattern scores
            pattern_scores = {}
            if total_cells > 0:
                for pattern, count in pattern_counts.items():
                    pattern_scores[pattern] = count / total_cells
                    
            content_patterns[col_idx] = pattern_scores
            
        return content_patterns
        
    def _score_column_roles(self, col_idx: int, header_text: str, content_patterns: Dict[str, float]) -> Dict[ColumnRole, float]:
        """
        Score a column for each possible role
        
        Args:
            col_idx: Column index
            header_text: Combined header text for the column
            content_patterns: Content pattern scores for the column
            
        Returns:
            Dictionary mapping roles to confidence scores
        """
        role_scores = {}
        
        # Score based on header text
        for role, keywords in self.role_keywords.items():
            score = 0.0
            
            # Check for exact matches
            for keyword in keywords:
                if keyword in header_text:
                    # Exact match in header is a strong signal
                    score += 0.7
                    break
                    
            # Check for partial matches
            if score == 0:
                for keyword in keywords:
                    if any(part in header_text for part in keyword.split()):
                        score += 0.3
                        break
                        
            # Adjust score based on content patterns
            if role == ColumnRole.PRICE_NET or role == ColumnRole.PRICE_RACK:
                # Price columns should contain price patterns
                score += content_patterns.get('price', 0) * 0.5
                
                # Differentiate between net and rack rates
                if role == ColumnRole.PRICE_RACK and 'rack' in header_text:
                    score += 0.3
                elif role == ColumnRole.PRICE_NET and ('rack' not in header_text and any(k in header_text for k in ['net', 'special', 'agent', 'contract'])):
                    score += 0.3
                    
            elif role == ColumnRole.DATE_START or role == ColumnRole.DATE_END or role == ColumnRole.DATE_RANGE_TEXT:
                # Date columns should contain date patterns
                score += content_patterns.get('date', 0) * 0.5
                
            elif role == ColumnRole.NUMBER_OF_ROOMS:
                # Room count columns should contain simple numbers
                score += content_patterns.get('room_count', 0) * 0.5
                
            elif role == ColumnRole.ROOM_TYPE:
                # Room type columns often have longer text
                score += content_patterns.get('long_text', 0) * 0.3
                
            # Only include roles with non-zero scores
            if score > 0:
                role_scores[role] = min(score, 1.0)  # Cap at 1.0
                
        return role_scores

/**
 * Utility to download a file from a URL to a local path
 * Used for downloading PDFs from Linode Object Storage for processing
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { promisify } = require('util');
const mkdirAsync = promisify(fs.mkdir);

/**
 * Downloads a file from a URL to a local path
 * 
 * @param {string} url - The URL of the file to download
 * @param {string} localPath - The local path to save the file to
 * @returns {Promise<string>} - The local path where the file was saved
 */
async function downloadFile(url, localPath) {
  try {
    // Create directory if it doesn't exist
    const dir = path.dirname(localPath);
    await mkdirAsync(dir, { recursive: true }).catch(() => {});

    // Download the file
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'stream',
    });

    // Save the file
    const writer = fs.createWriteStream(localPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => resolve(localPath));
      writer.on('error', reject);
    });
  } catch (error) {
    console.error('Error downloading file:', error);
    throw new Error(`Failed to download file: ${error.message}`);
  }
}

module.exports = downloadFile;

#!/usr/bin/env python3
"""
Test script for enhanced PDF extraction components

This script tests the new components added to the PDF extraction system:
- DynamicTableAnalyzer
- TextBlockAnalyzer
- PDFFormatClassifier
"""

import os
import sys
import argparse
import logging
import json
from datetime import datetime
import pdfplumber

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import extraction components
from advanced_pdf_extractor import TariffExtractor
from utils.dynamic_table_analyzer import DynamicTableAnalyzer, ColumnRole
from utils.text_block_analyzer import identify_text_blocks
from utils.pdf_format_classifier import PDFFormatClassifier
from utils.extraction_logger import ExtractionLogger

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_enhanced_extraction')

def test_dynamic_table_analyzer(pdf_path: str) -> None:
    """Test the DynamicTableAnalyzer component"""
    logger.info(f"Testing DynamicTableAnalyzer with {pdf_path}")
    
    # Create extractor
    extractor = TariffExtractor(pdf_path)
    
    # Extract text and tables
    text, tables = extractor.extract_text_and_tables()
    
    # Create analyzer
    analyzer = DynamicTableAnalyzer()
    
    # Analyze each table
    for i, table in enumerate(tables):
        if not table:
            continue
            
        logger.info(f"Analyzing table {i} with {len(table)} rows")
        
        # Analyze table
        analysis = analyzer.analyze_table(table)
        
        # Log results
        logger.info(f"Table {i} analysis:")
        logger.info(f"  Is multi-header: {analysis.get('is_multi_header', False)}")
        logger.info(f"  Header rows: {analysis.get('header_rows', [])}")
        logger.info(f"  Data rows: {analysis.get('data_rows', [])}")
        
        # Log column roles
        column_roles = analysis.get('column_roles', {})
        for col, role in column_roles.items():
            confidence = analysis.get('confidence', {}).get(col, 0.0)
            logger.info(f"  Column {col}: {role.name} (confidence: {confidence:.2f})")
            
def test_text_block_analyzer(pdf_path: str) -> None:
    """Test the TextBlockAnalyzer component"""
    logger.info(f"Testing TextBlockAnalyzer with {pdf_path}")
    
    # Create extractor
    extractor = TariffExtractor(pdf_path)
    
    # Extract text and tables
    text, _ = extractor.extract_text_and_tables()
    
    # Extract page texts
    page_texts = []
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            page_text = page.extract_text() or ""
            page_texts.append(page_text)
    
    # Identify text blocks
    text_blocks = identify_text_blocks(text, page_texts)
    
    # Log results
    logger.info(f"Identified text blocks:")
    for block_type, block_info in text_blocks.items():
        blocks = block_info.get('blocks', [])
        logger.info(f"  {block_type}: {len(blocks)} blocks")
        
        # Log first block of each type
        if blocks:
            logger.info(f"    First block: {blocks[0]}")
            
def test_pdf_format_classifier(pdf_path: str) -> None:
    """Test the PDFFormatClassifier component"""
    logger.info(f"Testing PDFFormatClassifier with {pdf_path}")
    
    # Create extractor
    extractor = TariffExtractor(pdf_path)
    
    # Extract text and tables
    text, tables = extractor.extract_text_and_tables()
    
    # Create analyzer and classifier
    analyzer = DynamicTableAnalyzer()
    classifier = PDFFormatClassifier()
    
    # Analyze tables
    table_analysis = {}
    for i, table in enumerate(tables):
        if table:
            table_analysis[i] = analyzer.analyze_table(table)
    
    # Extract page texts
    page_texts = []
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            page_text = page.extract_text() or ""
            page_texts.append(page_text)
    
    # Identify text blocks
    text_blocks = identify_text_blocks(text, page_texts)
    
    # Classify PDF format
    format_analysis = classifier.classify_pdf(
        text=text,
        tables=tables,
        table_analysis=table_analysis,
        text_blocks=text_blocks
    )
    
    # Log results
    logger.info(f"PDF format analysis:")
    logger.info(f"  Primary format: {format_analysis['primary_format']}")
    logger.info(f"  Confidence: {format_analysis['confidence']:.2f}")
    logger.info(f"  Secondary formats: {format_analysis['secondary_formats']}")
    
    # Log scores for all formats
    logger.info(f"  Format scores:")
    for fmt, score in format_analysis['all_scores'].items():
        logger.info(f"    {fmt}: {score:.2f}")
        
def test_full_extraction(pdf_path: str) -> None:
    """Test the full extraction process with enhanced components"""
    logger.info(f"Testing full extraction with {pdf_path}")
    
    # Create extractor
    extractor = TariffExtractor(pdf_path)
    
    # Extract data
    results = extractor.extract()
    
    # Log results
    logger.info(f"Extracted {len(results)} entries")
    if results:
        logger.info(f"First entry: {results[0]}")
        
    # Save results to JSON
    output_path = f"{os.path.splitext(pdf_path)[0]}_enhanced.json"
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)
        
    logger.info(f"Saved results to {output_path}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Test enhanced PDF extraction components')
    parser.add_argument('pdf_path', help='Path to PDF file')
    parser.add_argument('--component', choices=['table', 'text', 'format', 'full'], 
                        default='full', help='Component to test')
    args = parser.parse_args()
    
    if not os.path.exists(args.pdf_path):
        logger.error(f"PDF file not found: {args.pdf_path}")
        return
        
    if args.component == 'table':
        test_dynamic_table_analyzer(args.pdf_path)
    elif args.component == 'text':
        test_text_block_analyzer(args.pdf_path)
    elif args.component == 'format':
        test_pdf_format_classifier(args.pdf_path)
    else:
        test_full_extraction(args.pdf_path)

if __name__ == '__main__':
    main()

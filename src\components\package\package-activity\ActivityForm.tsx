/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";

import DialogTitle from "@mui/material/DialogTitle";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import PackageContext from "@/utils/context/PackageContext";
import { ActivityEvent } from "./ActivityDay";
type ActivityFormProps = {
  day: number;
  handleEvents: (event: ActivityEvent) => void;
  sort :number;
};
export default function ActivityForm(props: ActivityFormProps) {
  const { allActivity,destinationIds }: any = React.useContext(PackageContext);
  const [open, setOpen] = React.useState(false);
  const [activity, setActivity] = React.useState<string>("");
  const [time, setTime] = React.useState("");
  const [type, setType] = React.useState("");
  const handleClickOpen = () => {
    setOpen(true);
  };
  const [destActivities, setDestActivities] = React.useState<any[]>([]);
  const allType = ["free", "allocated"];
  const timePeriod = ["morning", "noon", "evening", "noon-evening", "full-day"];
  const handleChangeActivity = async (event: SelectChangeEvent) => {
    setActivity(event.target.value);
  };
  const handleChangeTime = (event: SelectChangeEvent) => {
    setTime(event.target.value as string);
  };
  const handleChangeType = (event: SelectChangeEvent) => {
    setType(event.target.value as string);
  };

  const handleClose = () => {
    setOpen(false);
  };
  function handleActivities(ids:any){
    if(destinationIds?.length > 0){
      const aa:any = allActivity.filter((k: any) => ids.some((l:any)=> l.destinationId === k.destinationId))
      setDestActivities(aa)
    }
  }
  React.useEffect(()=>{
    handleActivities(destinationIds)
  },[destinationIds])
  return (
    <React.Fragment>
      <Button variant="outlined" color="primary" onClick={handleClickOpen}>
        Add Activity
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        PaperProps={{
          component: "form",
          onSubmit: (event: React.FormEvent<HTMLFormElement>) => {
            event.preventDefault();
            const formData = new FormData(event.currentTarget);
            const formJson = Object.fromEntries((formData as any).entries());
            if ((type === 'free' || activity) && type && time && formJson.slot) {
              const name = activity.split("::")
              const eve = {
                slot: Number(formJson.slot),
                activityType: type as "free" | "allocated",
                activityId: type === 'allocated' ? name[0] : null,
                name:  type === 'allocated' ? name[1] : "",
                price:   Number(name[2]) || 0,
                timePeriod: time as
                  | "morning"
                  | "noon"
                  | "evening"
                  | "noon-evening"
                  | "full-day",
              }
              props.handleEvents(eve);
              handleClose();
            }
            
          },
        }}
      >
        <DialogTitle>Activity Day:{props.day}</DialogTitle>
        <div className="flex flex-col w-[300px] gap-2 m-2">
          <TextField
            autoFocus
            required
            margin="dense"
            id="name"
            name="slot"
            label="Slot"
            type="number"
            variant="standard"
            defaultValue={props.sort}
          />
          <FormControl fullWidth>
            <InputLabel id="demo-simple-select-label">Activity Type</InputLabel>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={type}
              label="Activity Type"
              onChange={handleChangeType}
            >
              {allType?.map((item: any) => {
                return (
                  <MenuItem key={item} value={item}>
                    {item}
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>
          {type === "allocated" && (
            <FormControl fullWidth>
              <InputLabel id="demo-simple-select-label">Activity Id</InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={activity}
                label="Activity Id"
                onChange={handleChangeActivity}
              >
                {
                destActivities?.length >0 ?
                destActivities?.map((item: any) => {
                  return (
                    <MenuItem key={item?.activityId} value={item?.activityId+'::'+item?.name+'::'+item?.price}>
                      {item?.name}
                    </MenuItem>
                  );
                }) : 
                <MenuItem color="red">No Activity Available</MenuItem>
        
                }
              </Select>
            </FormControl>
          )}

          <FormControl fullWidth>
            <InputLabel id="demo-simple-select-label">Time Period</InputLabel>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={time}
              label="Time Period"
              onChange={handleChangeTime}
            >
              {timePeriod?.map((item: any) => {
                return (
                  <MenuItem key={item} value={item}>
                    {item}
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>
        </div>

        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="submit">Save Event</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}

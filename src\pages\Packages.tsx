/* eslint-disable @typescript-eslint/no-explicit-any */
import Pagination from "@/components/layout-components/Pagination";
import Loading from "@/components/package/Loading";
import PackageCols from "@/components/package/all-package/PackageCols";
import PackageRow from "@/components/package/all-package/PackageRow";
import { getPackages } from "@/utils/api-functions/getPackages";
import { ChangeEvent, useEffect, useState } from "react";
import { IoIosHome } from "react-icons/io";
import { Link } from "react-router-dom";

const Packages = () => {
  const [pageNo, setPageNo] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [coupons, setCoupons] = useState<any[]>([]);
  const [inputText,setInputText] = useState("")
  const [showText,setShowText] = useState("");
  async function fetchPackages(offset: number,search?:string) {
    const response = await getPackages(offset,search);
    console.log(response);
      setCoupons(response?.docs);
      setTotalPages(response?.totalPages);
  }

  // async function handleSearch(){
  //   if(inputText){
     
  //   }
  // }
  useEffect(() => {
    fetchPackages(0);
  }, []);
  useEffect(() => {
    fetchPackages(pageNo * 10, inputText);
  }, [pageNo]);
  console.log(coupons);

  useEffect(()=>{
    fetchPackages(0,inputText)
    setShowText(inputText)
  },[inputText])

  return (
    <>
      <header className="flex items-center justify-between">
        <div className="flex gap-2">
          <a href="/packages">
          <IoIosHome className="w-[35px] h-[35px] ml-2"/>
          </a>
          <input
            type="text"
            className="border w-[300px] ml-2 text-xl px-2 py-1"
            placeholder="Search Package..."
            value={inputText}
            onInput={(e:ChangeEvent<HTMLInputElement>)=>setInputText(e.target.value)}
          />
          <button className="bg-blue-500 px-2 " >Search</button>
        </div>
        <Link to={"/packages/add"}>
          <button className="w-[200px] py-4 bg-black text-white m-2 rounded-lg">
            Create Package
          </button>
        </Link>

      </header>
      {
        showText? <div className="px-2">Search Results for "{showText}"</div>:<></>
      }
        <div className=" mt-6 border m-1 flex flex-col gap-5 min-h-[70vh] justify-between">
          <div className="">
            <PackageCols />
            {coupons?.length>0?
            coupons?.map((k) => (
              <PackageRow key={k._id} packageData={k} />
            )):<Loading/>}
          </div>
         <Pagination changePage={setPageNo} pageNo={pageNo} totalPages={totalPages} setPageNo={setPageNo}/>
        </div>
    </>
  );
};

export default Packages;
/* eslint-disable @typescript-eslint/no-explicit-any */
import Loading from "@/components/package/Loading";
import { DataTable } from "@/components/package/all-package/data-table";
import { columns } from "@/components/package/all-package/columns";
import { getPackages } from "@/utils/api-functions/getPackages";
import { useCallback, useEffect, useState } from "react";

const Packages = () => {
  const [pageNo, setPageNo] = useState(0);
  const [packages, setPackages] = useState<any[]>([]);
  const [packageData, setPackageData] = useState<any>({
    docs: [],
    totalDocs: 0,
    totalPages: 0
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  // Memoized function to update package data
  const updatePackageData = useCallback((data: any) => {
    // Only update if data is valid to prevent unnecessary re-renders
    if (data && typeof data === 'object') {
      setPackageData(data);
      setPackages(data?.docs || []);
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    let isMounted = true;

    const fetchInitialData = async () => {
      setIsLoading(true);
      try {
        const response = await getPackages(0, "", 10);

        // Only update state if component is still mounted
        if (isMounted) {
          updatePackageData(response);
        }
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchInitialData();

    // Cleanup function to prevent memory leaks
    return () => {
      isMounted = false;
    };
  }, [updatePackageData]);

  // If initial loading is happening, show the loading spinner
  if (isLoading && packages.length === 0) {
    return <Loading />;
  }

  return (
    <div className="min-h-[91vh] flex flex-col justify-between bg-gradient-to-br from-blue-50 to-white">
      <div className="px-5 pb-24 max-w-7xl mx-auto w-full">
        <div className="flex items-center justify-between py-8">
          <h1 className="text-3xl font-bold text-blue-900 tracking-tight">Packages</h1>
        </div>
        <div className="bg-white rounded-2xl shadow-lg border border-blue-100 p-6">
          <DataTable
            columns={columns}
            data={packages}
            totalDocs={packageData?.totalDocs || 0}
            totalPages={packageData?.totalPages || 0}
            setPackages={updatePackageData}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            pageNo={pageNo}
            setPageNo={setPageNo}
          />
        </div>
      </div>
    </div>
  );
};

export default Packages;
import { Link } from 'react-router-dom'
import { RoomData } from '../AddRoom'
import { FaRegUser } from "react-icons/fa";

export default function RoomRow({ data }:{data:RoomData}) {
  return (
    <tr className="hover:bg-gray-50 transition-colors">
      <td className="px-6 py-4 whitespace-nowrap">
        <Link 
          to={`/hotels/${data.hotelId}/room/${data.hotelRoomId}`}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          {data.hotelRoomType}
        </Link>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
        {data.hotelName}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${data.isAc ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
          {data.isAc ? "AC" : "Non-AC"}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
        <div className="flex items-center">
          <span className="mr-2">{data.roomCapacity}</span>
          <FaRegUser className="text-gray-500" />
        </div>
      </td>
    </tr>
  )
}

/* eslint-disable @typescript-eslint/no-explicit-any */
export default function CouponsRow(props:any) {
    // coupons Name
    // is public 
    // userid
    // value type
    // value
    // valid date
    return (
      <div className="flex border">
        <div className="m-1 flex justify-between w-full font-bold ">
            <div className="w-full text-center">{props?.data?.couponName}</div>
            <div className="w-full text-center">{props?.data?.isPublic ? "True" : "False"}</div>
            <div className="w-full text-center">{props?.data?.userId}</div>
            <div className="w-full text-center">{props?.data?.valueType}</div>
            <div className="w-full text-center">{props?.data?.value}</div>
            <div className="w-full text-center">{props?.data?.validDate}</div>

        </div>
      </div>
    );
  }
  
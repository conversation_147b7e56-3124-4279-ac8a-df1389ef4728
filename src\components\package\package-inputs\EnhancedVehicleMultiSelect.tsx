/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from "react";
import { input_field_css } from "../PackageForm";

export interface EnhancedVehicleMultiSelectProps {
  initialData: any[];
  allData: any[];
  dataName: string;
  dataId: string;
  pHolder: string;
  setDatas: React.Dispatch<React.SetStateAction<any[]>>;
}

export default function EnhancedVehicleMultiSelect(props: EnhancedVehicleMultiSelectProps) {
  const [selectedData, setSelectedData] = useState<any[]>([]);
  const [restData, setRestData] = useState<any[]>(props.allData);
  const [view, setView] = useState(false);
  const [textInput, setTextInput] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Group vehicles by type
  const groupedVehicles = () => {
    const grouped: Record<string, any[]> = {};
    restData.forEach(vehicle => {
      const type = vehicle.vehicleType || "Other";
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(vehicle);
    });
    return grouped;
  };

  function handleInput(inp: any) {
    props.setDatas((prev) => [...prev, inp]);
    setSelectedData((prev) => [...prev, inp]);
    setTextInput("");
  }

  function handleClose(inp: string) {
    const data = selectedData.filter((k) => {
      return k[props.dataId] !== inp;
    });
    props.setDatas(data);
    setSelectedData(data);
  }

  function handleInputText(inp: string) {
    setTextInput(inp);
    const data = props.allData.filter((k) => {
      return (
        k[props.dataName].toLowerCase()?.includes(inp.toLowerCase()) &&
        !selectedData.some((j) => j[props.dataId] === k[props.dataId])
      );
    });
    setRestData(data);
  }

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setView(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setRestData(props.allData);
  }, [props.allData]);

  useEffect(() => {
    const data = props.allData.filter((k) => {
      return !selectedData.some((j) => j[props.dataId] === k[props.dataId]);
    });
    setRestData(data);
  }, [selectedData, props.allData]);

  useEffect(() => {
    setSelectedData(props.initialData);
  }, [props.initialData]);

  // Add all vehicles of a specific type
  function addAllOfType(type: string) {
    const vehiclesToAdd = groupedVehicles()[type] || [];
    if (vehiclesToAdd.length > 0) {
      const newSelectedData = [...selectedData, ...vehiclesToAdd];
      props.setDatas(newSelectedData);
      setSelectedData(newSelectedData);
    }
  }

  return (
    <div className="w-full border relative py-2 text-xl px-2 flex flex-wrap bg-white rounded-md">
      {selectedData?.length > 0 ? (
        selectedData?.map((k) => (
          <div
            key={k[props.dataId]}
            className="text-sm text-white bg-blue-500 m-1 p-1 rounded-lg flex items-center"
          >
            <div className="flex flex-col">
              <div className="flex items-center">
                <span className="font-medium">{k[props.dataName]}</span>
                {k.price && <span className="ml-1 text-blue-100">₹{k.price}</span>}
              </div>
              {(k.seatingCapacity || k.acType) && (
                <div className="text-xs text-blue-100">
                  {k.seatingCapacity && `${k.seatingCapacity} seats`}
                  {k.seatingCapacity && k.acType && " • "}
                  {k.acType && k.acType}
                </div>
              )}
            </div>
            <div
              onClick={() => handleClose(k[props.dataId])}
              className="bg-blue-600 flex justify-center items-center cursor-pointer w-[20px] h-[20px] ml-4 rounded-full font-bold"
            >
              x
            </div>
          </div>
        ))
      ) : (
        ""
      )}
      <div className="relative" ref={dropdownRef}>
        <input
          ref={inputRef}
          placeholder={props.pHolder}
          onFocus={() => setView(true)}
          type="text"
          value={textInput}
          className={input_field_css}
          onInput={(e: any) => handleInputText(e.target.value)}
        />
        {view && (
          <div className="fixed inset-0 z-50 flex items-start justify-center pt-20 bg-black bg-opacity-30" onClick={() => setView(false)}>
            <div
              className="w-full max-w-2xl bg-white border border-gray-200 rounded-md shadow-xl max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center p-3 border-b border-gray-200 bg-gray-50">
                <h3 className="text-lg font-medium text-gray-900">Select Vehicles</h3>
                <button
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => setView(false)}
                >
                  ×
                </button>
              </div>

              {/* Search within dropdown */}
              <div className="p-3 border-b border-gray-200">
                <div className="flex items-center border border-gray-300 rounded-md bg-white overflow-hidden focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                  <input
                    type="text"
                    placeholder="Search vehicles..."
                    className="w-full py-2 px-3 border-none focus:outline-none text-sm"
                    value={textInput}
                    onChange={(e) => handleInputText(e.target.value)}
                    autoFocus
                  />
                  {textInput && (
                    <button
                      type="button"
                      className="p-2 text-gray-400 hover:text-gray-600"
                      onClick={() => setTextInput("")}
                    >
                      ×
                    </button>
                  )}
                </div>
              </div>

              {/* Vehicle List */}
              <div className="overflow-y-auto" style={{ maxHeight: 'calc(80vh - 120px)' }}>
                <div className="p-3">
                  {restData.length === 0 ? (
                    <div className="p-3 text-sm text-gray-500 text-center">
                      No vehicles found matching your search
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {Object.entries(groupedVehicles()).map(([type, vehicles]) => (
                        <div key={type} className="mb-4">
                          <div className="flex justify-between items-center mb-2 bg-gray-50 p-2 rounded-md">
                            <h4 className="text-sm font-medium text-gray-700">{type} ({vehicles.length})</h4>
                            <button
                              className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md"
                              onClick={() => addAllOfType(type)}
                            >
                              Add All {type}s
                            </button>
                          </div>

                          <div className="space-y-1">
                            {vehicles.map((vehicle) => (
                              <div
                                key={vehicle[props.dataId]}
                                onMouseDown={() => handleInput(vehicle)}
                                className="flex items-center justify-between p-2 hover:bg-blue-50 rounded-md cursor-pointer border border-gray-100"
                              >
                                <div className="flex items-center">
                                  <div className="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center mr-2 text-xs font-medium">
                                    {vehicle.vehicleType?.charAt(0) || 'V'}
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">{vehicle[props.dataName]}</div>
                                    <div className="text-xs text-gray-500">
                                      {vehicle.seatingCapacity && `${vehicle.seatingCapacity} seats`}
                                      {vehicle.price && ` • ₹${vehicle.price}`}
                                      {vehicle.acType && ` • ${vehicle.acType}`}
                                      {vehicle.destination && ` • ${vehicle.destination}`}
                                    </div>
                                  </div>
                                </div>
                                <button
                                  className="bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium py-1 px-2 rounded"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleInput(vehicle);
                                  }}
                                >
                                  Add
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Footer with actions */}
              <div className="p-3 border-t border-gray-200 bg-gray-50 flex justify-end">
                <button
                  className="bg-gray-200 hover:bg-gray-300 text-gray-800 mr-2 py-2 px-4 rounded-md text-sm font-medium"
                  onClick={() => setView(false)}
                >
                  Cancel
                </button>
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium"
                  onClick={() => setView(false)}
                >
                  Done
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

import {  createSlice,PayloadAction } from "@reduxjs/toolkit"
interface HotelEditState {
    cuurentTab : string
}


const initialState : HotelEditState ={
    cuurentTab : 'hotel'
    
}


const HotelEditSlice =createSlice({
    name : 'Hotel Edit',
    initialState,
    reducers : {
 
        setTab : (state , action:PayloadAction<string>) =>{
            state.cuurentTab = action.payload;

        },
      
       
       
    }
})

export const {setTab} = HotelEditSlice.actions;
export default HotelEditSlice.reducer;
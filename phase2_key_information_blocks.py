#!/usr/bin/env python3
"""
Phase 2: Identifying Key Information Blocks

This script implements the second phase of the PDF extraction process:
1. Locating Date Ranges (Validity Periods)
2. Identifying Rate Tables vs. Other Tables
3. Locating Extra Charges & Meal Plan Definitions

Usage:
    python phase2_key_information_blocks.py <pdf_file_path> [--output <output_file>] [--debug]
"""

import os
import sys
import json
import argparse
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add utils directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our components
from utils.pdf_loader import PDFLoader
from utils.text_cleaner import TextCleaner
from utils.date_range_extractor import DateRangeExtractor
from utils.table_classifier import TableClassifier
from utils.extra_charges_extractor import ExtraChargesExtractor
# Define a simple config loader for our script
class SimpleConfigLoader:
    """Simple configuration loader for our script"""

    def __init__(self, config_path=None):
        """Initialize with default configuration"""
        self.config = {}

    def get_full_config(self):
        """Get the full configuration"""
        return self.config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('tariff_extractor')

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Phase 2: Identifying Key Information Blocks')

    # Required arguments
    parser.add_argument('pdf_file', help='Path to the PDF file')

    # Output options
    parser.add_argument('--output', '-o', help='Output file (default: stdout)')

    # Configuration options
    parser.add_argument('--config', help='Path to configuration file')

    # Logging options
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')

    return parser.parse_args()

def main():
    """Main function"""
    args = parse_args()

    # Configure logging
    if args.debug:
        logger.setLevel(logging.DEBUG)

    # Load configuration
    config = None
    if args.config:
        config = SimpleConfigLoader(args.config)
        logger.info(f"Loaded configuration from {args.config}")
    else:
        config = SimpleConfigLoader()
        logger.info(f"Using default configuration")

    start_time = datetime.now()

    try:
        # Step 1: Load PDF and extract text and tables
        logger.info(f"Step 1: Loading PDF {args.pdf_file}")
        pdf_loader = PDFLoader(args.pdf_file, config)
        text, tables, metadata = pdf_loader.load_pdf()

        # Log basic PDF information
        logger.info(f"PDF loaded: {len(text)} chars, {len(tables)} tables, {metadata.get('page_count', 0)} pages")

        # Step 2: Clean text and tables
        logger.info(f"Step 2: Cleaning text and tables")
        text_cleaner = TextCleaner(config)
        cleaned_text = text_cleaner.clean_text(text)
        cleaned_tables = []
        for table in tables:
            if table:
                cleaned_table = text_cleaner.clean_table(table)
                cleaned_tables.append(cleaned_table)

        # Step 3: Locate Date Ranges (Validity Periods)
        logger.info(f"Step 3: Locating date ranges")
        date_extractor = DateRangeExtractor(config)

        # Extract date ranges from different sources
        date_ranges = []

        # Extract from main text
        text_date_ranges = date_extractor.extract_date_ranges(cleaned_text, source="text")
        if text_date_ranges:
            date_ranges.extend(text_date_ranges)
            logger.info(f"Found {len(text_date_ranges)} date ranges in main text")

        # Extract from tables
        for table in cleaned_tables:
            if not table:
                continue

            # Check header row for date information
            if len(table) > 0:
                header_text = ' '.join([str(h) for h in table[0]])
                header_date_ranges = date_extractor.extract_date_ranges(header_text, source="table")
                if header_date_ranges:
                    date_ranges.extend(header_date_ranges)
                    logger.info(f"Found {len(header_date_ranges)} date ranges in table header")

        # Extract from PDF metadata if available
        if 'pdf_metadata' in metadata and metadata['pdf_metadata'].get('title'):
            title_date_ranges = date_extractor.extract_date_ranges(metadata['pdf_metadata']['title'], source="header")
            if title_date_ranges:
                date_ranges.extend(title_date_ranges)
                logger.info(f"Found {len(title_date_ranges)} date ranges in PDF title")

        # Log date range extraction results
        if date_ranges:
            logger.info(f"Found {len(date_ranges)} date ranges in total")
            for i, date_range in enumerate(date_ranges):
                logger.info(f"Date range {i+1}: {date_range['start_date']} to {date_range['end_date']} (source: {date_range['source']}, global: {date_range['is_global']})")
        else:
            logger.warning(f"No date ranges found in the PDF")

        # Step 4: Identify Rate Tables vs. Other Tables
        logger.info(f"Step 4: Identifying rate tables")
        table_classifier = TableClassifier(config)

        # Classify tables
        table_classifications = []
        for i, table in enumerate(cleaned_tables):
            if not table:
                continue

            classification = table_classifier.classify_table(table)
            table_classifications.append(classification)

            logger.info(f"Table {i+1}: {classification['type']} (confidence: {classification['confidence']:.2f})")

            # Log table features
            features = classification['features']
            if features['has_room_types']:
                logger.info(f"  - Has room types (column {features['room_type_column']})")
            if features['has_prices']:
                logger.info(f"  - Has prices (columns {features['numeric_columns']})")
            if features['has_meal_plans']:
                logger.info(f"  - Has meal plans (columns {features['meal_plan_columns']})")
            if features['has_dates']:
                logger.info(f"  - Has dates (columns {features['date_columns']})")

        # Get rate tables
        rate_tables = []
        for i, (table, classification) in enumerate(zip(cleaned_tables, table_classifications)):
            if classification['is_rate_table']:
                rate_tables.append((table, classification))
                logger.info(f"Table {i+1} is a rate table with confidence {classification['confidence']:.2f}")

        # Step 5: Locate Extra Charges & Meal Plan Definitions
        logger.info(f"Step 5: Locating extra charges and meal plan definitions")
        extra_charges_extractor = ExtraChargesExtractor(config)

        # Extract extra charges from text
        extra_charges = extra_charges_extractor.extract_extra_charges(cleaned_text)

        # Log extra charges extraction results
        if extra_charges:
            logger.info(f"Found extra charges:")
            for charge_type, value in extra_charges.items():
                if charge_type != 'meal_plan_definitions':
                    logger.info(f"  - {charge_type}: {value}")

            if 'meal_plan_definitions' in extra_charges:
                logger.info(f"Found meal plan definitions:")
                for plan_type, definition in extra_charges['meal_plan_definitions'].items():
                    logger.info(f"  - {plan_type}: {definition}")
        else:
            logger.warning(f"No extra charges found in the PDF")

        # Prepare output
        result = {
            "pdf_path": args.pdf_file,
            "metadata": metadata,
            "date_ranges": date_ranges,
            "table_classifications": table_classifications,
            "rate_tables_count": len(rate_tables),
            "extra_charges": extra_charges,
            "extraction_stats": {
                "date_extraction": date_extractor.get_extraction_stats(),
                "table_classification": table_classifier.get_classification_stats(),
                "extra_charges_extraction": extra_charges_extractor.get_extraction_stats()
            },
            "processing_time_seconds": (datetime.now() - start_time).total_seconds()
        }

        # Output the results
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2)
            logger.info(f"Results saved to {args.output}")
        else:
            # Print to stdout for piping or capture
            print(json.dumps(result, indent=2))

        # Log success
        logger.info(f"Key information blocks identification completed successfully in {(datetime.now() - start_time).total_seconds():.2f} seconds")

    except Exception as e:
        # Log error
        logger.error(f"Error during key information blocks identification: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()

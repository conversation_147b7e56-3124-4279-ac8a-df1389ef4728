#!/usr/bin/env python3
"""
PDF Loader for Tariff Extraction

This module provides enhanced PDF loading and content retrieval functionality,
with improved error handling, OCR fallback, and content analysis.
"""

import os
import re
import logging
import tempfile
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime

import pdfplumber
from PIL import Image

# Configure logging
logger = logging.getLogger('tariff_extractor')

# Check if OCR libraries are available
OCR_AVAILABLE = False
try:
    import pytesseract
    from pdf2image import convert_from_path
    OCR_AVAILABLE = True
except ImportError:
    logger.warning("OCR libraries not available. Install with: pip install pytesseract pdf2image pillow")

class PDFLoader:
    """Enhanced PDF loader with improved content retrieval and OCR fallback"""
    
    def __init__(self, pdf_path: str, config: Optional[Any] = None):
        """
        Initialize the PDF loader
        
        Args:
            pdf_path: Path to the PDF file
            config: Optional configuration object
        """
        self.pdf_path = pdf_path
        self.config = config
        self.is_scanned = False
        self.ocr_used = False
        self.page_count = 0
        self.extraction_stats = {
            'text_extraction_time': 0,
            'table_extraction_time': 0,
            'ocr_time': 0,
            'total_text_length': 0,
            'total_tables': 0,
            'scanned_pages': 0,
            'ocr_processed_pages': 0
        }
        
    def load_pdf(self) -> Tuple[str, List[List[List[str]]], Dict[str, Any]]:
        """
        Load PDF and extract text and tables with enhanced error handling
        
        Returns:
            Tuple containing:
            - Full text from the PDF
            - List of tables from the PDF
            - Dictionary of extraction statistics and metadata
        """
        start_time = datetime.now()
        full_text = ""
        all_tables = []
        metadata = {}
        
        try:
            # First attempt: Use pdfplumber for text and table extraction
            with pdfplumber.open(self.pdf_path) as pdf:
                self.page_count = len(pdf.pages)
                metadata['page_count'] = self.page_count
                
                # Extract PDF metadata if available
                if hasattr(pdf, 'metadata') and pdf.metadata:
                    metadata['pdf_metadata'] = {
                        'title': pdf.metadata.get('Title', ''),
                        'author': pdf.metadata.get('Author', ''),
                        'creator': pdf.metadata.get('Creator', ''),
                        'producer': pdf.metadata.get('Producer', ''),
                        'creation_date': pdf.metadata.get('CreationDate', '')
                    }
                
                # Process each page
                for page_num, page in enumerate(pdf.pages):
                    page_start_time = datetime.now()
                    logger.debug(f"Processing page {page_num+1}/{self.page_count}")
                    
                    # Extract text
                    try:
                        page_text = page.extract_text() or ""
                        
                        # Check if page might be scanned (very little text extracted)
                        if len(page_text.strip()) < 100:
                            logger.debug(f"Page {page_num+1} has very little text ({len(page_text)} chars), might be scanned")
                            self.is_scanned = True
                            self.extraction_stats['scanned_pages'] += 1
                        
                        full_text += page_text + "\n"
                        self.extraction_stats['total_text_length'] += len(page_text)
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num+1}: {str(e)}")
                        self.is_scanned = True
                        self.extraction_stats['scanned_pages'] += 1
                    
                    # Extract tables
                    try:
                        # First try with default settings
                        tables = page.extract_tables()
                        
                        # If no tables found, try with alternative settings
                        if not tables:
                            logger.debug(f"No tables found on page {page_num+1} with default settings, trying alternatives")
                            tables = page.extract_tables(table_settings={
                                "vertical_strategy": "text", 
                                "horizontal_strategy": "text"
                            })
                            
                            # If still no tables, try with even more relaxed settings
                            if not tables:
                                tables = page.extract_tables(table_settings={
                                    "vertical_strategy": "lines", 
                                    "horizontal_strategy": "lines",
                                    "intersection_tolerance": 15
                                })
                        
                        if tables:
                            all_tables.extend(tables)
                            self.extraction_stats['total_tables'] += len(tables)
                            logger.debug(f"Found {len(tables)} tables on page {page_num+1}")
                    except Exception as e:
                        logger.warning(f"Error extracting tables from page {page_num+1}: {str(e)}")
                    
                    # Calculate page processing time
                    page_time = (datetime.now() - page_start_time).total_seconds()
                    logger.debug(f"Page {page_num+1} processed in {page_time:.2f} seconds")
            
            # If very little text was extracted or PDF is detected as scanned, try OCR
            if (self.is_scanned or self.extraction_stats['total_text_length'] < 500) and OCR_AVAILABLE:
                logger.info(f"PDF appears to be scanned or has very little text ({self.extraction_stats['total_text_length']} chars). Trying OCR.")
                ocr_text, ocr_stats = self._extract_with_ocr()
                
                # If OCR extracted more text, use it
                if len(ocr_text) > len(full_text):
                    logger.info(f"OCR extracted more text ({len(ocr_text)} chars) than pdfplumber ({len(full_text)} chars). Using OCR text.")
                    full_text = ocr_text
                    self.ocr_used = True
                    self.extraction_stats.update(ocr_stats)
                
        except Exception as e:
            logger.error(f"Error loading PDF {self.pdf_path}: {str(e)}")
            
            # Try OCR as fallback if available
            if OCR_AVAILABLE:
                logger.info(f"Attempting OCR fallback after PDF loading error")
                full_text, ocr_stats = self._extract_with_ocr()
                self.ocr_used = True
                self.extraction_stats.update(ocr_stats)
        
        # Calculate total processing time
        self.extraction_stats['total_processing_time'] = (datetime.now() - start_time).total_seconds()
        
        # Add extraction stats to metadata
        metadata['extraction_stats'] = self.extraction_stats
        metadata['is_scanned'] = self.is_scanned
        metadata['ocr_used'] = self.ocr_used
        
        return full_text, all_tables, metadata
    
    def _extract_with_ocr(self) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text using OCR for scanned PDFs
        
        Returns:
            Tuple containing:
            - Extracted text
            - Dictionary of OCR statistics
        """
        if not OCR_AVAILABLE:
            return "", {"ocr_time": 0, "ocr_processed_pages": 0}
        
        ocr_start_time = datetime.now()
        full_text = ""
        ocr_stats = {
            "ocr_time": 0,
            "ocr_processed_pages": 0,
            "ocr_text_length": 0
        }
        
        try:
            # Convert PDF to images
            logger.info(f"Converting PDF to images for OCR")
            images = convert_from_path(self.pdf_path)
            
            # Process each page
            for i, image in enumerate(images):
                logger.debug(f"Processing page {i+1}/{len(images)} with OCR")
                
                # Preprocess image for better OCR results
                preprocessed_image = self._preprocess_image(image)
                
                # Perform OCR on the image
                page_text = pytesseract.image_to_string(preprocessed_image)
                full_text += page_text + "\n"
                
                ocr_stats["ocr_processed_pages"] += 1
                ocr_stats["ocr_text_length"] += len(page_text)
                
                logger.debug(f"OCR extracted {len(page_text)} chars from page {i+1}")
        except Exception as e:
            logger.error(f"Error during OCR extraction: {str(e)}")
        
        ocr_stats["ocr_time"] = (datetime.now() - ocr_start_time).total_seconds()
        logger.info(f"OCR extraction completed in {ocr_stats['ocr_time']:.2f} seconds")
        
        return full_text, ocr_stats
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Preprocess image for better OCR results
        
        Args:
            image: PIL Image object
            
        Returns:
            Preprocessed PIL Image object
        """
        try:
            # Convert to grayscale
            gray_image = image.convert('L')
            
            # Increase contrast
            # This is a simple contrast enhancement - more sophisticated methods could be used
            min_value = 50  # Adjust these values based on your PDFs
            max_value = 200
            enhanced_image = Image.eval(gray_image, lambda x: min(max(min_value, x), max_value) * 255 / (max_value - min_value))
            
            return enhanced_image
        except Exception as e:
            logger.warning(f"Error preprocessing image for OCR: {str(e)}")
            return image

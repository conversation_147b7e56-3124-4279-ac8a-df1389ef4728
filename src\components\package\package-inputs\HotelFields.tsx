/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeEvent, useContext, useEffect, useState } from "react";
import PackageContext from "@/utils/context/PackageContext";
import PackageSearchSelect from "./PackageSearchSelect";
import { label_css } from "../PackageForm";
import toast from "react-hot-toast";
import HotelFieldsMp from "./HotelFieldsMp";
import { getPackageHotelData, getMealPlansForRoom } from "@/utils/api-functions/getPackageHotelData";
import NumberDropdown from "./NumberDropdown";

export interface HotelDataType {
  hotelId: string;
  hotelName: string;
  hotelRoomId: string;
  hotelRoomType: string;
  isAddOn: boolean;
  mealPlan: string;
  noOfNight: number;
  sort: number;
  startDateWise:number;
  endDateWise:number;
}
interface MealPlanType {
 mealId:string, mealPlan:string,price:number,startDate:string[],endDate:string[]
}
// Helper function to display meal plan names with descriptions
function getMealPlanDisplay(mealPlan: string): string {
  // Ensure we have a valid string to work with
  if (!mealPlan) return '';

  // Normalize the input to lowercase for consistent lookup
  const normalizedMealPlan = mealPlan.toLowerCase();

  const mealPlanMap: Record<string, string> = {
    // Define only lowercase keys for consistent lookup
    'cp': 'CP (Continental Plan - Breakfast Only)',
    'map': 'MAP (Modified American Plan - Breakfast & Dinner)',
    'ap': 'AP (American Plan - All Meals)',
    'ep': 'EP (European Plan - Room Only)',
    'ai': 'AI (All Inclusive)',
    'bb': 'BB (Bed & Breakfast)'
  };

  // Return the display name or uppercase the original if not found
  return mealPlanMap[normalizedMealPlan] || mealPlan.toUpperCase();
}

export default function HotelFields() {
  const { allHotels, handleHotelSave, handleHotelDelete, hotelSaveData ,endDateWise,startDateWise,setStartDateWise,setEndDateWise}: any =
    useContext(PackageContext);
  const [hotelFullDetails,setHotelFullDetails] = useState<any>({})
  const [hotelName, setHotelName] = useState("");
  const [allMealPlans,setAllMealPlans] = useState<MealPlanType[]>([])
  const [roomType, setRoomType] = useState("");
  const [allRooms, setAllRooms] = useState<any[]>([]);
  const [hotelId, setHotelId] = useState<string>("");
  const [roomId, setRoomId] = useState<string>("");
  const [mealPlan, setMealPlan] = useState<string>();
  const [noNights, setNoNights] = useState(0);
  const [sort, setSort] = useState(1);
  const [isAddOn, setIsAddOn] = useState("");

  const [price,setPrice] = useState(0);
  const [startDates,setStartDates] = useState<string[]>([])
  const [endDates,setEndDates] = useState<string[]>([])

  function handleSave() {
    if (
      hotelId &&
      roomId &&
      mealPlan &&
      noNights >= 0 &&
      sort &&
      isAddOn &&
      noNights >= 0 &&
      startDateWise >=0 &&
      endDateWise >=0 &&
      startDateWise <= endDateWise
    ) {
      let addon;
      if (isAddOn === "1") {
        addon = true;
      } else {
        addon = false;
      }
      // Get the meal plan value - match the working version exactly
      const mealPlanValue = mealPlan.split("**")[0];

      // CRITICAL: Ensure meal plan is lowercase for server compatibility
      // The front end web app expects lowercase enum values: 'ep', 'cp', 'map', 'ap'
      // This must be lowercase or it won't display in the frontend
      const normalizedMealPlan = mealPlanValue.toLowerCase();

      // Double-check that the meal plan is actually lowercase
      if (normalizedMealPlan !== normalizedMealPlan.toLowerCase()) {
        console.error("Meal plan normalization failed:", mealPlanValue, "->", normalizedMealPlan);
      }

      const hotelData: HotelDataType = {
        hotelId: hotelId,
        hotelRoomId: roomId,
        hotelName: hotelName,
        hotelRoomType: roomType,
        mealPlan: normalizedMealPlan.toLowerCase(), // Force lowercase again as a safeguard
        noOfNight: noNights,
        sort: sort,
        isAddOn: addon,
        startDateWise,
        endDateWise,
      };
      handleHotelSave(hotelData);
      setHotelId('')
      setHotelName('')
      setRoomId('')
      setRoomType('')
      setMealPlan("")
      setNoNights(0)
      setSort(hotelSaveData.length+1)
      setIsAddOn("")
    } else {
      toast.error("Enter fields correctly");
    }
  }
  function handleDelete(data:string) {
    handleHotelDelete(data);
  }

  useEffect(() => {
    if (hotelId) {
    setHotelFullDetails(allHotels.find((k:any)=>k.hotelId === hotelId))
    setRoomType("")
    setRoomId("")
    }
  }, [hotelId]);
  useEffect(() => {
    setAllRooms(hotelFullDetails?.hotelRoomDetails || []);
  },[hotelFullDetails])
  useEffect(() => {
    async function fetchMealPlans() {
      if (roomId) {
        try {
          // First try to get meal plans directly from the room data
          const selectedRoom = allRooms?.find((k) => k.hotelRoomId === roomId);

          if (selectedRoom?.mealPlan && selectedRoom.mealPlan.length > 0) {
            // Map the meal plan data to the expected format
            const mp = selectedRoom.mealPlan.map((k: any) => ({
              mealId: k?.hotelMealId,
              mealPlan: k?.mealPlan,
              price: k?.roomPrice,
              startDate: k?.startDate || [],
              endDate: k?.endDate || []
            }));

            // Add empty option at the beginning
            mp.unshift({ mealId: "", mealPlan: "", price: 0, startDate: [], endDate: [] });
            setAllMealPlans(mp || []);

            // Check if there's a previously saved meal plan for this room
            if (hotelSaveData && hotelSaveData.length > 0) {
              const saved = hotelSaveData.find(
                (h: any) => h.hotelRoomId === roomId
              );
              if (saved) {
                const found = mp.find(
                  (k: any) => k.mealPlan.toLowerCase() === saved.mealPlan.toLowerCase()
                );
                if (found) {
                  setMealPlan(found.mealPlan + "**" + found.mealId);
                  return;
                }
              }
            }

            // Set default meal plan (first one in the list)
            setMealPlan(mp[0].mealPlan ? mp[0].mealPlan + "**" + mp[0].mealId : "");
            return; // Exit early if we found meal plans in room data
          }

          // If no meal plans in room data, try to get from package data
          const { packageId, noNights = 1, noChild = 0 } = useContext(PackageContext) as any;

          if (!packageId) {
            // No packageId available, use fallback data
            const fallbackPlans = [
              { mealId: "dummy1", mealPlan: "cp", price: 1500, startDate: [], endDate: [] },
              { mealId: "dummy2", mealPlan: "map", price: 2500, startDate: [], endDate: [] },
              { mealId: "dummy3", mealPlan: "ap", price: 3500, startDate: [], endDate: [] }
            ];
            const mp = [{ mealId: "", mealPlan: "", price: 0, startDate: [], endDate: [] }, ...fallbackPlans];
            setAllMealPlans(mp);
            setMealPlan(mp[1].mealPlan + "**" + mp[1].mealId);
            return;
          }

          // Fetch package hotel data with correct parameters
          const params = {
            noOfNight: noNights,
            startDate: new Date().toISOString().split('T')[0],
            noOfChild: noChild,
            noRoomCount: 1,
            noExtraAdult: 0
          };

          // Fetch hotel data for the package
          const hotelData = await getPackageHotelData(packageId, params);

          // Extract meal plans for this specific room
          const mealPlans = getMealPlansForRoom(hotelData, hotelId, roomId);

          if (!mealPlans || mealPlans.length === 0) {
            // No meal plans found, use fallback data
            const fallbackPlans = [
              { mealId: "dummy1", mealPlan: "cp", price: 1500, startDate: [], endDate: [] },
              { mealId: "dummy2", mealPlan: "map", price: 2500, startDate: [], endDate: [] },
              { mealId: "dummy3", mealPlan: "ap", price: 3500, startDate: [], endDate: [] }
            ];
            const mp = [{ mealId: "", mealPlan: "", price: 0, startDate: [], endDate: [] }, ...fallbackPlans];
            setAllMealPlans(mp);
            setMealPlan(mp[1].mealPlan + "**" + mp[1].mealId);
            return;
          }

          // Process meal plans as before
          const mp = mealPlans.map((k: any) => ({
            mealId: k.hotelMealId,
            mealPlan: k.mealPlan,
            price: k.roomPrice,
            startDate: k.startDate || [],
            endDate: k.endDate || [],
          }));

          mp.unshift({ mealId: "", mealPlan: "", price: 0, startDate: [], endDate: [] });
          setAllMealPlans(mp || []);

          if (hotelSaveData && hotelSaveData.length > 0) {
            const saved = hotelSaveData.find(
              (h: any) => h.hotelRoomId === roomId
            );
            if (saved) {
              const found = mp.find(
                (k: any) => k.mealPlan.toLowerCase() === saved.mealPlan.toLowerCase()
              );
              if (found) {
                setMealPlan(found.mealPlan + "**" + found.mealId);
                return;
              }
            }
          }
          setMealPlan(mp[0].mealPlan ? mp[0].mealPlan + "**" + mp[0].mealId : "");
        } catch (error) {
          // Handle error in fetchMealPlans
          // Use fallback data on error
          const fallbackPlans = [
            { mealId: "dummy1", mealPlan: "cp", price: 1500, startDate: [], endDate: [] },
            { mealId: "dummy2", mealPlan: "map", price: 2500, startDate: [], endDate: [] },
            { mealId: "dummy3", mealPlan: "ap", price: 3500, startDate: [], endDate: [] }
          ];
          const mp = [{ mealId: "", mealPlan: "", price: 0, startDate: [], endDate: [] }, ...fallbackPlans];
          setAllMealPlans(mp);
          setMealPlan(mp[1].mealPlan + "**" + mp[1].mealId);
        }
      } else {
        // Reset meal plans when no room is selected
        setAllMealPlans([{ mealId: "", mealPlan: "Select Room First", price: 0, startDate: [], endDate: [] }]);
        setMealPlan("");
      }
    }
    fetchMealPlans();
  }, [roomId, hotelId, hotelSaveData, allRooms]);

  useEffect(()=>{
      setSort(hotelSaveData.length+1)
      setStartDateWise(endDateWise);
  },[hotelSaveData])

  useEffect(() => {
    if (mealPlan) {
      // Find the selected meal plan data
      const d = allMealPlans?.find(
        (k) => k.mealPlan + "**" + k.mealId === mealPlan && k.price > 0
      );
      setPrice(d?.price || 0);
      setStartDates(d?.startDate || []);
      setEndDates(d?.endDate || []);
    }
  }, [mealPlan, allMealPlans]);

  // Remove console.log to clean up the console

  return (
    <>
      {/* Hotel List Display */}
      {hotelSaveData?.length > 0 && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2 px-2">
            <h3 className="text-lg font-semibold">Added Hotels ({hotelSaveData.length})</h3>
            <button
              className="text-xs text-blue-600 hover:text-blue-800 font-medium"
              onClick={() => {
                // This is just a placeholder - you would need to implement the actual sorting logic
                // For now, it's just a visual element to improve the UI
              }}
            >
              Sort by Order
            </button>
          </div>

          <div className="max-h-[350px] overflow-y-auto border border-gray-200 rounded-lg shadow-inner bg-gray-50 p-2">
            {hotelSaveData?.map((data: any) => (
              <div
                key={data.hotelId+data.hotelRoomId+data.sort}
                className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 mb-2 last:mb-0"
              >
                <div className="p-3">
                  {/* Hotel Header with Sort Number and Delete Button */}
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center">
                      <span className="bg-blue-100 text-blue-800 font-medium rounded-full w-6 h-6 flex items-center justify-center mr-2 flex-shrink-0">
                        {data?.sort}
                      </span>
                      <h4 className="font-medium text-gray-900">{data?.hotelName}</h4>
                    </div>
                    <button
                      onClick={() => handleDelete(data?.hotelRoomId+data?.sort)}
                      className="bg-red-500 hover:bg-red-600 transition-colors duration-200 w-7 h-7 rounded-full text-white font-bold flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-red-400 flex-shrink-0"
                      title="Remove hotel"
                    >
                      ×
                    </button>
                  </div>

                  {/* Hotel Details */}
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm pl-8">
                    <div className="flex items-center">
                      <span className="text-gray-600 font-medium mr-2">Room:</span>
                      <span className={data?.hotelRoomType ? "text-gray-800" : "text-red-500 italic"}>
                        {data?.hotelRoomType || "None"}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-gray-600 font-medium mr-2">Meal Plan:</span>
                      <span className="text-gray-800">{getMealPlanDisplay(data?.mealPlan)}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-gray-600 font-medium mr-2">Nights:</span>
                      <span className="text-gray-800">{data?.noOfNight}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-gray-600 font-medium mr-2">Add-on:</span>
                      <span className="text-gray-800">{data?.isAddOn ? "Yes" : "No"}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add New Hotel Form */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-md m-2 overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Add New Hotel <span className="text-red-500">*</span></h3>
        </div>

        {/* Hotel and Room Selection */}
        <div className="p-4 space-y-4 border-b border-gray-200">
          <div className="z-50">
            <PackageSearchSelect
              setDataName={setHotelName}
              inputName={hotelName}
              allData={allHotels}
              dataName="hotelName"
              dataId="hotelId"
              setData={setHotelId}
              pHolder="Select Hotel"
            />
          </div>

          <PackageSearchSelect
            inputName={roomType}
            setDataName={setRoomType}
            allData={allRooms}
            dataName="hotelRoomType"
            dataId="hotelRoomId"
            setData={setRoomId}
            pHolder="Select Room"
          />
        </div>

        {/* Hotel Details */}
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* Meal Plan */}
            <div>
              <label htmlFor="meal-plan" className={`${label_css} block mb-1`}>
                Meal Plan
              </label>
              <select
                id="meal-plan"
                onChange={(e: any) => {
                  // Set meal plan value without converting to lowercase here
                  // The lowercase conversion happens in handleSave
                  const value = e.target.value;
                  setMealPlan(value);
                }}
                className="w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={mealPlan || ""}
                disabled={!roomId}
              >
                {allMealPlans?.map((k,i) => (
                  <option
                    key={i}
                    value={k?.mealPlan+"**"+k?.mealId}
                    disabled={!k?.mealId}
                  >
                    {k?.mealPlan ? k?.mealPlan.toUpperCase() : 'Select Meal Plan'}
                    {k?.price ? ` (₹${k?.price})` : ''}
                    {k?.mealId && k?.mealId.startsWith('dummy') ? ' - Standard' : ''}
                  </option>
                ))}
              </select>
            </div>

            {/* Number of Nights */}
            <NumberDropdown
              label="Number of Nights"
              value={noNights}
              onChange={setNoNights}
              min={0}
              max={10}
              defaultValue={0}
              id="hotel-nights"
            />

            {/* Sort Order */}
            <NumberDropdown
              label="Sort Order"
              value={sort}
              onChange={setSort}
              min={0}
              max={20}
              defaultValue={1}
              id="hotel-sort"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* Start Date Wise */}
            <NumberDropdown
              label="Start Date Wise"
              value={startDateWise}
              onChange={setStartDateWise}
              min={0}
              max={10}
              defaultValue={0}
              id="start-date-wise"
            />

            {/* End Date Wise */}
            <NumberDropdown
              label="End Date Wise"
              value={endDateWise}
              onChange={setEndDateWise}
              min={0}
              max={10}
              defaultValue={0}
              id="end-date-wise"
            />

            {/* Add-ons Radio Buttons */}
            <div>
              <label className={`${label_css} block mb-2`}>Add-ons</label>
              <div className="flex space-x-4">
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="isAc"
                    name="isAddon"
                    value={"1"}
                    checked={isAddOn === "1"}
                    onChange={(e: ChangeEvent<HTMLInputElement>) =>
                      setIsAddOn(e.target.value)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <label htmlFor="isAc" className="ml-2 text-sm text-gray-700">
                    Yes
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="isNonAc"
                    value={"0"}
                    name="isAddon"
                    checked={isAddOn === "0"}
                    onChange={(e: ChangeEvent<HTMLInputElement>) =>
                      setIsAddOn(e.target.value)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <label htmlFor="isNonAc" className="ml-2 text-sm text-gray-700">
                    No
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Meal Plan Details and Save Button */}
          <div className="flex items-center justify-between mt-6">
            <div className="flex-1">
              {mealPlan && (
                <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                  <HotelFieldsMp price={price} mealPlan={mealPlan} startDates={startDates} endDates={endDates}/>
                </div>
              )}
            </div>
            <button
              onClick={handleSave}
              className="ml-4 bg-blue-600 hover:bg-blue-700 transition-colors duration-200 text-white font-medium py-2 px-6 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
              </svg>
              Save Hotel
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

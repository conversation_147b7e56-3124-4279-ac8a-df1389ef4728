/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeEvent, useContext, useEffect, useState } from "react";
import PackageContext from "@/utils/context/PackageContext";
import PackageSearchSelect from "./PackageSearchSelect";
import { input_field_css, label_css } from "../PackageForm";
import toast from "react-hot-toast";
import HotelFieldsMp from "./HotelFieldsMp";
import { getMealPlansByRoomId } from "@/utils/api-functions/getMealPlans";

export interface HotelDataType {
  hotelId: string;
  hotelName: string;
  hotelRoomId: string;
  hotelRoomType: string;
  isAddOn: boolean;
  mealPlan: string;
  noOfNight: number;
  sort: number;
  startDateWise:number;
  endDateWise:number;
}
interface MealPlanType {
 mealId:string, mealPlan:string,price:number,startDate:string[],endDate:string[]
}
export default function HotelFields() {
  const { allHotels, handleHotelSave, handleHotelDelete, hotelSaveData ,endDateWise,startDateWise,setStartDateWise,setEndDateWise}: any =
    useContext(PackageContext);
  const [hotelFullDetails,setHotelFullDetails] = useState<any>({})
  const [hotelName, setHotelName] = useState("");
  const [allMealPlans,setAllMealPlans] = useState<MealPlanType[]>([])
  const [roomType, setRoomType] = useState("");
  const [allRooms, setAllRooms] = useState<any[]>([]);
  const [hotelId, setHotelId] = useState<string>("");
  const [roomId, setRoomId] = useState<string>("");
  const [mealPlan, setMealPlan] = useState<string>();
  const [noNights, setNoNights] = useState(0);
  const [sort, setSort] = useState(1);
  const [isAddOn, setIsAddOn] = useState("");

  const [price,setPrice] = useState(0);
  const [startDates,setStartDates] = useState<string[]>([])
  const [endDates,setEndDates] = useState<string[]>([])

  function handleSave() {
    if (
      hotelId &&
      roomId &&
      mealPlan &&
      noNights >= 0 &&
      sort &&
      isAddOn &&
      noNights >= 0 &&
      startDateWise >=0 &&
      endDateWise >=0 &&
      startDateWise <= endDateWise
    ) { 
      let addon;
      if (isAddOn === "1") {
        addon = true;
      } else {
        addon = false;
      }
      const hotelData: HotelDataType = {
        hotelId: hotelId,
        hotelRoomId: roomId,
        hotelName: hotelName,
        hotelRoomType: roomType,
        mealPlan: mealPlan.split("**")[0],
        noOfNight: noNights,
        sort: sort,
        isAddOn: addon,
        startDateWise,
        endDateWise,
      };
      handleHotelSave(hotelData);
      setHotelId('')
      setHotelName('')
      setRoomId('')
      setRoomType('')
      setMealPlan("")
      setNoNights(0)
      setSort(hotelSaveData.length+1)
      setIsAddOn("")
    } else {
      toast.error("Enter fields correctly");
    }
  }
  function handleDelete(data:string) {
    handleHotelDelete(data);
  }

  useEffect(() => {
    if (hotelId) {
    setHotelFullDetails(allHotels.find((k:any)=>k.hotelId === hotelId))
    setRoomType("")
    setRoomId("")
    }
  }, [hotelId]);
  useEffect(() => {
    setAllRooms(hotelFullDetails?.hotelRoomDetails || []);
  },[hotelFullDetails])
  useEffect(() => {
    async function fetchMealPlans() {
      if (roomId) {
        const mealPlans = await getMealPlansByRoomId(roomId);
        const mp = mealPlans.map((k: any) => ({
          mealId: k.hotelMealId,
          mealPlan: k.mealPlan,
          price: k.roomPrice,
          startDate: k.startDate,
          endDate: k.endDate,
        }));
        mp.unshift({ mealPlan: "", price: 0, startDate: [], endDate: [] });
        setAllMealPlans(mp || []);
        if (hotelSaveData && hotelSaveData.length > 0) {
          const saved = hotelSaveData.find(
            (h: any) => h.hotelRoomId === roomId
          );
          if (saved) {
            const found = mp.find(
              (k: any) => k.mealPlan.toLowerCase() === saved.mealPlan.toLowerCase()
            );
            if (found) {
              setMealPlan(found.mealPlan + "**" + found.mealId);
              return;
            }
          }
        }
        setMealPlan(mp[0].mealPlan ? mp[0].mealPlan + "**" + mp[0].mealId : "");
      }
    }
    fetchMealPlans();
  }, [roomId, hotelSaveData]);

  useEffect(()=>{
      setSort(hotelSaveData.length+1)
      setStartDateWise(endDateWise);
  },[hotelSaveData])

  useEffect(() => {
    if (mealPlan) {
      const d = allMealPlans?.find(
        (k) => k.mealPlan + "**" + k.mealId === mealPlan
      );
      setPrice(d?.price || 0);
      setStartDates(d?.startDate || []);
      setEndDates(d?.endDate || []);
    }
  }, [mealPlan, allMealPlans]);

  console.log(hotelSaveData)
 
  return (
    <>
      {hotelSaveData?.length > 0 &&
        hotelSaveData?.map((data: any) => (
          
          <div key={data.hotelId+data.hotelRoomId+data.sort} className={ "m-2" }>
            <div className="w-full bg-gray-200 border-2 shadow-xl flex px-3 py-1 justify-between">
              <div className="">
                <div className="flex">
                  <span>{data?.sort}.&nbsp;</span>
                  <span className="font-bold">
                    Hotel Name&nbsp;&nbsp;:&nbsp;&nbsp;
                  </span>
                  <span>{data?.hotelName}</span>
                </div>
                <div className="flex">
                  <span className="font-bold">
                    Room Type&nbsp;&nbsp;:&nbsp;&nbsp;
                  </span>
                  <span className={data?.hotelRoomType ? "" : "text-red-500"}>
                    {data?.hotelRoomType || "No rooms Alloted"}
                  </span>
                </div>
              </div>
              <button
                onClick={()=>handleDelete(data?.hotelRoomId+data?.sort)}
                className="bg-red-500 w-[25px] h-[25px] rounded-full text-white font-bold"
              >
                X
              </button>
            </div>
          </div>
        ))}
      <div className={ "m-2 border-2  shadow"}>
        <div className="p-2">
          <div className="z-50">
          <PackageSearchSelect
            setDataName={setHotelName}
            inputName={hotelName}
            allData={allHotels}
            dataName="hotelName"
            dataId="hotelId"
            setData={setHotelId}
            pHolder="Select Hotel"
          />
          </div>
          
        
            <PackageSearchSelect
              inputName={roomType}
              setDataName={setRoomType}
              allData={allRooms}
              dataName="hotelRoomType"
              dataId="hotelRoomId"
              setData={setRoomId}
              pHolder="Select Room"
            />
        </div>
        <div className="flex">
          <div className="w-1/6 px-2 pb-2">
            <label htmlFor="meal-plan" className={label_css}>
              Meal Plan
            </label>
            <select
              name=""
              id="meal-plan"
              onChange={(e: any) => setMealPlan(e.target.value.toLowerCase())}
              className=" w-full py-1"
              defaultValue={""}
              value={mealPlan}
            >
              {allMealPlans?.map((k,i) => (
                <option key={i} value={k?.mealPlan+"**"+k?.mealId} >
                  {k?.mealPlan.toUpperCase()}
                </option>
              ))}
            </select>
          </div>
          <div className="w-1/6 px-1">
            <label htmlFor="hotel-nights" className={label_css}>
              No. of nights
            </label>
            <input
              id="hotel-nights"
              type="number"
              min={0}
              value={noNights}
              onInput={(e: any) => setNoNights(Number(e.target.value))}
              className={input_field_css}
              placeholder="Stay Nights"
            />
          </div>
          <div className="w-1/6 px-1">
            <label htmlFor="hotel-sort" className={label_css}>
              Sort
            </label>
            <input
              id="hotel-sort"
              type="number"
              value={sort}
              onInput={(e: any) => setSort(Number(e.target.value))}
              className={input_field_css}
              placeholder="Sort Hotel"
              min={0}
            />
          </div>
          <div className="w-1/6 px-1">
            <label htmlFor="hotel-sort" className={label_css}>
            StartDateWise
            </label>
            <input
              id="hotel-sort"
              type="number"
              value={startDateWise}
              onInput={(e: any) => setStartDateWise(Number(e.target.value))}
              className={input_field_css}
              placeholder="Sort Hotel"
              min={0}
            />
          </div>
          <div className="w-1/6 px-1">
            <label htmlFor="hotel-sort" className={label_css}>
            EndDateWise
            </label>
            <input
              id="hotel-sort"
              type="number"
              value={endDateWise}
              onInput={(e: any) => setEndDateWise(Number(e.target.value))}
              className={input_field_css}
              placeholder="Sort Hotel"
              min={0}
            />
          </div>
          <div className="w-1/6 px-2 flex flex-col items-end justify-end">
            <div className="flex flex-col gap-2 px-1 ">
              <div className="flex items-center">
                <label htmlFor="isAc" className={label_css}>
                  Addons&nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;
                </label>
                <input
                  type="radio"
                  id="isAc"
                  name="isAddon"
                  value={"1"}
                  checked={isAddOn === "1"}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setIsAddOn(e.target.value)
                  }
                />
              </div>
              <div className="flex items-center">
                <label htmlFor="isNonAc" className={label_css}>
                  No Addons&nbsp;:&nbsp;
                </label>
                <input
                  type="radio"
                  id="isNonAc"
                  value={"0"}
                  name="isAddon"
                  checked={isAddOn === "0"}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setIsAddOn(e.target.value)
                  }
                />
              </div>
            </div>             
          </div>
        </div>
        <div className="flex w-full justify-between px-2">
          <div className=""></div>
          {
            mealPlan?
              <HotelFieldsMp price={price} mealPlan={mealPlan} startDates={startDates} endDates={endDates}/>  
            :<></>
          }
            <button
              onClick={handleSave}
              className=" m-2 bg-black rounded-lg shadow-xl text-white px-6 py-2"
            >
              Save
            </button>
            </div>
      </div>
    </>
  );
}

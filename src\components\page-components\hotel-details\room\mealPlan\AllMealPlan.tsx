import { useEffect, useState } from "react";
import { RoomGetData } from "../RoomDetailsHeader";
import { useParams } from "react-router-dom";
import { getHotelRoomById } from "@/utils/api-functions/getHotelRooms";
import { MealPlan } from "../RoomDetailsHeader";
import MealPlanRow from "./MealPlanRow";


export default function AllMealPlan() {
  const { id,id2 } = useParams();

  const [mealPlans, setMealPlans] = useState<MealPlan[]>([]);
  const fetchRooms = async () => {
    if (id != undefined) {
      const rooms: RoomGetData = await getHotelRoomById(id,id2 as string );
      setMealPlans(rooms.mealPlan)
      console.log(rooms)
      
    }
  };
  useEffect(() => {  
    fetchRooms();
  }, []);

  return (
    <div >
      {/* <DataTable columns={columns} data={rooms || []}/> */}
      <div className="flex p-4 border m-2 font-bold">
        <div className="w-1/6">Meal Plan</div>
        <div className="w-1/6">Room Price</div>
        <div className="w-1/6">Adult Price</div>
        <div className="w-1/6">Child Price</div>
        <div className="w-1/6">Start Dates</div>
        <div className="w-1/6">End Dates</div>
        <div className="w-[20px]"></div>
      </div>
      <div className="mb-10">
        {mealPlans?.length > 0 ? (
          mealPlans?.map((k:MealPlan) => <MealPlanRow key={k.mealPlan} data={k} />)
        ) : (
          <div className="flex justify-center items-center m-5 text-red-500">Nothing found</div>
        )}
      </div>
    </div>
  );
}
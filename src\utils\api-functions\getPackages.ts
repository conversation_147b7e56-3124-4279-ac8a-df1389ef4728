/* eslint-disable @typescript-eslint/no-explicit-any */
import api from './auth';

// Define types for the package data

export async function getPackages(offset: number, search?: string){
  try {
    const response = await api.get('admin/package', {
      params: {
        limit: 10,
        offset: offset,
        search: search || '',
      },
    });
    return response.data.result;
  } catch (error: any) {
    console.error('Error fetching packages:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch packages');
  }
}

export async function getPackage(id: string) {
  try {
    const response = await api.get(`admin/package/${id}/getOne`);
    return response.data.result;
  } catch (error: any) {
    console.error('Error fetching package:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch package');
  }
}

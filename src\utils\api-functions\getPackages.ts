/* eslint-disable @typescript-eslint/no-explicit-any */
import api from './auth';

// Define types for the package data

// Simple in-memory cache for package data
const packageCache: Record<string, { data: any, timestamp: number }> = {};
const CACHE_TTL = 60000; // 1 minute cache TTL

export async function getPackages(offset: number, search?: string, limit: number = 10){
  // Create a cache key based on the request parameters
  const cacheKey = `${offset}-${search || ''}-${limit}`;

  // Check if we have a valid cached response
  const now = Date.now();
  const cachedData = packageCache[cacheKey];

  if (cachedData && (now - cachedData.timestamp < CACHE_TTL)) {
    console.log('Using cached package data');
    return cachedData.data;
  }

  try {
    console.log('Fetching fresh package data');
    const response = await api.get('admin/package', {
      params: {
        limit: limit,
        offset: offset,
        search: search || '',
      },
    });

    // Cache the response
    const result = response.data.result;
    packageCache[cacheKey] = {
      data: result,
      timestamp: now
    };

    return result;
  } catch (error: any) {
    console.error('Error fetching packages:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch packages');
  }
}

export async function getPackage(id: string) {
  try {
    const response = await api.get(`admin/package/${id}/getOne`);
    return response.data.result;
  } catch (error: any) {
    console.error('Error fetching package:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch package');
  }
}

// Indian Holidays Utility Function
// This function checks if a given date is an Indian holiday

import { format } from 'date-fns';

// Type definition for holiday object
export interface Holiday {
  name: string;
  type: 'public' | 'religious' | 'observance';
  // Optional properties for additional information
  description?: string;
  states?: string[]; // List of Indian states where this holiday is applicable
}

// Indian holidays for 2025 (sample data)
// In a production environment, this should be fetched from an API or more complete dataset
const INDIAN_HOLIDAYS_2025: Record<string, Holiday> = {
  // January
  '2025-01-01': { name: 'New Year\'s Day', type: 'public' },
  '2025-01-14': { name: '<PERSON><PERSON>', type: 'religious' },
  '2025-01-15': { name: 'Pongal', type: 'religious', states: ['Tamil Nadu'] },
  '2025-01-26': { name: 'Republic Day', type: 'public' },
  
  // February
  '2025-02-04': { name: 'Vasant <PERSON>', type: 'religious' },
  '2025-02-19': { name: '<PERSON><PERSON>', type: 'public', states: ['Maharashtra'] },
  
  // March
  '2025-03-14': { name: '<PERSON><PERSON>', type: 'religious' },
  '2025-03-17': { name: '<PERSON><PERSON><PERSON>', type: 'religious' },
  '2025-03-18': { name: 'Holi', type: 'public' },
  
  // April
  '2025-04-11': { name: 'Rama Navami', type: 'religious' },
  '2025-04-14-1': { name: 'Dr. Ambedkar Jayanti', type: 'public' },
  '2025-04-14-2': { name: 'Tamil New Year', type: 'religious', states: ['Tamil Nadu'] },
  '2025-04-15': { name: 'Vishu', type: 'religious', states: ['Kerala'] },
  '2025-04-18': { name: 'Good Friday', type: 'religious' },
  '2025-04-20': { name: 'Easter Sunday', type: 'religious' },
  
  // May
  '2025-05-01': { name: 'Labor Day', type: 'public' },
  
  // June
  '2025-06-15': { name: 'Rath Yatra', type: 'religious', states: ['Odisha'] },
  
  // July
  '2025-07-29': { name: 'Muharram', type: 'religious' },
  
  // August
  '2025-08-15': { name: 'Independence Day', type: 'public' },
  '2025-08-19': { name: 'Raksha Bandhan', type: 'religious' },
  '2025-08-27': { name: 'Janmashtami', type: 'religious' },
  
  // September
  '2025-09-07': { name: 'Ganesh Chaturthi', type: 'religious' },
  
  // October
  '2025-10-02': { name: 'Gandhi Jayanti', type: 'public' },
  '2025-10-11': { name: 'Dussehra', type: 'public' },
  
  // November
  '2025-11-01': { name: 'Diwali', type: 'public' },
  '2025-11-02': { name: 'Govardhan Puja', type: 'religious' },
  '2025-11-03': { name: 'Bhai Dooj', type: 'religious' },
  '2025-11-15': { name: 'Guru Nanak Jayanti', type: 'religious' },
  
  // December
  '2025-12-25': { name: 'Christmas', type: 'public' }
};

/**
 * Checks if a given date is an Indian holiday
 * @param date - The date to check
 * @returns The holiday information or null if not a holiday
 */
export function getHoliday(date: Date): Holiday | null {
  // Format the date as YYYY-MM-DD
  const dateString = format(date, 'yyyy-MM-dd');
  
  // Check if this date is a holiday
  // First check exact date
  if (INDIAN_HOLIDAYS_2025[dateString]) {
    return INDIAN_HOLIDAYS_2025[dateString];
  }
  
  // Then check for dates with suffixes (for dates with multiple holidays)
  const datePrefixToCheck = dateString + '-';
  
  for (const key of Object.keys(INDIAN_HOLIDAYS_2025)) {
    if (key.startsWith(datePrefixToCheck)) {
      return INDIAN_HOLIDAYS_2025[key];
    }
  }
  
  return null;
}

/**
 * Returns all holidays for a given date (if multiple exist)
 * @param date - The date to check
 * @returns Array of holiday information or empty array if no holidays
 */
export function getAllHolidays(date: Date): Holiday[] {
  const dateString = format(date, 'yyyy-MM-dd');
  const results: Holiday[] = [];
  
  // Check for dates with and without suffixes
  for (const key of Object.keys(INDIAN_HOLIDAYS_2025)) {
    if (key === dateString || key.startsWith(dateString + '-')) {
      results.push(INDIAN_HOLIDAYS_2025[key]);
    }
  }
  
  return results;
}

export default getHoliday; 
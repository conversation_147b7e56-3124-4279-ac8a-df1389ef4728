#!/usr/bin/env python3
"""
PDF Tariff Extractor for Tripmilestone

This script extracts tariff data from hotel PDF files.
It handles different formats and extracts room type, meal plan, date range, and price.
Includes OCR fallback for scanned PDFs.

Usage:
    python extract_pdf.py <pdf_file_path>

Output:
    JSON array of extracted tariff data
"""

import sys
import os
import json
import re
import io
import logging
from datetime import datetime
import pdfplumber
import pandas as pd

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('tariff_extractor')

def clean_text(text):
    """Clean and normalize text"""
    if not text:
        return ""
    # Replace multiple spaces with a single space
    text = re.sub(r'\s+', ' ', text)
    # Remove leading/trailing whitespace
    return text.strip()

def normalize_meal_plan(meal_plan):
    """Normalize meal plan to standard format (ep, cp, map, ap)"""
    meal_plan = meal_plan.lower()

    # Map common variations to standard formats
    if re.search(r'ep|european|room only|without meals', meal_plan):
        return 'ep'
    elif re.search(r'cp|continental|breakfast|bed and breakfast|b&b', meal_plan):
        return 'cp'
    elif re.search(r'map|american|half board|breakfast.*dinner|dinner.*breakfast', meal_plan):
        return 'map'
    elif re.search(r'ap|all inclusive|full board|all meals', meal_plan):
        return 'ap'

    # Default to EP if unknown
    return 'ep'

def parse_date(date_str):
    """Parse date string into standard format (YYYY-MM-DD)"""
    date_str = clean_text(date_str)

    # Handle special case for "Dew Drops" format: "1st April 2025"
    ordinal_pattern = re.compile(r'(\d+)(st|nd|rd|th)\s+([A-Za-z]+)\s+(\d{4})')
    match = ordinal_pattern.search(date_str)
    if match:
        day = int(match.group(1))
        month_name = match.group(3)
        year = int(match.group(4))

        # Convert month name to number
        try:
            month_num = {
                'january': 1, 'february': 2, 'march': 3, 'april': 4,
                'may': 5, 'june': 6, 'july': 7, 'august': 8,
                'september': 9, 'october': 10, 'november': 11, 'december': 12
            }[month_name.lower()]

            return datetime(year, month_num, day).strftime('%Y-%m-%d')
        except (KeyError, ValueError):
            pass

    # Try different date formats
    date_formats = [
        '%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y',
        '%m/%d/%Y', '%m-%d-%Y', '%m.%d.%Y',
        '%d/%m/%y', '%d-%m/%y', '%d.%m.%y',
        '%m/%d/%y', '%m-%d-%y', '%m.%d.%y',
        '%d %b %Y', '%d %B %Y',
        '%b %d, %Y', '%B %d, %Y',
        '%d %b, %Y', '%d %B, %Y'
    ]

    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt).strftime('%Y-%m-%d')
        except ValueError:
            continue

    # Try to extract date components using regex
    # Look for patterns like "April 1, 2025" or "1 April 2025"
    month_names = "January|February|March|April|May|June|July|August|September|October|November|December"
    month_abbr = "Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec"

    # Pattern: Month Day, Year (e.g., "April 1, 2025")
    pattern1 = rf"({month_names}|{month_abbr})\s+(\d{{1,2}}),?\s+(\d{{4}})"
    match = re.search(pattern1, date_str, re.IGNORECASE)
    if match:
        month_name = match.group(1)
        day = int(match.group(2))
        year = int(match.group(3))

        try:
            month_num = {
                'january': 1, 'february': 2, 'march': 3, 'april': 4,
                'may': 5, 'june': 6, 'july': 7, 'august': 8,
                'september': 9, 'october': 10, 'november': 11, 'december': 12,
                'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'jun': 6, 'jul': 7,
                'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
            }[month_name.lower()]

            return datetime(year, month_num, day).strftime('%Y-%m-%d')
        except (KeyError, ValueError):
            pass

    # Pattern: Day Month Year (e.g., "1 April 2025")
    pattern2 = rf"(\d{{1,2}})\s+({month_names}|{month_abbr})\s+(\d{{4}})"
    match = re.search(pattern2, date_str, re.IGNORECASE)
    if match:
        day = int(match.group(1))
        month_name = match.group(2)
        year = int(match.group(3))

        try:
            month_num = {
                'january': 1, 'february': 2, 'march': 3, 'april': 4,
                'may': 5, 'june': 6, 'july': 7, 'august': 8,
                'september': 9, 'october': 10, 'november': 11, 'december': 12,
                'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'jun': 6, 'jul': 7,
                'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
            }[month_name.lower()]

            return datetime(year, month_num, day).strftime('%Y-%m-%d')
        except (KeyError, ValueError):
            pass

    # If all formats fail, return the original string
    return date_str

def extract_price(price_str):
    """Extract numeric price from string"""
    if not price_str:
        return 0

    # Extract all numbers from the string
    numbers = re.findall(r'[\d,]+\.?\d*', str(price_str))
    if not numbers:
        return 0

    # Take the first number and convert to float
    price = numbers[0].replace(',', '')
    try:
        return float(price)
    except ValueError:
        return 0

def extract_date_range_from_header(text):
    """Extract date ranges from header text like '1st April 2025 to 9th June 2025'"""
    # Look for patterns like "1st April 2025 to 9th June 2025"
    date_range_pattern = re.compile(
        r'(\d+(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})'
        r'\s+to\s+'
        r'(\d+(?:st|nd|rd|th)?\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4})',
        re.IGNORECASE
    )

    matches = date_range_pattern.findall(text)
    if matches:
        date_ranges = []
        for start_date, end_date in matches:
            start = parse_date(start_date)
            end = parse_date(end_date)
            if start and end:
                date_ranges.append((start, end))
        return date_ranges

    return []

def map_meal_plan_headers(header_row):
    """Map header names to meal plan types (ep, cp, map, ap) and ignore rack rate columns. More robust matching."""
    plan_map = {}

    # Debug log the header row
    logger.info(f"Processing header row: {header_row}")

    # First, look for exact matches for "Special Rate" or "Net Rate" as these are the target columns
    special_rate_idx = -1
    for idx, header in enumerate(header_row):
        if header is None:
            continue

        header_clean = str(header).strip().lower()
        if re.search(r'special\s*rate|net\s*rate', header_clean) and not re.search(r'rack\s*rate', header_clean):
            special_rate_idx = idx
            logger.info(f"Found explicit Special/Net Rate column at position {idx}: '{header_clean}'")

    # If we found a Special Rate column, use it as priority
    if special_rate_idx >= 0:
        # Try to determine which meal plan this Special Rate is for
        special_header = str(header_row[special_rate_idx]).strip().lower()
        if re.search(r'cp|continental|breakfast', special_header):
            plan_map['cp'] = special_rate_idx
            logger.info(f"  - Mapped CP (from special rate) to column {special_rate_idx}")
        elif re.search(r'map|american|half\s*board', special_header):
            plan_map['map'] = special_rate_idx
            logger.info(f"  - Mapped MAP (from special rate) to column {special_rate_idx}")
        elif re.search(r'ap|all\s*inclusive|full\s*board', special_header):
            plan_map['ap'] = special_rate_idx
            logger.info(f"  - Mapped AP (from special rate) to column {special_rate_idx}")
        elif re.search(r'ep|european|room\s*only', special_header):
            plan_map['ep'] = special_rate_idx
            logger.info(f"  - Mapped EP (from special rate) to column {special_rate_idx}")
        else:
            # If there's no explicit meal plan in the special rate header, default to CP
            plan_map['cp'] = special_rate_idx
            logger.info(f"  - Mapped CP (default for special rate) to column {special_rate_idx}")

        # Now process the rest of the headers normally, but we've already set our primary rate column

    # Process all headers
    for idx, header in enumerate(header_row):
        if header is None:
            continue

        header_clean = str(header).strip().lower()
        logger.info(f"Analyzing header '{header_clean}' at position {idx}")

        # Skip Rack Rate columns
        if re.search(r'rack\s*rate', header_clean):
            logger.info(f"  - Skipping Rack Rate column: {header_clean}")
            continue

        # Skip columns we know aren't rates (like "No of Rooms")
        if re.search(r'no(\.)?\s+of\s+rooms|room\s+count|available|capacity', header_clean):
            logger.info(f"  - Skipping non-rate column: {header_clean}")
            continue

        # Skip columns that are room types
        if re.search(r'^room\s+type$|^category$|^accommodation$', header_clean):
            logger.info(f"  - Skipping room type column: {header_clean}")
            continue

        # First check for columns with explicit 'special rate' or 'net rate'
        # These take priority as they're typically the correct pricing columns to use
        if re.search(r'special\s*rate|net\s*rate', header_clean) and not re.search(r'rack\s*rate', header_clean):
            # Try to determine which meal plan this rate is for
            if re.search(r'cp|continental|breakfast', header_clean):
                plan_map['cp'] = idx
                logger.info(f"  - Mapped CP (special/net rate) to column {idx}")
            elif re.search(r'map|american|half\s*board', header_clean):
                plan_map['map'] = idx
                logger.info(f"  - Mapped MAP (special/net rate) to column {idx}")
            elif re.search(r'ap|all\s*inclusive|full\s*board', header_clean):
                plan_map['ap'] = idx
                logger.info(f"  - Mapped AP (special/net rate) to column {idx}")
            elif re.search(r'ep|european|room\s*only', header_clean):
                plan_map['ep'] = idx
                logger.info(f"  - Mapped EP (special/net rate) to column {idx}")
            else:
                # If there's no explicit meal plan, look for hints
                if 'single' in header_clean or 'double' in header_clean:
                    # Some PDFs have rate columns labeled by room type rather than meal plan
                    # In this case, assume it's CP (most common default)
                    plan_map['cp'] = idx
                    logger.info(f"  - Mapped CP (assumed from {header_clean}) to column {idx}")
                else:
                    # If we found special/net rate without specific meal plan, default to CP
                    plan_map['cp'] = idx
                    logger.info(f"  - Mapped CP (default for special/net rate) to column {idx}")
        # Then check for standard meal plan columns
        elif re.search(r'cpai|cp\b|continental|breakfast|bed\s*and\s*breakfast|b&b', header_clean):
            plan_map['cp'] = idx
            logger.info(f"  - Mapped CP to column {idx}")
        elif re.search(r'mapai|map\b|half\s*board|american', header_clean):
            plan_map['map'] = idx
            logger.info(f"  - Mapped MAP to column {idx}")
        elif re.search(r'apai|ap\b|all\s*inclusive|full\s*board', header_clean):
            plan_map['ap'] = idx
            logger.info(f"  - Mapped AP to column {idx}")
        elif re.search(r'epai|ep\b|european|room\s*only', header_clean):
            plan_map['ep'] = idx
            logger.info(f"  - Mapped EP to column {idx}")
        # Check for price/amount/rate columns that don't have meal plan info
        elif re.search(r'price|amount|rate|tariff', header_clean) and not re.search(r'rack', header_clean):
            # If this is just a generic "rate" column (not rack rate), default to CP
            if 'cp' not in plan_map:  # Only use if we don't already have a CP mapping
                plan_map['cp'] = idx
                logger.info(f"  - Mapped CP (default for generic rate column) to column {idx}")

    # If we didn't find any explicit meal plan columns, look for numeric headers or currency signs
    if not plan_map:
        price_indicator_cols = []
        for idx, header in enumerate(header_row):
            if header is None:
                continue

            header_clean = str(header).strip().lower()
            # Look for numeric headers or currency symbols
            if re.match(r'^[\d,.]+$', header_clean) or re.match(r'^rs\.?\s*[\d,.]+$', header_clean) or '₹' in header_clean:
                price_indicator_cols.append(idx)
                logger.info(f"  - Found potential price indicator in column {idx}: '{header_clean}'")

        if price_indicator_cols:
            # If we found price indicators, use the first one as CP (most common default)
            plan_map['cp'] = price_indicator_cols[0]
            logger.info(f"  - Mapped CP (fallback to price indicator) to column {price_indicator_cols[0]}")

    logger.info(f"Final meal plan mapping: {plan_map}")
    return plan_map

def extract_from_tables(pdf_path):
    """Extract tariff data from tables in PDF"""
    results = []
    extraction_success = False

    try:
        with pdfplumber.open(pdf_path) as pdf:
            logger.info(f"Processing PDF with {len(pdf.pages)} pages")

            # First, try to extract date ranges from headers or text blocks
            date_ranges = []
            for page_num, page in enumerate(pdf.pages):
                text = page.extract_text()
                if text:
                    logger.info(f"Page {page_num+1} text sample: {text[:300]}...")
                    page_date_ranges = extract_date_range_from_header(text)
                    if page_date_ranges:
                        date_ranges.extend(page_date_ranges)
                        logger.info(f"Found {len(page_date_ranges)} date ranges in page {page_num+1} text")

            # Process tables
            for page_num, page in enumerate(pdf.pages):
                logger.info(f"Processing page {page_num+1}")
                tables = page.extract_tables()

                if not tables:
                    logger.warning(f"No tables found on page {page_num+1}, trying alternative extraction")
                    # Try alternative table extraction - some PDFs have tables that aren't detected properly
                    # Try with different table settings
                    tables = page.extract_tables(table_settings={"vertical_strategy": "text", "horizontal_strategy": "text"})
                    if not tables:
                        logger.warning(f"Still no tables found on page {page_num+1}. Page text for debug:\n{page.extract_text()[:500]}")
                    else:
                        logger.info(f"Found {len(tables)} tables with alternative settings on page {page_num+1}")

                if not tables:
                    continue

                logger.info(f"Found {len(tables)} tables on page {page_num+1}")

                # Extract text to look for meal plan information
                page_text = page.extract_text().lower() if page.extract_text() else ''
                meal_plan_info = None

                # Look for meal plan charges in the text
                meal_plan_patterns = [
                    (r'map\s+charges:?:?\s*(?:rs\.?|inr|₹)?\s*(\d[\d,]*\.?\d*)', 'map'),
                    (r'cp\s+charges:?:?\s*(?:rs\.?|inr|₹)?\s*(\d[\d,]*\.?\d*)', 'cp'),
                    (r'ap\s+charges:?:?\s*(?:rs\.?|inr|₹)?\s*(\d[\d,]*\.?\d*)', 'ap'),
                    (r'ep\s+charges:?:?\s*(?:rs\.?|inr|₹)?\s*(\d[\d,]*\.?\d*)', 'ep'),
                    # Additional patterns for breakfast/half-board/full-board charges
                    (r'breakfast\s+charges:?:?\s*(?:rs\.?|inr|₹)?\s*(\d[\d,]*\.?\d*)', 'cp'),
                    (r'half\s+board\s+charges:?:?\s*(?:rs\.?|inr|₹)?\s*(\d[\d,]*\.?\d*)', 'map'),
                    (r'full\s+board\s+charges:?:?\s*(?:rs\.?|inr|₹)?\s*(\d[\d,]*\.?\d*)', 'ap')
                ]

                for pattern, meal_type in meal_plan_patterns:
                    match = re.search(pattern, page_text, re.IGNORECASE)
                    if match:
                        price = extract_price(match.group(1))
                        if price > 0:
                            logger.info(f"Found {meal_type.upper()} charges in text: {price}")
                            # Store this info for later use
                            if meal_plan_info is None:
                                meal_plan_info = []
                            meal_plan_info.append({
                                "mealPlanType": meal_type,
                                "roomPrice": price
                            })

                # Look for any currency symbols in the page text to use for validation
                currency_symbols = re.findall(r'(₹|rs\.?|inr)', page_text.lower())
                logger.info(f"Found currency symbols in page text: {set(currency_symbols)}")

                for table_idx, table in enumerate(tables):
                    # Skip empty tables
                    if not table or len(table) <= 1:
                        logger.info(f"Skipping empty table {table_idx+1} on page {page_num+1}")
                        continue

                    header_row = table[0]
                    logger.info(f"Table {table_idx+1} header row: {header_row}")
                    plan_map = map_meal_plan_headers(header_row)
                    logger.info(f"Meal plan mapping for table {table_idx+1}: {plan_map}")

                    if not plan_map:
                        logger.warning(f"No meal plan columns found in table {table_idx+1} on page {page_num+1}")

                        # Let's try to find the Special Rate column directly
                        special_rate_col = None

                        # Search in header row for Special Rate
                        for idx, header in enumerate(header_row):
                            if header is None:
                                continue

                            header_clean = str(header).strip().lower()
                            if 'special rate' in header_clean or 'net rate' in header_clean:
                                special_rate_col = idx
                                logger.info(f"Found Special Rate column at index {idx}: '{header}'")
                                plan_map = {'cp': special_rate_col}  # Default to CP for Special Rate
                                break

                        # If we still don't have a column, look for price patterns in the data rows
                        if not plan_map:
                            # Collect all data values by column and look for price patterns
                            col_values = {}
                            for row in table[1:4]:  # Check first 3 data rows for a reasonable sample
                                for col_idx, cell in enumerate(row):
                                    if cell is None:
                                        continue

                                    cell_str = str(cell).strip()
                                    # Add this cell to our column values collection
                                    if col_idx not in col_values:
                                        col_values[col_idx] = []
                                    col_values[col_idx].append(cell_str)

                            # Now analyze each column to find price patterns
                            price_cols = []
                            for col_idx, values in col_values.items():
                                # Join all values to check for currency symbols
                                col_text = ' '.join(values).lower()

                                # Count how many values look like prices
                                price_count = 0
                                max_price = 0
                                for val in values:
                                    # Look for currency symbols or common price formats
                                    if re.search(r'(₹|rs\.?|inr|\$)', val.lower()) or re.match(r'^\s*[\d,]+\.?\d*\s*$', val):
                                        price = extract_price(val)
                                        if price > 0:
                                            price_count += 1
                                            max_price = max(max_price, price)

                                # If most values look like prices, this is probably a price column
                                if price_count >= len(values) * 0.5:  # At least half the values are prices
                                    logger.info(f"Column {col_idx} has {price_count}/{len(values)} price-like values, max: {max_price}")
                                    price_cols.append((col_idx, max_price))

                            # Sort price columns by max price descending
                            price_cols.sort(key=lambda x: x[1], reverse=True)

                            if price_cols:
                                # Use the column with highest prices (most likely the "special rate" rather than "number of rooms")
                                special_rate_col = price_cols[0][0]
                                logger.info(f"Using column {special_rate_col} with max price {price_cols[0][1]} as CP price column")
                                plan_map = {'cp': special_rate_col}
                            else:
                                logger.warning(f"No viable price columns found. Skipping table.")
                                continue

                        if not plan_map:
                            logger.warning(f"No viable price columns found. Skipping table.")
                            continue

                    # Identify room type column - more aggressively try to find it
                    room_col = -1
                    # First, look for explicit room type headers
                    for idx, header in enumerate(header_row):
                        if header is None:
                            continue

                        header_text = str(header).lower()
                        if re.search(r'room|type|category|accommodation|view|cottage|suite', header_text):
                            room_col = idx
                            logger.info(f"Found room type column at index {idx}: '{header}'")
                            break

                    # If we didn't find an explicit header, use first column as a fallback
                    if room_col == -1:
                        # Check if first column contains text that looks like room types
                        first_col_values = [str(row[0]).lower() if row and len(row) > 0 else "" for row in table[1:]]
                        first_col_text = " ".join(first_col_values)
                        if re.search(r'deluxe|standard|suite|executive|classic|superior|cottage|villa|room|view', first_col_text):
                            room_col = 0
                            logger.info(f"Using first column as room type (based on content)")
                        else:
                            # Default to first column anyway
                            room_col = 0
                            logger.info(f"Defaulting to first column as room type column")

                    # Identify date columns (try to find two for start/end)
                    date_cols = []
                    for idx, header in enumerate(header_row):
                        if header is None:
                            continue

                        header_text = str(header).lower()
                        if re.search(r'date|period|from|to|valid|w\.?e\.?f|applicable', header_text):
                            date_cols.append(idx)
                            logger.info(f"Found date column at index {idx}: '{header}'")

                    # Process each row
                    row_count = 0
                    for row_idx, row in enumerate(table[1:], 1):
                        # Skip empty rows
                        if not row or not any(str(val).strip() for val in row if val is not None):
                            continue

                        # Skip header-like rows or rows that only contain "rack rate"
                        row_text = ' '.join(str(val).lower() for val in row if val is not None)
                        if ('rack rate' in row_text and len(row_text.split()) <= 5) or 'room type' in row_text:
                            logger.info(f"Skipping row that appears to be a header or rack rate row: {row_text[:50]}...")
                            continue

                        # Get room type - handle case where room_col might be out of bounds
                        room_type = clean_text(str(row[room_col])) if 0 <= room_col < len(row) else ''
                        if not room_type and len(row) > 0:
                            # Fallback: use first column with text as room type
                            for i, val in enumerate(row):
                                if val is not None and str(val).strip():
                                    room_type = clean_text(str(val))
                                    logger.info(f"Using fallback room type from column {i}: '{room_type}'")
                                    break

                        logger.info(f"Processing row {row_idx} with room type: '{room_type}'")

                        # For each meal plan column, extract price
                        for plan, idx in plan_map.items():
                            if idx < len(row):
                                cell_value = row[idx]
                                if cell_value is None:
                                    continue

                                price = extract_price(cell_value)
                                logger.info(f"  - {plan.upper()} price: {price} from value '{cell_value}'")

                                # Sanity check: If the price is very low (<10) but we see ₹ or Rs. in the page,
                                # it's likely this is a room count, not a price
                                if price > 0 and price < 10 and (currency_symbols or '₹' in page_text or 'rs.' in page_text.lower()):
                                    logger.warning(f"Suspicious price value ({price}) for {room_type} - might be a room count, not a price")
                                    # Try to find the actual special rate elsewhere in the row
                                    found_better_price = False
                                    for other_idx, other_cell in enumerate(row):
                                        if other_idx != idx and other_cell is not None:
                                            # Check if this cell contains a more likely price (higher value or has currency symbol)
                                            other_cell_text = str(other_cell).lower()
                                            if ('₹' in other_cell_text or 'rs.' in other_cell_text or 'inr' in other_cell_text):
                                                other_price = extract_price(other_cell)
                                                if other_price > price:
                                                    logger.info(f"  - Found better price {other_price} in column {other_idx}")
                                                    price = other_price
                                                    found_better_price = True
                                                    break

                                    if not found_better_price:
                                        logger.warning(f"Could not find a better price for {room_type}, using {price}")

                                if price > 0:
                                    # Extract dates if available
                                    start_date, end_date = '', ''
                                    date_found = False

                                    # Try to extract from date columns in table
                                    if len(date_cols) >= 2:
                                        if date_cols[0] < len(row) and date_cols[1] < len(row):
                                            start_date = parse_date(str(row[date_cols[0]]))
                                            end_date = parse_date(str(row[date_cols[1]]))
                                            if start_date and end_date:
                                                date_found = True
                                    elif len(date_cols) == 1 and date_cols[0] < len(row):
                                        date_range = str(row[date_cols[0]])
                                        date_parts = re.split(r'\s*to\s*|\s*-\s*|\s*till\s*|\s*until\s*', date_range)
                                        if len(date_parts) >= 2:
                                            start_date = parse_date(date_parts[0])
                                            end_date = parse_date(date_parts[1])
                                            if start_date and end_date:
                                                date_found = True

                                    # If no dates found in table, use dates from text
                                    if not date_found and date_ranges:
                                        for dr_start, dr_end in date_ranges:
                                            results.append({
                                                "mealPlanType": plan,
                                                "startDate": dr_start,
                                                "endDate": dr_end,
                                                "roomPrice": price,
                                                "roomType": room_type
                                            })
                                            row_count += 1
                                            logger.info(f"  - Added entry: {plan} for {room_type} at {price} ({dr_start} to {dr_end})")
                                    else:
                                        # If we found dates in the table, or no dates at all
                                        # In case of no dates, we'll still add the entry without dates
                                        results.append({
                                            "mealPlanType": plan,
                                            "startDate": start_date,
                                            "endDate": end_date,
                                            "roomPrice": price,
                                            "roomType": room_type
                                        })
                                        row_count += 1
                                        logger.info(f"  - Added entry: {plan} for {room_type} at {price} ({start_date} to {end_date})")

                    logger.info(f"Extracted {row_count} tariff entries from table {table_idx+1}")
                    if row_count > 0:
                        extraction_success = True

                # Add meal plan info if found
                if meal_plan_info and date_ranges:
                    for meal_info in meal_plan_info:
                        for start_date, end_date in date_ranges:
                            results.append({
                                "mealPlanType": meal_info["mealPlanType"],
                                "startDate": start_date,
                                "endDate": end_date,
                                "roomPrice": meal_info["roomPrice"],
                                "roomType": "All Rooms"
                            })
                    logger.info(f"Added meal plan info for {len(date_ranges)} date ranges")

            # If no results found, try a secondary extraction with more lenient settings
            if not results:
                logger.warning("No results found with primary extraction, trying secondary extraction")
                # Here you could add a more aggressive fallback extraction method

                # For now, just try to extract any table that might have prices
                for page_num, page in enumerate(pdf.pages):
                    text = page.extract_text()
                    if not text:
                        continue

                    # Look for patterns like "Rs. 1000" or "₹ 1000" in the text
                    price_matches = re.findall(r'(?:rs\.?|inr|₹)\s*(\d[\d,]*\.?\d*)', text.lower())
                    if price_matches:
                        for price_str in price_matches:
                            price = extract_price(price_str)
                            if price > 0 and price < 100000:  # Reasonable price range
                                logger.info(f"Found price {price} in text")
                                # Since we don't know what this price is for, default to CP
                                results.append({
                                    "mealPlanType": "cp",
                                    "startDate": "",
                                    "endDate": "",
                                    "roomPrice": price,
                                    "roomType": "Standard Room"  # Default room type
                                })
                                extraction_success = True

    except Exception as e:
        logger.error(f"Error extracting tables: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # Fallback to OCR if all else fails
        try:
            import pytesseract
            from pdf2image import convert_from_path
            from PIL import Image
            logger.info("Attempting OCR fallback extraction...")
            # (OCR fallback logic would go here)
        except ImportError:
            logger.warning("OCR libraries not installed. Skipping OCR fallback.")

    # Final sanity check - if we have no results but the PDF was processed, create a default entry
    if not results and extraction_success:
        logger.warning("Extraction process completed but no valid entries found. Creating default entry.")
        results.append({
            "mealPlanType": "cp",  # Default to CP
            "startDate": "",
            "endDate": "",
            "roomPrice": 0,  # This will need to be manually corrected
            "roomType": "Standard Room"  # Default room type
        })

    logger.info(f"Total extracted entries: {len(results)}")
    return results

def main():
    if len(sys.argv) < 2:
        logger.error("Usage: python extract_pdf_improved.py <pdf_file_path>")
        print("Usage: python extract_pdf_improved.py <pdf_file_path>")
        sys.exit(1)

    pdf_path = sys.argv[1]

    if not os.path.exists(pdf_path):
        logger.error(f"Error: File {pdf_path} not found")
        print(f"Error: File {pdf_path} not found")
        sys.exit(1)

    logger.info(f"Starting extraction from {pdf_path}")

    try:
        # Extract data from the PDF
        results = extract_from_tables(pdf_path)

        # Output the results as JSON
        print(json.dumps(results, indent=2))
        logger.info(f"Extraction complete. Found {len(results)} entries.")

    except Exception as e:
        error_msg = f"Error extracting data: {str(e)}"
        logger.error(error_msg)
        print(error_msg, file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()

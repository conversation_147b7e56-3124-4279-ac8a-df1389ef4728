#!/usr/bin/env python3
"""
Data Models for PDF Extraction

This module defines structured data models for the PDF extraction process,
ensuring consistent output format and rich metadata.
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field, asdict

@dataclass
class ExtractionSource:
    """Source information for extracted data"""
    pdf_path: str
    page_number: Optional[int] = None
    table_id: Optional[int] = None
    row_index: Optional[int] = None
    column_index: Optional[int] = None
    text_block_type: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {k: v for k, v in asdict(self).items() if v is not None}

@dataclass
class ExtractionWarning:
    """Warning or assumption made during extraction"""
    message: str
    category: str
    severity: str = "WARNING"  # INFO, WARNING, ERROR
    details: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

@dataclass
class DateRange:
    """Date range for a price entry"""
    start_date: str
    end_date: str
    is_global: bool = False  # Whether this applies to the whole document
    is_blackout: bool = False  # Whether this is a blackout period
    weekday_only: bool = False  # Whether this applies to weekdays only
    weekend_only: bool = False  # Whether this applies to weekends only
    source: Optional[ExtractionSource] = None
    confidence: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = {k: v for k, v in asdict(self).items() if k != 'source'}
        if self.source:
            result['source'] = self.source.to_dict()
        return result
    
    def __str__(self) -> str:
        """String representation"""
        return f"{self.start_date} to {self.end_date}"

@dataclass
class PriceEntry:
    """Extracted price entry with rich metadata"""
    # Core data
    room_type: str
    room_price: float
    meal_plan: str
    date_range: DateRange
    
    # Context and metadata
    hotel_id: Optional[str] = None
    hotel_name: Optional[str] = None
    room_id: Optional[str] = None
    currency: str = "INR"
    occupancy: str = "double"  # single, double, triple, etc.
    
    # Extra charges
    extra_adult_price: float = 0.0
    extra_child_with_bed_price: float = 0.0
    extra_child_without_bed_price: float = 0.0
    
    # Extraction metadata
    source: Optional[ExtractionSource] = None
    confidence: float = 1.0
    warnings: List[ExtractionWarning] = field(default_factory=list)
    extraction_timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with non-null values"""
        result = {}
        
        # Include all non-None core fields
        for field_name in ['room_type', 'room_price', 'meal_plan']:
            value = getattr(self, field_name)
            if value is not None:
                result[field_name] = value
                
        # Include date range
        result['date_range'] = self.date_range.to_dict()
        
        # Include context fields if not None
        for field_name in ['hotel_id', 'hotel_name', 'room_id', 'currency', 'occupancy']:
            value = getattr(self, field_name)
            if value is not None:
                result[field_name] = value
                
        # Include extra charges if non-zero
        for field_name in ['extra_adult_price', 'extra_child_with_bed_price', 'extra_child_without_bed_price']:
            value = getattr(self, field_name)
            if value > 0:
                result[field_name] = value
                
        # Include metadata
        result['confidence'] = self.confidence
        result['extraction_timestamp'] = self.extraction_timestamp
        
        # Include source if available
        if self.source:
            result['source'] = self.source.to_dict()
            
        # Include warnings if any
        if self.warnings:
            result['warnings'] = [w.to_dict() for w in self.warnings]
            
        return result
    
    def add_warning(self, message: str, category: str, 
                   severity: str = "WARNING", details: Dict[str, Any] = None) -> None:
        """
        Add a warning to this price entry
        
        Args:
            message: Warning message
            category: Warning category
            severity: Warning severity (INFO, WARNING, ERROR)
            details: Additional details
        """
        self.warnings.append(ExtractionWarning(
            message=message,
            category=category,
            severity=severity,
            details=details or {}
        ))
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PriceEntry':
        """
        Create a PriceEntry from a dictionary
        
        Args:
            data: Dictionary with price entry data
            
        Returns:
            PriceEntry object
        """
        # Extract and create DateRange
        date_range_data = data.pop('date_range', {})
        source_data = date_range_data.pop('source', None)
        
        source = None
        if source_data:
            source = ExtractionSource(**source_data)
            
        date_range = DateRange(
            start_date=date_range_data.get('start_date', ''),
            end_date=date_range_data.get('end_date', ''),
            is_global=date_range_data.get('is_global', False),
            is_blackout=date_range_data.get('is_blackout', False),
            weekday_only=date_range_data.get('weekday_only', False),
            weekend_only=date_range_data.get('weekend_only', False),
            source=source,
            confidence=date_range_data.get('confidence', 1.0)
        )
        
        # Extract and create warnings
        warnings_data = data.pop('warnings', [])
        warnings = [ExtractionWarning(**w) for w in warnings_data]
        
        # Extract source for the price entry
        entry_source_data = data.pop('source', None)
        entry_source = None
        if entry_source_data:
            entry_source = ExtractionSource(**entry_source_data)
            
        # Create PriceEntry with remaining data
        return cls(
            date_range=date_range,
            source=entry_source,
            warnings=warnings,
            **data
        )

@dataclass
class ExtractionResult:
    """Complete extraction result with metadata"""
    entries: List[PriceEntry]
    pdf_path: str
    hotel_id: Optional[str] = None
    hotel_name: Optional[str] = None
    room_id: Optional[str] = None
    extraction_timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    processing_time_seconds: float = 0.0
    pdf_format: Optional[str] = None
    confidence: float = 1.0
    warnings: List[ExtractionWarning] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = {
            'entries': [entry.to_dict() for entry in self.entries],
            'pdf_path': self.pdf_path,
            'extraction_timestamp': self.extraction_timestamp,
            'processing_time_seconds': self.processing_time_seconds,
            'confidence': self.confidence
        }
        
        # Include optional fields if not None
        for field_name in ['hotel_id', 'hotel_name', 'room_id', 'pdf_format']:
            value = getattr(self, field_name)
            if value is not None:
                result[field_name] = value
                
        # Include warnings if any
        if self.warnings:
            result['warnings'] = [w.to_dict() for w in self.warnings]
            
        return result
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2)
    
    def add_warning(self, message: str, category: str, 
                   severity: str = "WARNING", details: Dict[str, Any] = None) -> None:
        """
        Add a warning to the extraction result
        
        Args:
            message: Warning message
            category: Warning category
            severity: Warning severity (INFO, WARNING, ERROR)
            details: Additional details
        """
        self.warnings.append(ExtractionWarning(
            message=message,
            category=category,
            severity=severity,
            details=details or {}
        ))
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExtractionResult':
        """
        Create an ExtractionResult from a dictionary
        
        Args:
            data: Dictionary with extraction result data
            
        Returns:
            ExtractionResult object
        """
        # Extract and create entries
        entries_data = data.pop('entries', [])
        entries = [PriceEntry.from_dict(entry) for entry in entries_data]
        
        # Extract and create warnings
        warnings_data = data.pop('warnings', [])
        warnings = [ExtractionWarning(**w) for w in warnings_data]
        
        # Create ExtractionResult with remaining data
        return cls(
            entries=entries,
            warnings=warnings,
            **data
        )

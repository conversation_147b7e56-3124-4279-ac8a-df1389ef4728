import { MealPlanGetData } from "./MealPlanItem";
import { useEffect, useState, useId } from "react";
import { seasonTypesData } from "@/utils/constants/optionsData";
import { editMealPlanApi } from "@/utils/api-functions/edit/editMp";
import { MealPlanData } from "../hotel-details/room/mealPlan/AddMealPlan";
import toast from "react-hot-toast";

import { Calendar, DollarSign, Tag, PlusCircle, RefreshCw, Trash2, Undo, AlertCircle, Copy, Save, ChevronDown, ChevronUp } from "lucide-react";
import { DateRange as DateRangeComp, Range, RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import "./editMp.css";
import { getHotelRoomById } from "@/utils/api-functions/getHotelRooms";
import type { RoomGetData, MealPlan } from "@/components/page-components/hotel-details/room/RoomDetailsHeader";

interface EditMpProps {
  mp: MealPlanGetData;
  setEdit: React.Dispatch<React.SetStateAction<boolean>> | ((open: boolean) => void);
  onClose?: (shouldRefresh?: boolean) => void;
  isMaximized?: boolean;
}

// Array of colors for different date ranges
const colors = ["#3d91ff", "#ff6347", "#32cd32", "#ffa500", "#8a2be2", "#ff1493"];

export default function EditMp(props: EditMpProps) {
  // Dynamic meal plan types from backend
  const [availableMealTypes, setAvailableMealTypes] = useState<string[]>([]);
  const [loadingMealTypes, setLoadingMealTypes] = useState(true);

  // Store date ranges for the calendar
  const [dateRanges, setDateRanges] = useState<Range[]>([]);
  const [previousRanges, setPreviousRanges] = useState<Range[][]>([]);
  
  // Store other meal plans for context highlighting
  const [otherMealPlansRanges, setOtherMealPlansRanges] = useState<Range[]>([]);
  
  // Collapse state for other meal plans in maximized mode
  const [isOtherMealPlansCollapsed, setIsOtherMealPlansCollapsed] = useState(true);

  // Form state for the "Change Room Price" section
  const [formData, setFormData] = useState({
    mealPlanType: props.mp.mealPlan.toUpperCase(),
    seasonType: props.mp.seasonType || "Peak Season",
    gstPercentage: String(props.mp.gstPer || "0"),
    roomPrice: String(props.mp.roomPrice || ""),
    adultPrice: String(props.mp.adultPrice || ""),
    childPrice: String(props.mp.childPrice || "")
  });

  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());

  // Generate unique IDs for form fields
  const mealPlanId = useId();
  const seasonTypeId = useId();
  const gstId = useId();
  const roomPriceId = useId();
  const adultPriceId = useId();
  const childPriceId = useId();

  // Track which range segment is focused ([rangeIndex, segmentIndex])
  const [focusedRange, setFocusedRange] = useState<[number, 0 | 1]>([0, 0]);

  const [disable, setDisable] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});

  // Fetch available meal plan types and all meal plans for this room
  useEffect(() => {
    async function fetchMealPlanTypes() {
      try {
        // Fetch and dedupe meal plan types
        const fetched = await getHotelRoomById(props.mp.hotelId, props.mp.hotelRoomId);
        const room = fetched as RoomGetData | undefined;
        if (room?.mealPlan) {
          const list = room.mealPlan as MealPlan[];
          
          // Map to uppercase strings
          const uppercased: string[] = list.map((entry: MealPlan) => entry.mealPlan.toUpperCase());
          // Deduplicate
          const types: string[] = Array.from<string>(new Set<string>(uppercased));
          setAvailableMealTypes(types);
          
          // Create ranges for other meal plans of the SAME TYPE (not the current one being edited)
          const currentMealPlanType = props.mp.mealPlan.toUpperCase();
          const otherMealPlans = list.filter(mp => 
            mp.mealPlan.toUpperCase() === currentMealPlanType && // Same meal plan type only
            mp.hotelMealId !== props.mp.hotelMealId // But different meal plan instance
          );
          
          const otherRanges: Range[] = [];
          otherMealPlans.forEach((mp) => {
            if (mp.startDate && mp.endDate && mp.startDate.length > 0 && mp.endDate.length > 0) {
              mp.startDate.forEach((start, rangeIndex) => {
                if (start && mp.endDate[rangeIndex]) {
                  const startDate = new Date(start);
                  const endDate = new Date(mp.endDate[rangeIndex]);
                  
                  // Validate dates
                  if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                    otherRanges.push({
                      startDate,
                      endDate,
                      key: `other-${mp.hotelMealId}-${rangeIndex}`,
                      color: '#9ca3af', // Gray color for other meal plans
                      mealPlanType: mp.mealPlan.toUpperCase(),
                      isOtherMealPlan: true
                    } as Range & { mealPlanType: string; isOtherMealPlan: boolean });
                  }
                }
              });
            }
          });
          
          setOtherMealPlansRanges(otherRanges);
        }
      } catch (e) {
        console.error("Error fetching meal plan types:", e);
      } finally {
        setLoadingMealTypes(false);
      }
    }
    fetchMealPlanTypes();
  }, [props.mp.hotelId, props.mp.hotelRoomId, props.mp.hotelMealId, props.mp.mealPlan]);

  // Copy to form data from date range - select date range and quick apply price
  const copyPriceToForm = (range: Range) => {
    if (!props.mp) return;
    setFormData({
      ...formData,
      roomPrice: String(props.mp.roomPrice || 0),
      adultPrice: String(props.mp.adultPrice || 0),
      childPrice: String(props.mp.childPrice || 0),
    });
    if (range.startDate) {
      setCurrentMonth(new Date(range.startDate));
    }
  };

  // Initialize date ranges from existing meal plan dates
  useEffect(() => {
    if (props.mp.startDate?.length > 0 && props.mp.endDate?.length > 0) {
      try {
        const initialRanges = props.mp.startDate.map((start, index) => {
          // Ensure we're creating valid Date objects
          const startDate = new Date(start);
          const endDate = new Date(props.mp.endDate[index]);

          // Validate dates to ensure they're valid
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            console.error(`Invalid date detected: start=${start}, end=${props.mp.endDate[index]}`);
            // Return a fallback date range if invalid
            const today = new Date();
            return {
              startDate: today,
              endDate: today,
              key: `selection${index + 1}`,
              color: colors[index % colors.length]
            };
          }

          return {
            startDate,
            endDate,
            key: `selection${index + 1}`,
            color: colors[index % colors.length]
          };
        });

        // Set the date ranges
        setDateRanges(initialRanges);

        // Focus the last range by default
        if (initialRanges.length > 0) {
          const lastIndex = initialRanges.length - 1;
          setFocusedRange([lastIndex, 0]);

          // Ensure we have a valid date for the current month
          if (initialRanges[lastIndex].startDate) {
            setCurrentMonth(new Date(initialRanges[lastIndex].startDate));
          }
        }
      } catch (error) {
        console.error("Error initializing date ranges:", error);
        // Add a default range if there's an error
        addDateRange();
      }
    } else {
      // Add a default empty range if none exists
      addDateRange();
    }
  }, []);

  // Apply colors to date display items above calendar
  useEffect(() => {
    const applyColorsToDateDisplay = () => {
      const dateDisplayItems = document.querySelectorAll('.rdrDateDisplayItem');
      dateDisplayItems.forEach((item, index) => {
        const rangeIndex = Math.floor(index / 2); // Each range has 2 items (start and end)
        const range = dateRanges[rangeIndex];
        
        if (range && range.color) {
          const element = item as HTMLElement;
          element.style.setProperty('--range-color', range.color);
          element.style.setProperty('--range-color-light', `${range.color}15`);
          element.setAttribute('data-color', 'true');
          
          // Add a subtle border-left with the range color
          element.style.borderLeft = `4px solid ${range.color}`;
          element.style.backgroundColor = `${range.color}10`;
          element.style.borderColor = range.color;
          element.style.color = range.color;
        }
      });
    };

    // Apply colors after a short delay to ensure DOM is ready
    const timer = setTimeout(applyColorsToDateDisplay, 100);
    return () => clearTimeout(timer);
  }, [dateRanges, currentMonth]);

  const handleSelect = (ranges: RangeKeyDict) => {
    const selectedKey = Object.keys(ranges)[0];
    if (!selectedKey) return; // Guard against empty selection

    // Prevent modification of other meal plans (gray ranges)
    if (selectedKey.startsWith('other-')) {
      toast.error("Cannot modify other meal plans from this view", {
        duration: 2000,
        style: {
          borderRadius: '10px',
          background: '#FEF3C7',
          color: '#92400E',
        },
      });
      return;
    }

    // Save current state for undo
    setPreviousRanges([...previousRanges, [...dateRanges]]);

    // Remember which side (start=0 or end=1) was being edited
    const segment = focusedRange[1];

    // Extract the range index from the key (e.g., "selection1" -> 0)
    const rangeIndex = parseInt(selectedKey.replace('selection', '')) - 1;
    if (rangeIndex < 0 || rangeIndex >= dateRanges.length) return;

    // Update the date ranges with the new selection
    const updatedRanges = dateRanges.map(range => {
      if (range.key === selectedKey) {
        return { ...range, ...ranges[selectedKey] };
      }
      return range;
    });

    // Update the date ranges
    setDateRanges(updatedRanges);

    // CRITICAL: For date dragging to work properly, we need to maintain focus on the end date (segment 1)
    // when the user is dragging the end date. If we're editing the start date (segment 0), we don't need
    // to force the focus to stay on it.
    if (segment === 1) {
      // Keep focus on the end date of the current range for dragging
      setFocusedRange([rangeIndex, 1]);
    }
  };

  const addDateRange = () => {
    // Choose start = one day after the latest existing endDate (or today+1 if none exists)
    let base = new Date();
    if (dateRanges.length > 0) {
      // Extract only defined endDates
      const endDates: Date[] = dateRanges
        .filter(r => r.endDate !== undefined)
        .map(r => r.endDate as Date);
      if (endDates.length > 0) {
        // Find the latest endDate
        const maxEnd = endDates.reduce((a, b) => (b.getTime() > a.getTime() ? b : a));
        base = new Date(maxEnd.getTime());
      }
    }
    // Move to the next day to create an empty gap
    base.setDate(base.getDate() + 1);
    const newStart = base;
    const newKey = `selection${dateRanges.length + 1}`;
    const colorIndex = dateRanges.length % colors.length;

    // Save current state for undo
    setPreviousRanges([...previousRanges, [...dateRanges]]);

    // Create the new range
    const newRange = {
      startDate: newStart,
      endDate: newStart,
      key: newKey,
      color: colors[colorIndex]
    };

    // Calculate the new range index
    const newRangeIndex = dateRanges.length;

    // CRITICAL: Update the current month BEFORE updating the date ranges
    // This ensures the calendar is showing the correct month when the new range is added
    setCurrentMonth(newStart);

    // Add the new range to the date ranges
    setDateRanges([...dateRanges, newRange]);

    // IMPORTANT: Use a timeout to set the focused range AFTER the date ranges have been updated
    // This is crucial to prevent jumping back to the first range
    setTimeout(() => {
      // Focus on the newly added range
      setFocusedRange([newRangeIndex, 0]);

      // Force a re-render of the calendar to ensure it shows the correct focused range
      setDateRanges(prevRanges => [...prevRanges]);
    }, 50);

    // Add another timeout with a longer delay to ensure the focus is maintained
    setTimeout(() => {
      setFocusedRange([newRangeIndex, 0]);
    }, 300);
  };

  const handleUndo = () => {
    if (previousRanges.length > 0) {
      const lastState = previousRanges[previousRanges.length - 1];
      setDateRanges(lastState);
      setPreviousRanges(previousRanges.slice(0, -1));
    }
  };

  const handleDeleteRange = (rangeKey: string | undefined) => {
    if (!rangeKey) return; // Skip if key is undefined

    // Save current state for undo
    setPreviousRanges([...previousRanges, [...dateRanges]]);

    // Remove the specified range
    const updatedRanges = dateRanges.filter(range => range.key !== rangeKey);
    setDateRanges(updatedRanges);
  };

  const formatDate = (date: Date | undefined): string => {
    // Format date in 'DD/MM/YYYY' without timezone shifting
    if (!date) return 'Select date';
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).replace(/\//g, '/');
  };

  // Add utility for ISO date without timezone offset
  const toISODate = (date: Date | undefined): string => {
    if (!date) return '';
    const tzOffset = date.getTimezoneOffset() * 60000; // offset in ms
    return new Date(date.getTime() - tzOffset).toISOString().split('T')[0];
  };

  const validateForm = (): boolean => {
    const errors: {[key: string]: string} = {};

    if (!formData.roomPrice || parseFloat(formData.roomPrice) <= 0) {
      errors.roomPrice = "Room price is required";
    }

    if (!formData.adultPrice || parseFloat(formData.adultPrice) <= 0) {
      errors.adultPrice = "Adult price is required";
    }

    if (!formData.childPrice || parseFloat(formData.childPrice) <= 0) {
      errors.childPrice = "Child price is required";
    }

    if (dateRanges.length === 0 || !dateRanges.some(range => range.startDate && range.endDate)) {
      errors.dateRanges = "At least one date range is required";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: ""
      });
    }

    // For price fields, only allow numbers and decimal points
    if (name === "roomPrice" || name === "adultPrice" || name === "childPrice") {
      // Only allow numbers and decimal points
      if (value === "" || /^\d*\.?\d*$/.test(value)) {
        setFormData(prev => ({
          ...prev,
          [name]: value
        }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  async function handleSave() {
    if (!validateForm()) {
      toast.error("Please fix the validation errors");
      return;
    }

    setDisable(true);

    // Extract start and end dates from the dateRanges
    const startDates = dateRanges
      .filter(range => range.startDate && range.endDate)
      .map(range => toISODate(range.startDate!));

    const endDates = dateRanges
      .filter(range => range.startDate && range.endDate)
      .map(range => toISODate(range.endDate!));

    const data: MealPlanData = {
      // CRITICAL: Ensure meal plan is lowercase for server compatibility
      // The front end web app expects lowercase enum values: 'ep', 'cp', 'map', 'ap'
      mealPlan: formData.mealPlanType.toLowerCase(), // Convert to lowercase
      roomPrice: parseFloat(formData.roomPrice),
      adultPrice: parseFloat(formData.adultPrice),
      childPrice: parseFloat(formData.childPrice),
      seasonType: formData.seasonType,
      startDate: startDates,
      endDate: endDates,
      gstPer: parseInt(formData.gstPercentage),
    };

    try {
      // Show loading toast while API call is in progress
      const loadingToast = toast.loading("Updating meal plan...");

      const resp = await editMealPlanApi(props.mp.hotelRoomId, props.mp.hotelMealId, data);

      // Dismiss the loading toast
      toast.dismiss(loadingToast);

      if(resp.data.success){
        // Show success toast with more descriptive message
        toast.success("Meal plan updated successfully!", {
          duration: 3000, // 3 seconds
          icon: '🎉',
          style: {
            borderRadius: '10px',
            background: '#E8F5E9',
            color: '#1B5E20',
          },
        });

        // Delay the redirect slightly to show the success message
        setTimeout(() => {
          if (props.onClose) {
            props.onClose(true);
          } else {
        window.location.href = `/hotels/edit/${props.mp.hotelId}/${props.mp.hotelRoomId}`;
          }
        }, 1000);
      } else {
        // Show error toast with the message from API
        toast.error(resp.data.message || "Failed to update meal plan", {
          duration: 4000, // 4 seconds
          style: {
            borderRadius: '10px',
            background: '#FFEBEE',
            color: '#B71C1C',
          },
        });
        setDisable(false);
      }
    } catch (error) {
      // Show a more descriptive error message
      toast.error("Failed to update meal plan. Please try again later.", {
        duration: 4000, // 4 seconds
        style: {
          borderRadius: '10px',
          background: '#FFEBEE',
          color: '#B71C1C',
        },
      });

      console.error("Meal plan update error:", error);
      setDisable(false);
    }
  }

  // Get meal plan type full name
  const getMealPlanName = (type: string) => {
    switch(type) {
      case "EP": return "European Plan (Room Only)";
      case "CP": return "Continental Plan (Breakfast)";
      case "MAP": return "Modified American Plan (Breakfast & Dinner)";
      case "AP": return "American Plan (All Meals)";
      default: return type;
    }
  };

  // Function to navigate to a specific date range in the calendar
  const navigateToDateRange = (range: Range, index: number) => {
    // Only navigate if the startDate exists
    if (range.startDate) {
      try {
        // Create a new Date object to avoid potential issues with undefined
        const newDate = new Date(range.startDate);

        // Update the current month to show the selected range
        setCurrentMonth(newDate);

        // Focus this range and set to start date (0)
        setFocusedRange([index, 0]);

        // Force the calendar to update and show the selected range
        // This ensures the calendar stays on the selected range
        setTimeout(() => {
          // Re-apply the focused range to ensure it's properly set
          setFocusedRange([index, 0]);

          // Force a re-render of the calendar
          setDateRanges([...dateRanges]);
        }, 50);
      } catch (error) {
        console.error("Error navigating to date range:", error);
        // If there's an error, just focus the range without changing the month
        setFocusedRange([index, 0]);
      }
    }
  };

  // Function to navigate to other meal plans (read-only ranges)
  const navigateToOtherMealPlan = (range: Range) => {
    if (range.startDate) {
      try {
        // Create a new Date object
        const newDate = new Date(range.startDate);

        // Update the current month to show the selected range
        setCurrentMonth(newDate);

        // Force the calendar to update and show the range
        // We need to trigger a re-render to ensure the calendar updates
        setTimeout(() => {
          // Force a re-render of the entire range array to refresh the calendar
          setDateRanges([...dateRanges]);
          setOtherMealPlansRanges([...otherMealPlansRanges]);
        }, 50);

        // Add a longer timeout to ensure the calendar properly centers on the date
        setTimeout(() => {
          setCurrentMonth(newDate);
        }, 150);
      } catch (error) {
        console.error("Error navigating to other meal plan:", error);
        // Fallback to just setting the month
        setCurrentMonth(new Date(range.startDate));
      }
    }
  };

  return (
    <div className={`flex flex-col ${props.isMaximized ? 'max-w-none' : 'max-w-[1200px]'} mx-auto`}>
      {/* Main content container with improved spacing */}
      <div className={`grid grid-cols-1 gap-6 mb-8 ${
        props.isMaximized 
          ? 'lg:grid-cols-7' // 7 columns in maximized mode
          : 'lg:grid-cols-5'  // 5 columns in normal mode
      }`}>

        {/* Left Side - Date Range Selection - More space in maximized mode */}
        <div className={`bg-white rounded-xl shadow-md p-6 border border-gray-100 ${
          props.isMaximized 
            ? 'lg:col-span-5' // Takes 5/7 in maximized mode
            : 'lg:col-span-3' // Takes 3/5 in normal mode
        }`}>
          <div className="flex items-center justify-between mb-5">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <Calendar className="w-5 h-5 text-blue-500" />
              Select Date Ranges
            </h3>
            <div className="flex gap-2">
              <button
                onClick={handleUndo}
                disabled={previousRanges.length === 0}
                className={`inline-flex items-center gap-1.5 px-3 py-1.5 text-sm rounded-lg transition-all duration-200 ${
                  previousRanges.length === 0
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200 active:transform active:scale-95"
                }`}
                title="Undo last action"
              >
                <Undo className="w-3.5 h-3.5" />
                Undo
              </button>
              <button
                onClick={addDateRange}
                className="inline-flex items-center gap-1.5 bg-blue-50 text-blue-600 px-3 py-1.5 text-sm rounded-lg hover:bg-blue-100 transition-all duration-200 active:transform active:scale-95"
                title="Add new date range"
              >
                <PlusCircle className="w-3.5 h-3.5" />
                Add Range
              </button>
            </div>
          </div>

          {/* Calendar container with optimized layout for maximized mode */}
          <div className="overflow-hidden mb-5 shadow-sm border border-gray-100 rounded-lg">
            <div className={`rdrDateRangePickerWrapper custom-scrollbar ${
              props.isMaximized 
                ? 'flex gap-4 p-4' 
                : 'max-h-[360px] overflow-y-auto'
            }`}>
              <DateRangeComp
                ranges={[...dateRanges, ...otherMealPlansRanges]}
                onChange={handleSelect}
                focusedRange={focusedRange}
                onRangeFocusChange={setFocusedRange}
                moveRangeOnFirstSelection={false}
                months={props.isMaximized ? 2 : 1} // Show 2 months when maximized
                direction="horizontal"
                showDateDisplay={!props.isMaximized} // Hide default date display in maximized mode
                rangeColors={[...dateRanges.map(range => range.color || colors[0]), ...otherMealPlansRanges.map(() => '#9ca3af')]}
                date={currentMonth}
                minDate={new Date(new Date().getFullYear() - 2, 0, 1)}
                maxDate={new Date(new Date().getFullYear() + 2, 11, 31)}
                showMonthAndYearPickers={true}
                preventSnapRefocus={true}
                calendarFocus="forwards"
                fixedHeight={true}
                editableDateInputs={true}
                dragSelectionEnabled={true}
                className={props.isMaximized ? 'rdrMonthsHorizontal-maximized flex-1' : ''}
              />
              
              {/* Custom Date Display Panel for Maximized Mode */}
              {props.isMaximized && (
                <div className="w-80 bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h5 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-blue-500" />
                    Date Range Inputs
                  </h5>
                  <div className="space-y-2 max-h-[400px] overflow-y-auto custom-scrollbar">
                    {dateRanges.map((range, index) => (
                      range.startDate && range.endDate && (
                        <div 
                          key={range.key}
                          className="bg-white p-2 rounded-md border-l-4 shadow-sm"
                          style={{ borderLeftColor: range.color || colors[0] }}
                        >
                          <div className="flex items-center justify-between mb-1.5">
                            <span 
                              className="text-xs font-semibold"
                              style={{ color: range.color || colors[0] }}
                            >
                              Range {index + 1}
                            </span>
                            <button
                              onClick={() => navigateToDateRange(range, index)}
                              className="text-xs text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded transition-colors"
                            >
                              Focus
                            </button>
                          </div>
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <label className="text-[10px] text-gray-600 block mb-1">Start Date</label>
                              <input
                                type="date"
                                value={toISODate(range.startDate)}
                                onChange={(e) => {
                                  const newDate = new Date(e.target.value);
                                  if (!isNaN(newDate.getTime())) {
                                    const updatedRanges = dateRanges.map(r => 
                                      r.key === range.key ? { ...r, startDate: newDate } : r
                                    );
                                    setDateRanges(updatedRanges);
                                  }
                                }}
                                className="w-full text-[10px] p-1.5 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                style={{ borderColor: `${range.color}50` }}
                              />
                            </div>
                            <div>
                              <label className="text-[10px] text-gray-600 block mb-1">End Date</label>
                              <input
                                type="date"
                                value={toISODate(range.endDate)}
                                onChange={(e) => {
                                  const newDate = new Date(e.target.value);
                                  if (!isNaN(newDate.getTime())) {
                                    const updatedRanges = dateRanges.map(r => 
                                      r.key === range.key ? { ...r, endDate: newDate } : r
                                    );
                                    setDateRanges(updatedRanges);
                                  }
                                }}
                                className="w-full text-[10px] p-1.5 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                style={{ borderColor: `${range.color}50` }}
                              />
                            </div>
                          </div>
                        </div>
                      )
                                         ))}
                   </div>
                   
                   {/* Other Meal Plans Context in Side Panel */}
                   {otherMealPlansRanges.length > 0 && (
                     <div className="mt-4 border-t border-gray-200 pt-4">
                       <button
                         onClick={() => setIsOtherMealPlansCollapsed(!isOtherMealPlansCollapsed)}
                         className="w-full flex items-center justify-between text-sm font-semibold text-gray-700 hover:text-blue-600 transition-colors"
                       >
                         <div className="flex items-center gap-2">
                           <Tag className="w-4 h-4 text-gray-500" />
                           Other {formData.mealPlanType} Plans ({otherMealPlansRanges.length})
                         </div>
                         {isOtherMealPlansCollapsed ? (
                           <ChevronDown className="w-4 h-4" />
                         ) : (
                           <ChevronUp className="w-4 h-4" />
                         )}
                       </button>
                       
                       {!isOtherMealPlansCollapsed && (
                         <div className="mt-2 space-y-2 max-h-[200px] overflow-y-auto custom-scrollbar">
                           {otherMealPlansRanges.map((range) => (
                             range.startDate && range.endDate && (
                                                               <div
                                  key={range.key}
                                  className="bg-white p-2 rounded-md border-l-4 shadow-sm"
                                  style={{ borderLeftColor: '#9ca3af' }}
                                >
                                  <div className="flex items-center justify-between mb-1">
                                    <span className="text-[10px] font-semibold text-gray-600">
                                      {(range as any).mealPlanType || 'Unknown Plan'}
                                    </span>
                                    <div className="flex items-center gap-1">
                                      <button
                                        onClick={() => navigateToOtherMealPlan(range)}
                                        className="text-[9px] text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 px-1.5 py-0.5 rounded transition-colors"
                                        title="Focus on this date range"
                                      >
                                        Focus
                                      </button>
                                      <span className="text-[9px] text-gray-500 bg-gray-200 px-1.5 py-0.5 rounded">
                                        Read-only
                                      </span>
                                    </div>
                                  </div>
                                  <div className="flex items-center">
                                    <Calendar className="w-3 h-3 text-gray-400 mr-1.5" />
                                    <span className="text-[10px] text-gray-600">
                                      {formatDate(range.startDate)} — {formatDate(range.endDate)}
                                    </span>
                                  </div>
                                </div>
                             )
                           ))}
                           <div className="text-[9px] text-gray-500 flex items-center gap-1 mt-1">
                             <AlertCircle size={10} />
                             Gray ranges show other {formData.mealPlanType} seasons for context.
                           </div>
                         </div>
                       )}
                     </div>
                   )}
                 </div>
               )}
            </div>
          </div>

          {/* Calendar Legend */}
          <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
            <div className="flex flex-wrap items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-4 h-2 rounded" style={{ backgroundColor: colors[0] }}></div>
                <span className="text-blue-700 font-medium">Current {formData.mealPlanType} Plan (Editing)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-2 bg-gray-400 rounded"></div>
                <span className="text-gray-600 font-medium">Other {formData.mealPlanType} Plans (Read-only)</span>
              </div>
            </div>
          </div>

          {/* Selected Ranges Display - Enhanced with more visual distinction */}
          {dateRanges.length > 0 && (
            <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1.5">
                <Tag className="w-4 h-4 text-gray-500" />
                Selected Ranges ({dateRanges.length})
              </h4>
              <div className="space-y-2 max-h-[200px] overflow-y-auto custom-scrollbar">
                {dateRanges.map((range, index) => (
                  range.startDate && range.endDate && (
                    <div
                      key={range.key}
                      className="rounded-md overflow-hidden border-l-4 shadow-sm cursor-pointer"
                      style={{ borderLeftColor: range.color || colors[0] }}
                      onClick={() => navigateToDateRange(range, index)}
                    >
                      <div 
                        className="flex justify-between items-center px-3 py-1.5"
                        style={{ 
                          backgroundColor: `${range.color || colors[0]}15`, // 15 for light opacity
                          borderLeft: `4px solid ${range.color || colors[0]}`
                        }}
                      >
                        <span className="text-xs font-semibold" style={{ color: range.color || colors[0] }}>
                          {`Range ${index + 1}`}
                        </span>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent triggering the parent onClick
                              copyPriceToForm(range);
                            }}
                            className="text-blue-500 hover:text-blue-700 p-1 rounded"
                            title="Copy price data"
                          >
                            <Copy size={14} />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent triggering the parent onClick
                              handleDeleteRange(range.key!);
                            }}
                            className="text-red-500 hover:text-red-700 p-1 rounded"
                            title="Remove this date range"
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </div>
                      <div 
                        className="p-2 flex items-center"
                        style={{ 
                          backgroundColor: `${range.color || colors[0]}08`, // 08 for very light opacity
                          borderLeft: `4px solid ${range.color || colors[0]}`
                        }}
                      >
                        <Calendar className="w-4 h-4 mr-2" style={{ color: range.color || colors[0] }} />
                        <span className="text-sm font-medium" style={{ color: range.color || colors[0] }}>
                          {formatDate(range.startDate)} — {formatDate(range.endDate)}
                        </span>
                      </div>
                    </div>
                  )
                ))}
              </div>
              {validationErrors.dateRanges && (
                <div className="text-xs text-red-500 flex items-center gap-1 mt-2">
                  <AlertCircle size={12} />
                  {validationErrors.dateRanges}
                </div>
              )}
            </div>
          )}

          {/* Other Meal Plans Context - Only show in normal mode */}
          {!props.isMaximized && otherMealPlansRanges.length > 0 && (
            <div className="bg-gray-50 p-3 rounded-lg border border-gray-100 mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1.5">
                <Tag className="w-4 h-4 text-gray-500" />
                Other {formData.mealPlanType} Plans in this Room ({otherMealPlansRanges.length} ranges)
              </h4>
              <div className="space-y-1 max-h-[150px] overflow-y-auto custom-scrollbar">
                {otherMealPlansRanges.map((range) => (
                  range.startDate && range.endDate && (
                    <div
                      key={range.key}
                      className="rounded-md overflow-hidden border-l-4 shadow-sm bg-white"
                      style={{ borderLeftColor: '#9ca3af' }}
                    >
                      <div className="flex justify-between items-center px-3 py-1.5 bg-gray-100">
                        <span className="text-xs font-semibold text-gray-600">
                          {(range as any).mealPlanType || 'Unknown Plan'}
                        </span>
                        <span className="text-xs text-gray-500 bg-gray-200 px-2 py-0.5 rounded">
                          Read-only
                        </span>
                      </div>
                      <div className="bg-white p-2 flex items-center">
                        <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-600">
                          {formatDate(range.startDate)} — {formatDate(range.endDate)}
                        </span>
                      </div>
                    </div>
                  )
                ))}
              </div>
              <div className="mt-2 text-xs text-gray-500 flex items-center gap-1">
                <AlertCircle size={12} />
                Gray ranges show other {formData.mealPlanType} seasons/date ranges for context. They cannot be edited here.
              </div>
            </div>
          )}
          </div>

        {/* Right Side - Pricing Form - Compact in maximized mode */}
        <div className={`${
          props.isMaximized 
            ? 'lg:col-span-2' // Takes 2/7 in maximized mode (more compact)
            : 'lg:col-span-2' // Takes 2/5 in normal mode
        }`}>
          {/* Pricing Settings Card */}
          <div className={`bg-white rounded-xl shadow-md border border-gray-100 ${
            props.isMaximized ? 'p-4' : 'p-6'
          }`}>
            <h4 className={`font-medium flex items-center text-gray-800 border-b border-gray-100 ${
              props.isMaximized ? 'text-base mb-3 pb-2' : 'text-lg mb-5 pb-3'
            }`}>
              <DollarSign size={18} className="mr-2 text-green-600" />
              Pricing Settings
            </h4>

            {/* Meal Plan & Season Settings Group */}
            <form className={`mb-6 ${props.isMaximized ? 'space-y-3' : 'space-y-5'}`}>
              <div className="form-group">
                <label
                  htmlFor={mealPlanId}
                  className="block text-sm font-medium text-gray-700 mb-1.5"
                >
                  Meal Plan
                </label>
            <select
                  id={mealPlanId}
              name="mealPlanType"
              value={formData.mealPlanType}
              onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-sm"
                  aria-describedby={`${mealPlanId}-desc`}
                >
                  {loadingMealTypes ? (
                    <option disabled>Loading…</option>
                  ) : (
                    availableMealTypes.map(type => (
                      <option key={type} value={type}>
                        {getMealPlanName(type)}
                      </option>
                    ))
                  )}
            </select>
                <p id={`${mealPlanId}-desc`} className="text-xs text-gray-500 mt-1">
                  Currently: {getMealPlanName(formData.mealPlanType)}
                </p>
          </div>

              <div className="form-group">
                <label
                  htmlFor={seasonTypeId}
                  className="block text-sm font-medium text-gray-700 mb-1.5"
                >
                  Season Type
                </label>
            <select
                  id={seasonTypeId}
              name="seasonType"
              value={formData.seasonType}
              onChange={handleInputChange}
                  className="w-full p-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50"
            >
              {seasonTypesData?.map((k) => (
                <option key={k.value} value={k.value}>{k.text}</option>
              ))}
            </select>
          </div>

              <div className="form-group">
                <label
                  htmlFor={gstId}
                  className="block text-sm font-medium text-gray-700 mb-1.5"
                >
                  GST Percentage
                </label>
            <select
                  id={gstId}
              name="gstPercentage"
              value={formData.gstPercentage}
              onChange={handleInputChange}
                  className="w-full p-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50"
            >
                  <option value="0">0%</option>
                  <option value="10">12%</option>
                  <option value="12">18%</option>
            </select>
          </div>

              {/* Pricing Table */}
              <fieldset className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                <legend className="text-sm font-medium text-gray-700 px-2">Price Configuration</legend>

                <div className="space-y-4 pt-2">
                  <div className="form-group">
                    <label
                      htmlFor={roomPriceId}
                      className="block text-sm font-medium text-gray-700 mb-1.5"
                    >
                      Room Price (₹)
                    </label>
                    <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500">₹</span>
                </div>
                <input
                        type="number"
                        step="0.01"
                        id={roomPriceId}
                  name="roomPrice"
                  value={formData.roomPrice}
                  onChange={handleInputChange}
                        min="0"
                        inputMode="decimal"
                        className={`w-full p-2.5 pl-8 border ${validationErrors.roomPrice ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'} rounded-md focus:ring-2 focus:border-blue-500 bg-white`}
                  placeholder="0.00"
                        aria-invalid={!!validationErrors.roomPrice}
                        aria-describedby={validationErrors.roomPrice ? `${roomPriceId}-error` : undefined}
                />
              </div>
              {validationErrors.roomPrice && (
                      <div id={`${roomPriceId}-error`} className="text-xs text-red-500 flex items-center gap-1 mt-1">
                  <AlertCircle size={12} />
                  {validationErrors.roomPrice}
                </div>
              )}
            </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="form-group">
                      <label
                        htmlFor={adultPriceId}
                        className="block text-sm font-medium text-gray-700 mb-1.5"
                      >
                        Adult Price (₹)
                      </label>
                      <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500">₹</span>
                </div>
                <input
                          type="number"
                          step="0.01"
                          id={adultPriceId}
                  name="adultPrice"
                  value={formData.adultPrice}
                  onChange={handleInputChange}
                          min="0"
                          inputMode="decimal"
                          className={`w-full p-2.5 pl-8 border ${validationErrors.adultPrice ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'} rounded-md focus:ring-2 focus:border-blue-500 bg-white`}
                  placeholder="0.00"
                          aria-invalid={!!validationErrors.adultPrice}
                          aria-describedby={validationErrors.adultPrice ? `${adultPriceId}-error` : undefined}
                />
              </div>
              {validationErrors.adultPrice && (
                        <div id={`${adultPriceId}-error`} className="text-xs text-red-500 flex items-center gap-1 mt-1">
                  <AlertCircle size={12} />
                  {validationErrors.adultPrice}
                </div>
              )}
            </div>

                    <div className="form-group">
                      <label
                        htmlFor={childPriceId}
                        className="block text-sm font-medium text-gray-700 mb-1.5"
                      >
                        Child Price (₹)
                      </label>
                      <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500">₹</span>
                </div>
                <input
                          type="number"
                          step="0.01"
                          id={childPriceId}
                  name="childPrice"
                  value={formData.childPrice}
                  onChange={handleInputChange}
                          min="0"
                          inputMode="decimal"
                          className={`w-full p-2.5 pl-8 border ${validationErrors.childPrice ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'} rounded-md focus:ring-2 focus:border-blue-500 bg-white`}
                  placeholder="0.00"
                          aria-invalid={!!validationErrors.childPrice}
                          aria-describedby={validationErrors.childPrice ? `${childPriceId}-error` : undefined}
                />
              </div>
              {validationErrors.childPrice && (
                        <div id={`${childPriceId}-error`} className="text-xs text-red-500 flex items-center gap-1 mt-1">
                  <AlertCircle size={12} />
                  {validationErrors.childPrice}
                </div>
              )}
            </div>
          </div>
                </div>
              </fieldset>
            </form>

            {/* Save Button */}
          <button
              type="button"
            onClick={handleSave}
            disabled={disable}
            className={
              !disable
                  ? "w-full bg-blue-600 text-white py-3 rounded-md hover:bg-blue-700 transition flex items-center justify-center font-medium shadow-sm"
                : "w-full bg-blue-400 text-white py-3 rounded-md flex items-center justify-center font-medium cursor-not-allowed"
            }
              aria-busy={disable}
          >
            <Save size={18} className="mr-2" />
            Update Meal Plan
          </button>
          </div>
        </div>
      </div>

      {/* Action buttons at the bottom - Better spaced and styled */}
      <div className="flex justify-end gap-4 mb-6 px-2">
        <button
          type="button"
          onClick={() => {
            if (props.onClose) props.onClose(false);
            else props.setEdit(false);
          }}
          className="bg-white text-gray-700 px-5 py-2.5 rounded-md hover:bg-gray-100 transition border border-gray-200"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleSave}
          disabled={disable}
          className={
            !disable
              ? "bg-green-600 text-white px-5 py-2.5 rounded-md hover:bg-green-700 transition flex items-center shadow-sm"
              : "bg-green-400 text-white px-5 py-2.5 rounded-md flex items-center cursor-not-allowed"
          }
          aria-busy={disable}
        >
          <RefreshCw size={16} className="mr-2" />
          Save Changes
        </button>
      </div>
    </div>
  );
}

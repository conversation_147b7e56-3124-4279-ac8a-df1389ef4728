const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const logger = require('../utils/logger');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/tariffs');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `${req.body.hotelId || 'hotel'}-${req.body.roomId || 'room'}-${uniqueSuffix}-${file.originalname}`);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: function (req, file, cb) {
    // Accept only PDFs
    if (file.mimetype !== 'application/pdf') {
      return cb(new Error('Only PDF files are allowed'));
    }
    cb(null, true);
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

/**
 * Extract data from a tariff PDF with enhanced extraction
 * 
 * POST /api/admin/hotel/tariff/extract-enhanced
 * 
 * Request body:
 * - multipart/form-data with PDF file
 * - hotelId: string
 * - roomId: string
 * 
 * Response:
 * {
 *   success: boolean,
 *   result: {
 *     extractedData: {
 *       roomTypes: string[],
 *       dateRanges: { startDate: string, endDate: string }[],
 *       prices: { roomType: string, mealPlanType: string, startDate: string, endDate: string, roomPrice: number }[],
 *       extraCharges: { type: string, amount: number }[]
 *     },
 *     filePath: string,
 *     confidence: number
 *   }
 * }
 */
router.post('/extract-enhanced', upload.single('pdfFile'), async (req, res) => {
  try {
    const { hotelId, roomId } = req.body;
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No PDF file uploaded'
      });
    }

    // Log the extraction request
    logger.info(`[PDF_EXTRACTION] Starting enhanced extraction for hotel ${hotelId}, room ${roomId}`, {
      hotel_id: hotelId,
      room_id: roomId,
      file_path: req.file.path,
      file_name: req.file.originalname,
      file_size: req.file.size
    });

    // Run the Python extraction script
    const extractionResult = await runPythonExtraction(req.file.path, hotelId, roomId);

    // Return the extraction result
    return res.status(200).json({
      success: true,
      result: {
        extractedData: extractionResult,
        filePath: req.file.path,
        confidence: extractionResult.confidence || 0.8
      }
    });
  } catch (error) {
    logger.error(`[PDF_EXTRACTION] Error extracting data from PDF: ${error.message}`, {
      error_stack: error.stack
    });

    return res.status(500).json({
      success: false,
      message: 'Error extracting data from PDF',
      error: error.message
    });
  }
});

/**
 * Run the Python extraction script
 * 
 * @param {string} filePath - Path to the PDF file
 * @param {string} hotelId - Hotel ID
 * @param {string} roomId - Room ID
 * @returns {Promise<Object>} - Extraction result
 */
async function runPythonExtraction(filePath, hotelId, roomId) {
  return new Promise((resolve, reject) => {
    // Path to the Python script
    const scriptPath = path.join(__dirname, '../scripts/phase4_post_processing.py');
    
    // Run the Python script
    const pythonProcess = spawn('python', [
      scriptPath,
      filePath,
      '--hotel-id', hotelId,
      '--room-id', roomId,
      '--format', 'rich'
    ]);

    let dataString = '';
    let errorString = '';

    // Collect data from stdout
    pythonProcess.stdout.on('data', (data) => {
      dataString += data.toString();
    });

    // Collect errors from stderr
    pythonProcess.stderr.on('data', (data) => {
      errorString += data.toString();
    });

    // Handle process completion
    pythonProcess.on('close', (code) => {
      if (code !== 0) {
        logger.error(`[PDF_EXTRACTION] Python process exited with code ${code}`, {
          error: errorString,
          hotel_id: hotelId,
          room_id: roomId,
          file_path: filePath
        });
        
        // If the process failed but we have some data, try to parse it
        if (dataString) {
          try {
            const result = JSON.parse(dataString);
            resolve(result);
            return;
          } catch (e) {
            // If parsing fails, reject with the error
            reject(new Error(`Python extraction failed: ${errorString}`));
            return;
          }
        }
        
        reject(new Error(`Python extraction failed: ${errorString}`));
        return;
      }

      try {
        // Parse the JSON output
        const result = JSON.parse(dataString);
        resolve(result);
      } catch (error) {
        logger.error(`[PDF_EXTRACTION] Error parsing Python output: ${error.message}`, {
          error_stack: error.stack,
          output: dataString.substring(0, 1000) // Log first 1000 chars of output
        });
        reject(new Error(`Error parsing extraction result: ${error.message}`));
      }
    });
  });
}

/**
 * Map extracted data to database entities
 * 
 * POST /api/admin/hotel/tariff/map-data
 * 
 * Request body:
 * {
 *   hotelId: string,
 *   roomMappings: { [extractedRoomId: string]: string },
 *   priceMappings: { [priceId: string]: { roomId: string, mealPlan: string, startDate: string, endDate: string, price: number } },
 *   chargeMappings: { [chargeId: string]: { type: string, amount: number, scope: string } }
 * }
 * 
 * Response:
 * {
 *   success: boolean,
 *   result: {
 *     roomsCreated: number,
 *     pricesUpdated: number,
 *     chargesUpdated: number
 *   }
 * }
 */
router.post('/map-data', async (req, res) => {
  try {
    const { hotelId, roomMappings, priceMappings, chargeMappings } = req.body;
    
    // Log the mapping request
    logger.info(`[PDF_EXTRACTION] Processing mapped data for hotel ${hotelId}`, {
      hotel_id: hotelId,
      room_mappings_count: Object.keys(roomMappings || {}).length,
      price_mappings_count: Object.keys(priceMappings || {}).length,
      charge_mappings_count: Object.keys(chargeMappings || {}).length
    });

    // TODO: Implement actual database operations
    // For now, we'll just return a success response
    
    return res.status(200).json({
      success: true,
      result: {
        roomsCreated: Object.values(roomMappings || {}).filter(v => v === 'new').length,
        pricesUpdated: Object.keys(priceMappings || {}).length,
        chargesUpdated: Object.keys(chargeMappings || {}).length
      }
    });
  } catch (error) {
    logger.error(`[PDF_EXTRACTION] Error mapping data: ${error.message}`, {
      error_stack: error.stack
    });

    return res.status(500).json({
      success: false,
      message: 'Error mapping data',
      error: error.message
    });
  }
});

module.exports = router;

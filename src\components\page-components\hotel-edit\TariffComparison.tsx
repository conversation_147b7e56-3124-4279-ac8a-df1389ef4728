import React, { useState, useEffect } from 'react';
import { TariffUpload, TariffPriceData, MealPlan } from '@/types/types';
import { HotelRoom } from '@/types/types';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { Check, X, Calendar, FileText, AlertCircle } from 'lucide-react';
import { updateTariffStatus } from '@/utils/api-functions/tariff-upload';
import toast from 'react-hot-toast';

interface TariffComparisonProps {
  tariff: TariffUpload;
  existingData: MealPlan[];
  extractedData?: TariffPriceData[];
  roomName: string;
  onApprove: () => void;
  onReject: () => void;
  onClose: () => void;
  rooms: HotelRoom[];
}

const TariffComparison: React.FC<TariffComparisonProps> = ({
  tariff,
  existingData,
  extractedData = [],
  roomName,
  onApprove,
  onReject,
  onClose,
  rooms
}) => {
  const [finalData, setFinalData] = useState<TariffPriceData[]>([]);
  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [filterOption, setFilterOption] = useState<'all' | 'changes' | 'new'>('all');

  // Log context information at component initialization
  console.log('[CONTEXT_TRACE] TariffComparison - Component initialized', {
    hotel_id: tariff.hotelId,
    room_id: tariff.roomId,
    tariff_id: tariff.tariffId,
    existing_data_count: existingData.length,
    extracted_data_count: extractedData.length
  });

  // [CONTEXT_VERIFICATION] - Verify tariff and existingData context
  console.log('[CONTEXT_VERIFICATION] TariffComparison - Received Context', {
    // Tariff context (new uploaded tariff)
    tariff_hotel_id: tariff.hotelId,
    tariff_room_id: tariff.roomId,
    tariff_id: tariff.tariffId,
    tariff_file_path: tariff.filePath,

    // ExistingData context (current prices in the system)
    existing_data_count: existingData.length,
    existing_data_sample: existingData.slice(0, 3).map(item => ({
      meal_plan: item.mealPlan,
      hotel_id: item.hotelId,
      room_id: item.hotelRoomId,
      start_dates_count: item.startDate?.length || 0
    })),

    // Unique hotel and room IDs in existingData
    unique_hotel_ids: [...new Set(existingData.map(item => item.hotelId))],
    unique_room_ids: [...new Set(existingData.map(item => item.hotelRoomId))],

    // Context matching verification
    context_match: {
      all_hotel_ids_match: existingData.every(item => item.hotelId === tariff.hotelId),
      all_room_ids_match: existingData.every(item => item.hotelRoomId === tariff.roomId),
      mismatched_hotel_ids_count: existingData.filter(item => item.hotelId !== tariff.hotelId).length,
      mismatched_room_ids_count: existingData.filter(item => item.hotelRoomId !== tariff.roomId).length
    }
  });

  // PRICE VERIFICATION: Log the raw existing data before conversion with detailed validation
  console.log('[PRICE_VERIFICATION] TariffComparison - Raw existing data', {
    hotel_id: tariff.hotelId,
    room_id: tariff.roomId,
    existing_data_count: existingData.length,
    existing_data_sample: existingData.slice(0, 2),
    existing_data_meal_plans: existingData.map(mp => mp.mealPlan),
    existing_data_hotel_ids: existingData.map(mp => mp.hotelId || 'unknown'),
    existing_data_room_ids: existingData.map(mp => mp.hotelRoomId || 'unknown'),
    hotel_id_match: existingData.every(mp => mp.hotelId === tariff.hotelId),
    room_id_match: existingData.every(mp => mp.hotelRoomId === tariff.roomId),
    missing_hotel_ids: existingData.filter(mp => !mp.hotelId).length,
    missing_room_ids: existingData.filter(mp => !mp.hotelRoomId).length,
    mismatched_hotel_ids: existingData.filter(mp => mp.hotelId && mp.hotelId !== tariff.hotelId)
      .map(mp => mp.hotelId),
    mismatched_room_ids: existingData.filter(mp => mp.hotelRoomId && mp.hotelRoomId !== tariff.roomId)
      .map(mp => mp.hotelRoomId)
  });

  // PRICE VERIFICATION: Validate that existingData belongs to the correct hotel and room
  if (existingData.length > 0) {
    const allHotelIdsMatch = existingData.every(mp => mp.hotelId === tariff.hotelId);
    const allRoomIdsMatch = existingData.every(mp => mp.hotelRoomId === tariff.roomId);

    if (!allHotelIdsMatch || !allRoomIdsMatch) {
      console.warn('[PRICE_VERIFICATION] TariffComparison - WARNING: existingData contains mismatched hotel or room IDs', {
        tariff_hotel_id: tariff.hotelId,
        tariff_room_id: tariff.roomId,
        all_hotel_ids_match: allHotelIdsMatch,
        all_room_ids_match: allRoomIdsMatch,
        mismatched_entries: existingData
          .filter(mp => mp.hotelId !== tariff.hotelId || mp.hotelRoomId !== tariff.roomId)
          .map(mp => ({
            meal_plan: mp.mealPlan,
            hotel_id: mp.hotelId,
            room_id: mp.hotelRoomId
          }))
      });
    }
  }

  // Convert existing data to TariffPriceData format for comparison
  const convertedExistingData: TariffPriceData[] = existingData.flatMap(mealPlan => {
    // PRICE VERIFICATION: Log each meal plan being processed
    console.log('[PRICE_VERIFICATION] TariffComparison - Processing meal plan', {
      hotel_id: tariff.hotelId,
      room_id: tariff.roomId,
      meal_plan_type: mealPlan.mealPlan,
      meal_plan_hotel_id: mealPlan.hotelId || 'unknown',
      meal_plan_room_id: mealPlan.hotelRoomId || 'unknown',
      date_ranges_count: mealPlan.startDate.length,
      room_price: mealPlan.roomPrice
    });

    const result: TariffPriceData[] = [];

    for (let i = 0; i < mealPlan.startDate.length; i++) {
      if (mealPlan.startDate[i] && mealPlan.endDate[i]) {
        // PRICE VERIFICATION: Log each date range being converted
        console.log('[PRICE_VERIFICATION] TariffComparison - Converting date range', {
          hotel_id: tariff.hotelId,
          room_id: tariff.roomId,
          meal_plan_type: mealPlan.mealPlan,
          start_date: mealPlan.startDate[i],
          end_date: mealPlan.endDate[i],
          room_price: mealPlan.roomPrice,
          index: i
        });

        result.push({
          mealPlanType: mealPlan.mealPlan,
          startDate: mealPlan.startDate[i],
          endDate: mealPlan.endDate[i],
          roomPrice: mealPlan.roomPrice
        });
      }
    }

    return result;
  });

  // PRICE VERIFICATION: Log the converted data with detailed information
  console.log('[PRICE_VERIFICATION] TariffComparison - Converted existing data', {
    hotel_id: tariff.hotelId,
    room_id: tariff.roomId,
    converted_data_count: convertedExistingData.length,
    meal_plans: [...new Set(convertedExistingData.map(item => item.mealPlanType))],
    date_ranges_sample: convertedExistingData.slice(0, 5).map(item =>
      `${item.mealPlanType}: ${item.startDate} to ${item.endDate} - ₹${item.roomPrice}`
    ),
    expected_hotel_id: tariff.hotelId,
    expected_room_id: tariff.roomId,
    is_data_filtered_by_hotel_room: true // This should be verified in the logs
  });

  // Initialize selected items (all true by default)
  useEffect(() => {
    // [CONTEXT_VERIFICATION] - Verify context in useEffect
    console.log('[CONTEXT_VERIFICATION] TariffComparison - Data Processing Context', {
      // Tariff context
      tariff_hotel_id: tariff.hotelId,
      tariff_room_id: tariff.roomId,

      // Extracted data context
      extracted_data_count: extractedData.length,
      extracted_data_sample: extractedData.slice(0, 3).map(item => ({
        meal_plan: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        price: item.roomPrice
      })),

      // Converted existing data context
      converted_existing_data_count: convertedExistingData.length,
      converted_existing_data_sample: convertedExistingData.slice(0, 3).map(item => ({
        meal_plan: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        price: item.roomPrice
      })),

      // Context verification
      processing_stage: 'initializing_selections',
      component_lifecycle: 'useEffect_dependency_on_extractedData'
    });

    const initialSelected: Record<string, boolean> = {};

    // Ensure all extracted data has hotel and room IDs matching the tariff
    const validatedData = extractedData.map(item => {
      // Add hotelId and roomId if they're missing
      if (!item.hotelId) {
        item.hotelId = tariff.hotelId;
      }
      if (!item.roomId) {
        item.roomId = tariff.roomId;
      }
      return item;
    });

    // Initialize all selections to true by default
    validatedData.forEach((item, index) => {
      const key = `${index}-${item.mealPlanType}-${item.startDate}-${item.endDate}`;
      initialSelected[key] = true;
    });

    setSelectedItems(initialSelected);

    // Initialize final data with all items selected
    setFinalData(validatedData);
  }, [extractedData, tariff.hotelId, tariff.roomId]);

  // Update final data when selections change
  useEffect(() => {
    // [CONTEXT_VERIFICATION] - Verify context in final data update
    console.log('[CONTEXT_VERIFICATION] TariffComparison - Final Data Update Context', {
      // Tariff context
      tariff_hotel_id: tariff.hotelId,
      tariff_room_id: tariff.roomId,

      // Selection context
      selected_items_count: Object.values(selectedItems).filter(Boolean).length,
      total_items_count: extractedData.length,
      selection_percentage: extractedData.length > 0
        ? Math.round((Object.values(selectedItems).filter(Boolean).length / extractedData.length) * 100)
        : 0,

      // Processing context
      processing_stage: 'updating_final_data',
      component_lifecycle: 'useEffect_dependency_on_selectedItems'
    });

    const newFinalData = extractedData.filter((item, index) =>
      selectedItems[`${index}-${item.mealPlanType}-${item.startDate}-${item.endDate}`]
    );

    // Log the final data that will be used for approval
    console.log('[CONTEXT_VERIFICATION] TariffComparison - Final Data Selection', {
      tariff_hotel_id: tariff.hotelId,
      tariff_room_id: tariff.roomId,
      final_data_count: newFinalData.length,
      final_data_sample: newFinalData.slice(0, 3).map(item => ({
        meal_plan: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        price: item.roomPrice
      }))
    });

    setFinalData(newFinalData);
  }, [selectedItems, extractedData, tariff.hotelId, tariff.roomId]);

  // Toggle selection of an item
  const toggleSelection = (item: TariffPriceData, index: number) => {
    const key = `${index}-${item.mealPlanType}-${item.startDate}-${item.endDate}`;
    setSelectedItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Find conflicts between existing and extracted data
  const findConflict = (item: TariffPriceData): TariffPriceData | null => {
    // PRICE VERIFICATION: Log the data source we're using for comparison
    console.log('[PRICE_VERIFICATION] TariffComparison - findConflict data source', {
      hotel_id: tariff.hotelId,
      room_id: tariff.roomId,
      existing_data_source: 'convertedExistingData',
      existing_data_count: convertedExistingData.length,
      existing_data_hotel_id: tariff.hotelId, // Verify this matches the expected hotel_id
      existing_data_room_id: tariff.roomId    // Verify this matches the expected room_id
    });

    // Log the item being checked
    console.log('[FILTER_VERIFICATION] TariffComparison - Item being checked', {
      item: {
        meal_plan_type: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        room_price: item.roomPrice
      },
      tariff_context: {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        tariff_id: tariff.tariffId
      }
    });

    // Log the data we're filtering through
    console.log('[FILTER_VERIFICATION] TariffComparison - Data before filtering', {
      converted_existing_data_count: convertedExistingData.length,
      converted_existing_data_sample: convertedExistingData.slice(0, 3).map(data => ({
        meal_plan_type: data.mealPlanType,
        start_date: data.startDate,
        end_date: data.endDate,
        room_price: data.roomPrice
      })),
      source_meal_plans: existingData.slice(0, 3).map(mp => ({
        meal_plan: mp.mealPlan,
        hotel_id: mp.hotelId,
        room_id: mp.hotelRoomId
      })),
      filter_context: {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId
      }
    });

    // Make sure we're only comparing data for the current room and hotel
    // This ensures we don't compare with prices from other hotels

    // PRICE VERIFICATION: Log the exact match criteria
    console.log('[PRICE_VERIFICATION] TariffComparison - Exact match criteria', {
      hotel_id: tariff.hotelId,
      room_id: tariff.roomId,
      match_criteria: {
        mealPlanType: item.mealPlanType,
        startDate: item.startDate,
        endDate: item.endDate
      }
    });

    // Log the exact filter criteria being used
    console.log('[FILTER_VERIFICATION] TariffComparison - Exact match filter criteria', {
      filter_expression: "existing.mealPlanType === item.mealPlanType && existing.startDate === item.startDate && existing.endDate === item.endDate",
      item_values: {
        mealPlanType: item.mealPlanType,
        startDate: item.startDate,
        endDate: item.endDate
      },
      matching_meal_plan_count: convertedExistingData.filter(e => e.mealPlanType === item.mealPlanType).length,
      matching_start_date_count: convertedExistingData.filter(e => e.startDate === item.startDate).length,
      matching_end_date_count: convertedExistingData.filter(e => e.endDate === item.endDate).length,
      matching_all_criteria_count: convertedExistingData.filter(e =>
        e.mealPlanType === item.mealPlanType &&
        e.startDate === item.startDate &&
        e.endDate === item.endDate
      ).length
    });

    // First try to find an exact date range match
    const exactMatch = convertedExistingData.find(existing =>
      existing.mealPlanType === item.mealPlanType &&
      existing.startDate === item.startDate &&
      existing.endDate === item.endDate
    );

    if (exactMatch) {
      // Log the match result with detailed information
      console.log('[FILTER_VERIFICATION] TariffComparison - Exact match result', {
        found_match: true,
        match_type: 'exact',
        match_details: {
          meal_plan_type: exactMatch.mealPlanType,
          start_date: exactMatch.startDate,
          end_date: exactMatch.endDate,
          room_price: exactMatch.roomPrice
        },
        source_context: {
          hotel_id: tariff.hotelId,
          room_id: tariff.roomId,
          source_meal_plan: existingData.find(mp =>
            mp.mealPlan === exactMatch.mealPlanType &&
            mp.startDate.includes(exactMatch.startDate) &&
            mp.endDate.includes(exactMatch.endDate)
          )
        }
      });

      console.log('[PRICE_VERIFICATION] TariffComparison - Found exact match', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        meal_plan: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        new_price: item.roomPrice,
        existing_price: exactMatch.roomPrice,
        match_details: {
          item_meal_plan: item.mealPlanType,
          existing_meal_plan: exactMatch.mealPlanType,
          item_start_date: item.startDate,
          existing_start_date: exactMatch.startDate,
          item_end_date: item.endDate,
          existing_end_date: exactMatch.endDate
        }
      });
      return exactMatch;
    }

    // PRICE VERIFICATION: Log the overlapping match criteria
    console.log('[PRICE_VERIFICATION] TariffComparison - Overlapping match criteria', {
      hotel_id: tariff.hotelId,
      room_id: tariff.roomId,
      match_criteria: {
        mealPlanType: item.mealPlanType,
        date_overlap: `Looking for dates overlapping with ${item.startDate} to ${item.endDate}`
      }
    });

    // Log the overlapping filter criteria being used
    console.log('[FILTER_VERIFICATION] TariffComparison - Overlapping match filter criteria', {
      filter_expression: "existing.mealPlanType === item.mealPlanType && (new Date(existing.startDate) <= new Date(item.endDate) && new Date(existing.endDate) >= new Date(item.startDate))",
      item_values: {
        mealPlanType: item.mealPlanType,
        startDate: item.startDate,
        endDate: item.endDate,
        startDate_as_date: new Date(item.startDate).toISOString(),
        endDate_as_date: new Date(item.endDate).toISOString()
      },
      matching_meal_plan_count: convertedExistingData.filter(e => e.mealPlanType === item.mealPlanType).length,
      potential_overlapping_dates: convertedExistingData
        .filter(e => e.mealPlanType === item.mealPlanType)
        .slice(0, 3)
        .map(e => ({
          start_date: e.startDate,
          end_date: e.endDate,
          start_date_as_date: new Date(e.startDate).toISOString(),
          end_date_as_date: new Date(e.endDate).toISOString(),
          would_overlap: new Date(e.startDate) <= new Date(item.endDate) && new Date(e.endDate) >= new Date(item.startDate)
        }))
    });

    // If no exact match, find overlapping date ranges
    const overlappingMatch = convertedExistingData.find(existing =>
      existing.mealPlanType === item.mealPlanType &&
      (
        (new Date(existing.startDate) <= new Date(item.endDate) &&
         new Date(existing.endDate) >= new Date(item.startDate))
      )
    );

    if (overlappingMatch) {
      // Log the overlapping match result with detailed information
      console.log('[FILTER_VERIFICATION] TariffComparison - Overlapping match result', {
        found_match: true,
        match_type: 'overlapping',
        match_details: {
          meal_plan_type: overlappingMatch.mealPlanType,
          start_date: overlappingMatch.startDate,
          end_date: overlappingMatch.endDate,
          room_price: overlappingMatch.roomPrice
        },
        overlap_details: {
          item_date_range: `${item.startDate} to ${item.endDate}`,
          match_date_range: `${overlappingMatch.startDate} to ${overlappingMatch.endDate}`,
          overlap_start: new Date(Math.max(
            new Date(item.startDate).getTime(),
            new Date(overlappingMatch.startDate).getTime()
          )).toISOString(),
          overlap_end: new Date(Math.min(
            new Date(item.endDate).getTime(),
            new Date(overlappingMatch.endDate).getTime()
          )).toISOString()
        },
        source_context: {
          hotel_id: tariff.hotelId,
          room_id: tariff.roomId,
          source_meal_plan: existingData.find(mp =>
            mp.mealPlan === overlappingMatch.mealPlanType &&
            mp.startDate.includes(overlappingMatch.startDate) &&
            mp.endDate.includes(overlappingMatch.endDate)
          )
        }
      });

      console.log('[PRICE_VERIFICATION] TariffComparison - Found overlapping match', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        meal_plan: item.mealPlanType,
        item_date_range: `${item.startDate} to ${item.endDate}`,
        existing_date_range: `${overlappingMatch.startDate} to ${overlappingMatch.endDate}`,
        new_price: item.roomPrice,
        existing_price: overlappingMatch.roomPrice,
        overlap_details: {
          item_start_date: new Date(item.startDate).toISOString(),
          item_end_date: new Date(item.endDate).toISOString(),
          existing_start_date: new Date(overlappingMatch.startDate).toISOString(),
          existing_end_date: new Date(overlappingMatch.endDate).toISOString(),
          overlap_start: new Date(Math.max(
            new Date(item.startDate).getTime(),
            new Date(overlappingMatch.startDate).getTime()
          )).toISOString(),
          overlap_end: new Date(Math.min(
            new Date(item.endDate).getTime(),
            new Date(overlappingMatch.endDate).getTime()
          )).toISOString()
        }
      });
    } else {
      // Log the no match result
      console.log('[FILTER_VERIFICATION] TariffComparison - No match result', {
        found_match: false,
        item_details: {
          meal_plan_type: item.mealPlanType,
          start_date: item.startDate,
          end_date: item.endDate,
          room_price: item.roomPrice
        },
        available_meal_plans: [...new Set(convertedExistingData.map(e => e.mealPlanType))],
        matching_meal_plan_entries: convertedExistingData
          .filter(e => e.mealPlanType === item.mealPlanType)
          .slice(0, 3)
          .map(e => ({
            start_date: e.startDate,
            end_date: e.endDate,
            room_price: e.roomPrice
          }))
      });

      // PRICE VERIFICATION: Log when no overlapping match is found
      console.log('[PRICE_VERIFICATION] TariffComparison - No overlapping match found', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        meal_plan: item.mealPlanType,
        item_date_range: `${item.startDate} to ${item.endDate}`,
        available_date_ranges: convertedExistingData
          .filter(d => d.mealPlanType === item.mealPlanType)
          .slice(0, 5)
          .map(d => `${d.startDate} to ${d.endDate}`)
      });
    }

    return overlappingMatch || null;
  };

  // Get the correct price for comparison
  const getComparisonPrice = (item: TariffPriceData): number | null => {
    // Log the function entry with detailed context
    console.log('[FILTER_VERIFICATION] TariffComparison - getComparisonPrice entry', {
      function_name: 'getComparisonPrice',
      item_details: {
        meal_plan_type: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        room_price: item.roomPrice
      },
      tariff_context: {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        tariff_id: tariff.tariffId
      },
      data_context: {
        existing_data_count: existingData.length,
        converted_existing_data_count: convertedExistingData.length,
        extracted_data_count: extractedData.length
      }
    });

    // PRICE VERIFICATION: Log the exact data we're using to find existing prices
    console.log('[PRICE_VERIFICATION] TariffComparison - getComparisonPrice input', {
      hotel_id: tariff.hotelId,
      room_id: tariff.roomId,
      meal_plan: item.mealPlanType,
      start_date: item.startDate,
      end_date: item.endDate,
      new_price: item.roomPrice,
      existing_data_count: convertedExistingData.length,
      existing_data_sample: convertedExistingData.slice(0, 2) // Log a sample of existing data
    });

    // Log the data we're searching through
    console.log('[FILTER_VERIFICATION] TariffComparison - getComparisonPrice data source', {
      converted_existing_data_count: convertedExistingData.length,
      converted_existing_data_sample: convertedExistingData.slice(0, 3).map(data => ({
        meal_plan_type: data.mealPlanType,
        start_date: data.startDate,
        end_date: data.endDate,
        room_price: data.roomPrice
      })),
      source_meal_plans: existingData.slice(0, 3).map(mp => ({
        meal_plan: mp.mealPlan,
        hotel_id: mp.hotelId,
        room_id: mp.hotelRoomId,
        start_dates_count: mp.startDate?.length || 0
      })),
      unique_meal_plans: [...new Set(convertedExistingData.map(d => d.mealPlanType))],
      meal_plan_counts: [...new Set(convertedExistingData.map(d => d.mealPlanType))].map(mealPlan => ({
        meal_plan: mealPlan,
        count: convertedExistingData.filter(d => d.mealPlanType === mealPlan).length
      }))
    });

    // Log the exact filter conditions we're using
    console.log('[PRICE_VERIFICATION] TariffComparison - Filter conditions', {
      hotel_id: tariff.hotelId,
      room_id: tariff.roomId,
      filter_by_meal_plan: item.mealPlanType,
      filter_by_date_range: `${item.startDate} to ${item.endDate}`
    });

    // Log that we're calling findConflict
    console.log('[FILTER_VERIFICATION] TariffComparison - Calling findConflict', {
      item_details: {
        meal_plan_type: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        room_price: item.roomPrice
      },
      search_strategy: 'First try exact match, then try overlapping date ranges'
    });

    const conflict = findConflict(item);

    // Log the result of findConflict
    console.log('[FILTER_VERIFICATION] TariffComparison - findConflict result', {
      found_conflict: !!conflict,
      conflict_details: conflict ? {
        meal_plan_type: conflict.mealPlanType,
        start_date: conflict.startDate,
        end_date: conflict.endDate,
        room_price: conflict.roomPrice,
        match_type: conflict.startDate === item.startDate && conflict.endDate === item.endDate ? 'exact' : 'overlapping'
      } : null
    });

    if (!conflict) {
      console.log('[PRICE_VERIFICATION] TariffComparison - No matching price found', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        meal_plan: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        new_price: item.roomPrice,
        existing_meal_plans: [...new Set(convertedExistingData.map(d => d.mealPlanType))],
        existing_date_ranges_sample: convertedExistingData
          .slice(0, 3)
          .map(d => `${d.mealPlanType}: ${d.startDate} to ${d.endDate}`)
      });

      // Log additional details about why no match was found
      console.log('[FILTER_VERIFICATION] TariffComparison - No match details', {
        item_meal_plan: item.mealPlanType,
        matching_meal_plan_entries: convertedExistingData.filter(d => d.mealPlanType === item.mealPlanType).length,
        meal_plan_exists: convertedExistingData.some(d => d.mealPlanType === item.mealPlanType),
        date_range_exists: convertedExistingData.some(d =>
          d.startDate === item.startDate && d.endDate === item.endDate
        ),
        overlapping_dates_exist: convertedExistingData.some(d =>
          d.mealPlanType === item.mealPlanType &&
          new Date(d.startDate) <= new Date(item.endDate) &&
          new Date(d.endDate) >= new Date(item.startDate)
        )
      });
    } else {
      console.log('[PRICE_VERIFICATION] TariffComparison - Matching price found', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        meal_plan: item.mealPlanType,
        start_date: item.startDate,
        end_date: item.endDate,
        new_price: item.roomPrice,
        existing_price: conflict.roomPrice,
        match_type: conflict.startDate === item.startDate && conflict.endDate === item.endDate ? 'exact' : 'overlapping',
        existing_date_range: `${conflict.startDate} to ${conflict.endDate}`
      });

      // Log additional details about the match
      console.log('[FILTER_VERIFICATION] TariffComparison - Match details', {
        price_difference: item.roomPrice - conflict.roomPrice,
        price_difference_percentage: Math.round(((item.roomPrice - conflict.roomPrice) / conflict.roomPrice) * 100),
        is_price_increase: item.roomPrice > conflict.roomPrice,
        is_price_decrease: item.roomPrice < conflict.roomPrice,
        is_price_same: item.roomPrice === conflict.roomPrice,
        source_meal_plan: existingData.find(mp =>
          mp.mealPlan === conflict.mealPlanType &&
          mp.startDate.includes(conflict.startDate) &&
          mp.endDate.includes(conflict.endDate)
        )
      });
    }

    // Log the function exit
    console.log('[FILTER_VERIFICATION] TariffComparison - getComparisonPrice exit', {
      function_name: 'getComparisonPrice',
      result: conflict ? conflict.roomPrice : null,
      found_match: !!conflict
    });

    return conflict ? conflict.roomPrice : null;
  };

  // Select/deselect all items for a meal plan
  const toggleMealPlanSelection = (mealPlanType: string, select: boolean) => {
    const newSelectedItems = { ...selectedItems };

    extractedData.forEach((item, index) => {
      if (item.mealPlanType === mealPlanType) {
        const key = `${index}-${item.mealPlanType}-${item.startDate}-${item.endDate}`;
        newSelectedItems[key] = select;
      }
    });

    setSelectedItems(newSelectedItems);
  };

  // Handle approval with selected data
  const handleApprove = async () => {
    if (finalData.length === 0) {
      toast.error('No tariff data selected for approval');
      return;
    }

    console.log('[CONTEXT_TRACE] TariffComparison - Approving tariff', {
      hotel_id: tariff.hotelId,
      room_id: tariff.roomId,
      tariff_id: tariff.tariffId,
      selected_data_count: finalData.length,
      meal_plans: [...new Set(finalData.map(item => item.mealPlanType))]
    });

    setIsProcessing(true);

    try {
      await updateTariffStatus(
        tariff.tariffId!,
        'approved',
        finalData,
        `Approved with ${finalData.length} price updates`
      );

      console.log('[CONTEXT_TRACE] TariffComparison - Tariff approved successfully', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        tariff_id: tariff.tariffId,
        updated_price_count: finalData.length
      });

      toast.success('Tariff approved with selected data');
      onApprove();
    } catch (error) {
      console.error('Error approving tariff:', error);

      console.log('[CONTEXT_TRACE] TariffComparison - Error approving tariff', {
        hotel_id: tariff.hotelId,
        room_id: tariff.roomId,
        tariff_id: tariff.tariffId,
        error: error instanceof Error ? error.message : String(error)
      });

      toast.error('Failed to approve tariff');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle rejection
  const handleReject = async () => {
    setIsProcessing(true);

    try {
      await updateTariffStatus(
        tariff.tariffId!,
        'rejected',
        undefined,
        'Rejected by administrator'
      );

      toast.success('Tariff rejected');
      onReject();
    } catch (error) {
      console.error('Error rejecting tariff:', error);
      toast.error('Failed to reject tariff');
    } finally {
      setIsProcessing(false);
    }
  };

  // Group extracted data by roomId
  const extractedByRoom: Record<string, TariffPriceData[]> = {};
  extractedData.forEach((item) => {
    if (!item.roomId) return;
    if (!extractedByRoom[item.roomId]) extractedByRoom[item.roomId] = [];
    extractedByRoom[item.roomId].push(item);
  });

  // Group existing data by meal plan type
  const groupedExistingData: Record<string, TariffPriceData[]> = {};
  convertedExistingData.forEach(item => {
    if (!groupedExistingData[item.mealPlanType]) {
      groupedExistingData[item.mealPlanType] = [];
    }
    groupedExistingData[item.mealPlanType].push(item);
  });

  // Helper function to format currency
  const formatCurrency = (amount: number): string => {
    return amount.toLocaleString('en-IN', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 0
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-5xl mx-auto">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-3">
        <div>
          <h2 className="text-xl font-semibold text-gray-800 flex items-center">
            <FileText className="mr-2 text-blue-500" size={20} />
            Tariff Review - {roomName}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Review extracted tariff data before approving or rejecting
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
          {/* PDF info */}
          <div className="px-3 py-2 bg-blue-50 rounded-lg flex items-center text-blue-700 text-sm">
            <FileText className="mr-2" size={16} />
            <span className="truncate max-w-[200px]">
              {tariff.filePath.split('-').pop()}
            </span>
          </div>
          
          {/* Quick stats */}
          <div className="px-3 py-2 bg-gray-100 rounded-lg flex items-center gap-1 text-sm text-gray-700">
            <span className="font-medium">{extractedData.length}</span>
            <span>prices extracted</span>
            {finalData.length > 0 && finalData.length !== extractedData.length && (
              <>
                <span className="mx-1">•</span>
                <span className="font-medium text-blue-600">{finalData.length}</span>
                <span>selected</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Alert if no data extracted */}
      {extractedData.length === 0 && (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
          <div className="flex">
            <AlertCircle className="text-amber-500 mr-3 flex-shrink-0" size={20} />
            <div>
              <h3 className="text-sm font-medium text-amber-800">No Data Extracted</h3>
              <p className="text-sm text-amber-700 mt-1">
                No price data could be extracted from this PDF. The system supports table-based PDFs with columns for room type, meal plan, date ranges, and prices.
              </p>
              <div className="mt-3">
                <button 
                  className="text-xs px-2 py-1 bg-amber-100 text-amber-800 rounded border border-amber-200 hover:bg-amber-200"
                  onClick={onClose}
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Comparison content */}
      {extractedData.length > 0 && (
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-3">
            <h3 className="text-lg font-medium text-gray-700">Tariff Price Data</h3>

            {/* Filter and Search Controls */}
            <div className="flex flex-wrap items-center gap-2">
              {/* Search Input */}
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search dates..."
                  className="pl-8 pr-3 py-1.5 text-sm border rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 w-full max-w-[180px]"
                  onChange={() => {
                    // Implement search functionality if needed
                  }}
                />
                <svg
                  className="absolute left-2.5 top-2 h-4 w-4 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>

              {/* Filter Options */}
              <div className="flex border rounded-md overflow-hidden">
                <button
                  className={`px-3 py-1.5 text-sm ${filterOption === 'all' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  onClick={() => setFilterOption('all')}
                >
                  All
                </button>
                <button
                  className={`px-3 py-1.5 text-sm ${filterOption === 'changes' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  onClick={() => setFilterOption('changes')}
                >
                  Changes
                </button>
                <button
                  className={`px-3 py-1.5 text-sm ${filterOption === 'new' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  onClick={() => setFilterOption('new')}
                >
                  New
                </button>
              </div>
            </div>
          </div>

          {/* Unified Room Table */}
          <div className="overflow-x-auto shadow-md rounded-lg border border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Meal Plan</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Price (₹)</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Existing Price (₹)</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {rooms.map((room) => {
                  const roomTariffs = extractedByRoom[room.hotelRoomId] || [];
                  if (roomTariffs.length === 0) {
                    return (
                      <tr key={room.hotelRoomId} className="bg-gray-50">
                        <td className="px-4 py-3 font-semibold text-gray-700">{room.hotelRoomType}</td>
                        <td colSpan={7} className="px-4 py-3 text-gray-400 text-center">No new tariff data extracted for this room</td>
                      </tr>
                    );
                  }
                  return roomTariffs.map((item, idx) => {
                    const existingPrice = getComparisonPrice(item);
                    const priceChange = existingPrice !== null ? item.roomPrice - existingPrice : 0;
                    const changePercent = existingPrice !== null && existingPrice > 0 ? (priceChange / existingPrice) * 100 : 0;
                    return (
                      <tr key={room.hotelRoomId + '-' + idx} className="hover:bg-blue-50">
                        <td className="px-4 py-3 font-semibold text-gray-700">{room.hotelRoomType}</td>
                        <td className="px-4 py-3 text-sm">{item.mealPlanType.toUpperCase()}</td>
                        <td className="px-4 py-3 text-sm">{format(new Date(item.startDate), 'MMM d, yyyy')}</td>
                        <td className="px-4 py-3 text-sm">{format(new Date(item.endDate), 'MMM d, yyyy')}</td>
                        <td className="px-4 py-3 text-sm font-medium">₹{formatCurrency(item.roomPrice)}</td>
                        <td className="px-4 py-3 text-sm">
                          {existingPrice !== null ? (
                            <>₹{formatCurrency(existingPrice)}</>
                          ) : (
                            <span className="text-gray-400">New</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-sm">
                          {existingPrice !== null ? (
                            <span className={`${priceChange > 0 ? 'text-green-600' : priceChange < 0 ? 'text-red-600' : 'text-gray-500'}`}>{priceChange > 0 ? '+' : ''}{formatCurrency(priceChange)}<span className="ml-1 text-xs">({changePercent > 0 ? '+' : ''}{changePercent.toFixed(1)}%)</span></span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">New</span>
                          )}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {/* Actions: select/deselect, etc. */}
                          <button
                            onClick={() => toggleSelection(item, idx)}
                            className={`inline-flex items-center px-2 py-1 border rounded-md text-xs font-medium ${selectedItems[`${idx}-${item.mealPlanType}-${item.startDate}-${item.endDate}`] ? 'border-red-200 text-red-600 hover:bg-red-50' : 'border-green-200 text-green-600 hover:bg-green-50'}`}
                          >
                            {selectedItems[`${idx}-${item.mealPlanType}-${item.startDate}-${item.endDate}`] ? 'Deselect' : 'Select'}
                          </button>
                        </td>
                      </tr>
                    );
                  });
                })}
              </tbody>
            </table>
          </div>

          {/* Quick selection buttons for meal plans */}
          <div className="flex flex-wrap gap-2 mt-4">
            {Object.keys(groupedExistingData).map(mealPlanType => (
              <div key={`control-${mealPlanType}`} className="flex border rounded overflow-hidden">
                <span className="bg-gray-100 text-gray-700 px-2 py-1 text-xs font-medium inline-flex items-center">
                  {mealPlanType.toUpperCase()}
                </span>
                <button
                  onClick={() => toggleMealPlanSelection(mealPlanType, true)}
                  className="px-2 py-1 text-xs text-blue-700 hover:bg-blue-50"
                >
                  Select All
                </button>
                <button
                  onClick={() => toggleMealPlanSelection(mealPlanType, false)}
                  className="px-2 py-1 text-xs text-red-700 hover:bg-red-50"
                >
                  Deselect All
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Summary Section */}
      <div className="bg-gray-50 border rounded-md p-4 mb-6">
        <h3 className="text-md font-medium text-gray-700 mb-2">Tariff Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-3 rounded border">
            <div className="text-sm text-gray-500">Total Extracted Prices</div>
            <div className="flex items-end justify-between">
              <div className="text-xl font-semibold">{extractedData.length}</div>
              <div className="text-xs text-gray-400">Across {Object.keys(groupedExistingData).length} meal plans</div>
            </div>
          </div>
          <div className="bg-white p-3 rounded border">
            <div className="text-sm text-gray-500">Selected for Update</div>
            <div className="flex items-end justify-between">
              <div className="text-xl font-semibold text-blue-600">{finalData.length}</div>
              <div className="text-xs text-gray-400">
                {Math.round((finalData.length / extractedData.length) * 100)}% of total
              </div>
            </div>
          </div>
          <div className="bg-white p-3 rounded border">
            <div className="text-sm text-gray-500">Price Changes</div>
            <div className="flex items-end justify-between">
              <div className="text-xl font-semibold text-green-600">
                {finalData.filter(item => {
                  const existingPrice = getComparisonPrice(item);
                  return existingPrice !== null && item.roomPrice !== existingPrice;
                }).length}
              </div>
              <div className="text-xs text-gray-400">
                Including {finalData.filter(item => getComparisonPrice(item) === null).length} new periods
              </div>
            </div>
          </div>
        </div>
        
        {/* Pricing Analysis */}
        <div className="mt-4 bg-white p-3 rounded border">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Pricing Analysis</h4>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-3 text-center">
            {(() => {
              // Calculate price change statistics
              let priceIncreases = 0;
              let priceDecreases = 0;
              let noChanges = 0;
              let newPrices = 0;
              let totalChangeAmount = 0;
              let totalChangePercent = 0;
              let changeCount = 0;
              
              finalData.forEach(item => {
                const existingPrice = getComparisonPrice(item);
                if (existingPrice === null) {
                  newPrices++;
                } else {
                  const diff = item.roomPrice - existingPrice;
                  if (diff > 0) {
                    priceIncreases++;
                    totalChangeAmount += diff;
                    totalChangePercent += (diff / existingPrice) * 100;
                    changeCount++;
                  } else if (diff < 0) {
                    priceDecreases++;
                    totalChangeAmount += diff;
                    totalChangePercent += (diff / existingPrice) * 100;
                    changeCount++;
                  } else {
                    noChanges++;
                  }
                }
              });
              
              const avgChangeAmount = changeCount > 0 ? totalChangeAmount / changeCount : 0;
              const avgChangePercent = changeCount > 0 ? totalChangePercent / changeCount : 0;
              
              return (
                <>
                  <div className="p-2 border rounded">
                    <div className="text-green-600 font-medium">{priceIncreases}</div>
                    <div className="text-xs text-gray-500">Price Increases</div>
                  </div>
                  <div className="p-2 border rounded">
                    <div className="text-red-600 font-medium">{priceDecreases}</div>
                    <div className="text-xs text-gray-500">Price Decreases</div>
                  </div>
                  <div className="p-2 border rounded">
                    <div className="text-blue-600 font-medium">{newPrices}</div>
                    <div className="text-xs text-gray-500">New Periods</div>
                  </div>
                  <div className="p-2 border rounded">
                    <div className="font-medium">
                      {avgChangeAmount > 0 ? '+' : ''}{formatCurrency(avgChangeAmount)} 
                      <span className="text-xs text-gray-500">
                        ({avgChangePercent > 0 ? '+' : ''}{avgChangePercent.toFixed(1)}%)
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">Average Change</div>
                  </div>
                </>
              );
            })()}
          </div>
        </div>

        {/* Date Range Coverage */}
        <div className="mt-4 bg-white p-3 rounded border">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Date Coverage</h4>
          {(() => {
            // Find earliest start date and latest end date
            let earliestStart = new Date();
            let latestEnd = new Date();
            
            if (finalData.length > 0) {
              earliestStart = new Date(finalData[0].startDate);
              latestEnd = new Date(finalData[0].endDate);
              
              finalData.forEach(item => {
                const startDate = new Date(item.startDate);
                const endDate = new Date(item.endDate);
                
                if (startDate < earliestStart) {
                  earliestStart = startDate;
                }
                
                if (endDate > latestEnd) {
                  latestEnd = endDate;
                }
              });
            }
            
            // Calculate total coverage in days
            const totalDays = Math.round((latestEnd.getTime() - earliestStart.getTime()) / (1000 * 60 * 60 * 24));
            
            return (
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="text-xs text-gray-500 mb-1">Coverage Period</div>
                  <div className="text-sm">
                    {format(earliestStart, 'MMM d, yyyy')} - {format(latestEnd, 'MMM d, yyyy')}
                    <span className="text-xs text-gray-500 ml-2">({totalDays} days)</span>
                  </div>
                </div>
                <div className="flex-1">
                  <div className="text-xs text-gray-500 mb-1">Meal Plans</div>
                  <div className="flex flex-wrap gap-1">
                    {[...new Set(finalData.map(item => item.mealPlanType))].map(planType => (
                      <span key={planType} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        {planType.toUpperCase()}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      </div>

      {/* Calendar View Button */}
      <div className="flex justify-center mb-6">
        <Button
          variant="outline"
          className="text-blue-700 border-blue-200 hover:bg-blue-50"
          onClick={() => window.open(`/hotels/edit/${tariff.hotelId}?view=calendar&room=${tariff.roomId}`, '_blank')}
        >
          <Calendar size={16} className="mr-2" />
          Open in Price Calendar
        </Button>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-4 mt-6 pt-4 border-t">
        <Button
          variant="outline"
          onClick={onClose}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          variant="outline"
          onClick={handleReject}
          disabled={isProcessing}
          className="text-red-600 border-red-200 hover:bg-red-50"
        >
          <X size={16} className="mr-1.5" />
          Reject Tariff
        </Button>
        <Button
          onClick={handleApprove}
          disabled={finalData.length === 0 || isProcessing}
          className="bg-green-600 text-white hover:bg-green-700"
        >
          {isProcessing ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : (
            <>
              <Check size={16} className="mr-1.5" />
              Approve Selected ({finalData.length})
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default TariffComparison;
/* eslint-disable @typescript-eslint/no-explicit-any */
import { fetchApiData } from "@/utils/api-functions/fetchApiData";
import { AMENITIES_URL } from "@/utils/urls/urls";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
interface amenitiesType {
  _id: string;
  amenitiesId: string;
  name: string;
  image: string;
  __v: number;
}
export default function AddAmenities(props: any) {
  const {id} = useParams();
  const inputClass =
    "outline-none border rounded py-1 mt-2 border-gray-400 px-2 mt-0  m-2 text-sm";
  const [selectedAmenities, setselectedAmenities] = useState<amenitiesType[]>([]);
  const [showAmenities, setShowAmenities] = useState(false);
  const [restAmenities, setRestAmenities] = useState([]);
  const [allAmenities, setAllAmenities] = useState([]);

  function handleAmenitiesDelete(str: any) {
    const d: any = selectedAmenities.filter((k: any) => k._id !== str);
    setselectedAmenities(d);
  }
  function addAmenities(e: amenitiesType) {
    setselectedAmenities([e, ...selectedAmenities]);
    setShowAmenities(false);
  }
  async function fetchAmneties() {
    try {
      const data = await fetchApiData(AMENITIES_URL)
      setAllAmenities(data);
      setRestAmenities(data);
    } catch (err) {
      console.log(err);
    }
  }
  function handleAmenities() {
    const dv = allAmenities.filter((k) => !selectedAmenities?.includes(k));
    setRestAmenities(dv);
  }
  function setInitialAmenities(){
    if(id){
      const amenities = ['Closet', 'Desk Chair', 'Telephone', 'Tv', 'Bathroom', 'WiFi', 'Coffee Maker', 'Laundry', 'Heater', 'Room Service', 'AC'];
      const filter = allAmenities.filter((k:any)=>{
        return (amenities?.includes(k.name))
      })
      setselectedAmenities(filter)
    }else{
      const amenities = ['Child Friendly','Smoke Free', 'Laundry', 'Restaurant', 'Housekeep', 'AC', 'WiFi'];
      const filter = allAmenities.filter((k:any)=>{
        return (amenities?.includes(k.name))
      })
      setselectedAmenities(filter)
    }
  }
  useEffect(() => {
    const aId: any = selectedAmenities?.map((k: any) => k.amenitiesId);
    props.setAmenetiesId(aId);
  }, [selectedAmenities]);

  useEffect(() => {
    fetchAmneties();
    setInitialAmenities()
  }, []);

  useEffect(() => {
    setInitialAmenities()
  }, [allAmenities]);
  useEffect(() => {
    handleAmenities();
  }, [selectedAmenities]);
  return (
    <div className="relative">
      <div className="flex flex-wrap border border-gray-400 m-2 text-xl">
        {selectedAmenities?.length > 0 &&
          selectedAmenities?.map((v: any) => (
            <div
              className="flex  items-center  m-1 bg-red-500 rounded-r-full rounded-l-full"
              key={v.name}
            >
              <span className=" px-2 py-1 text-white text-sm">{v.name}</span>
              <div
                onClick={() => handleAmenitiesDelete(v._id)}
                className="  bg-red-500 rounded-full mx-1 flex justify-center items-center text-white"
              >
               <div className="flex justify-center p-1 rounded-full bg-white">
               <X size={12} className="text-slate-600"/>
               </div>
              </div>
            </div>
          ))}
        <button
          onClick={() => setShowAmenities(!showAmenities)}
          className={inputClass}
        >
          Add Amenities
        </button>
        {showAmenities && (
          <div className="w-full border-2 z-40  bg-white text-black shadow absolute top-[50px] p-2 h-[200px] cursor-default">
            <div className="h-[190px] overflow-y-auto">
            {restAmenities?.length > 0 ? (
              restAmenities?.map((k: amenitiesType) => (
                <div onClick={() => addAmenities(k)} key={k.name} className="hover:bg-gray-200">
                  {k.name}
                </div>
              ))
            ) : (
              <>None</>
            )}
            </div>
           
          </div>
        )}
      </div>
    </div>
  );
}

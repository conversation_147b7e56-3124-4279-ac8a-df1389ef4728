#!/usr/bin/env python3
"""
Test suite for the Advanced PDF Tariff Extractor

This module contains unit tests and integration tests for the PDF extraction system.
It validates the extraction logic, date parsing, price extraction, and format-specific handlers.
"""

import os
import sys
import json
import unittest
from datetime import datetime
import re
from typing import Dict, List, Any, Tuple

# Add parent directory to path to import the extractor
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from advanced_pdf_extractor import TariffExtractor

# Constants
TEST_DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_data')
GOLDEN_DATA_DIR = os.path.join(TEST_DATA_DIR, 'golden')


class TestPDFExtractor(unittest.TestCase):
    """Test cases for the PDF Extractor"""

    def setUp(self):
        """Set up test environment"""
        # Create test directories if they don't exist
        os.makedirs(TEST_DATA_DIR, exist_ok=True)
        os.makedirs(GOLDEN_DATA_DIR, exist_ok=True)

    def test_date_parsing(self):
        """Test date parsing functionality"""
        # Create a minimal extractor instance for testing utility functions
        extractor = TariffExtractor("dummy.pdf")
        
        # Test various date formats
        test_cases = [
            # Format: (input_date, expected_output)
            ("1st April 2025", "2025-04-01"),
            ("April 1, 2025", "2025-04-01"),
            ("1 April 2025", "2025-04-01"),
            ("01/04/2025", "2025-04-01"),
            ("01-04-2025", "2025-04-01"),
            ("2025-04-01", "2025-04-01"),
            ("April 2025 to June 2025", "April 2025 to June 2025"),  # Should not parse as a single date
        ]
        
        for input_date, expected in test_cases:
            result = extractor.parse_date(input_date)
            self.assertEqual(result, expected, f"Failed to parse date: {input_date}")

    def test_price_extraction(self):
        """Test price extraction functionality"""
        extractor = TariffExtractor("dummy.pdf")
        
        # Test various price formats
        test_cases = [
            # Format: (input_price, expected_output)
            ("₹5000", 5000),
            ("Rs. 5,000", 5000),
            ("INR 5000/-", 5000),
            ("5000+GST", 5000),
            ("5,000.00", 5000),
            ("Rs.5000 per night", 5000),
            ("N/A", 0),
            ("", 0),
        ]
        
        for input_price, expected in test_cases:
            result = extractor.extract_price(input_price)
            self.assertEqual(result, expected, f"Failed to extract price: {input_price}")

    def test_meal_plan_normalization(self):
        """Test meal plan normalization"""
        extractor = TariffExtractor("dummy.pdf")
        
        # Test various meal plan formats
        test_cases = [
            # Format: (input_meal_plan, expected_output)
            ("EP", "ep"),
            ("European Plan", "ep"),
            ("Room Only", "ep"),
            ("CP", "cp"),
            ("Continental Plan", "cp"),
            ("Bed & Breakfast", "cp"),
            ("MAP", "map"),
            ("Modified American Plan", "map"),
            ("Half Board", "map"),
            ("Breakfast and Dinner", "map"),
            ("AP", "ap"),
            ("American Plan", "ap"),
            ("Full Board", "ap"),
            ("All Meals", "ap"),
            ("Unknown", "cp"),  # Default to CP
        ]
        
        for input_plan, expected in test_cases:
            result = extractor.normalize_meal_plan(input_plan)
            self.assertEqual(result, expected, f"Failed to normalize meal plan: {input_plan}")

    def test_date_range_extraction(self):
        """Test date range extraction from text"""
        extractor = TariffExtractor("dummy.pdf")
        
        # Test various date range formats in text
        test_cases = [
            # Format: (input_text, expected_output_count, expected_first_range)
            (
                "Valid from 1st April 2025 to 9th June 2025", 
                1, 
                ("2025-04-01", "2025-06-09")
            ),
            (
                "Season 1: April 1 - June 9, 2025; Season 2: June 10 - September 20, 2025", 
                2, 
                ("2025-04-01", "2025-06-09")
            ),
            (
                "Rates applicable from 01/04/2025 till 31/03/2026", 
                1, 
                ("2025-04-01", "2026-03-31")
            ),
        ]
        
        for input_text, expected_count, expected_first_range in test_cases:
            result = extractor.extract_date_ranges(input_text)
            self.assertEqual(len(result), expected_count, f"Wrong number of date ranges extracted from: {input_text}")
            if expected_count > 0:
                self.assertEqual(result[0], expected_first_range, f"First date range doesn't match for: {input_text}")

    def test_rack_rate_filtering(self):
        """Test filtering of rack rates"""
        extractor = TariffExtractor("dummy.pdf")
        
        # Create a test table with both rack and net rates
        test_table = [
            ["Room Type", "Rack Rate", "Net Rate"],
            ["Deluxe", "10000", "5000"],
            ["Super Deluxe", "15000", "7500"],
        ]
        
        # Process the table
        processed_tables = extractor.preprocess_tables([test_table])
        
        # Verify that only net rate column is kept
        self.assertEqual(len(processed_tables), 1, "Should have one processed table")
        self.assertEqual(len(processed_tables[0][0]), 2, "Processed table should have 2 columns (Room Type and Net Rate)")
        self.assertEqual(processed_tables[0][0][0], "Room Type", "First column should be Room Type")
        self.assertEqual(processed_tables[0][0][1], "Net Rate", "Second column should be Net Rate")

    def test_dew_drops_format_handler(self):
        """Test the Dew Drops format handler"""
        # This test requires a sample Dew Drops PDF
        dew_drops_pdf = os.path.join(TEST_DATA_DIR, 'dew_drops_sample.pdf')
        
        # Skip if the test file doesn't exist
        if not os.path.exists(dew_drops_pdf):
            self.skipTest(f"Test file not found: {dew_drops_pdf}")
            return
        
        extractor = TariffExtractor(dew_drops_pdf)
        
        # Extract text and tables
        text, tables = extractor.extract_text_and_tables()
        
        # Test the Dew Drops format handler
        results = extractor.extract_dew_drops_format(tables, text)
        
        # Verify results
        self.assertGreater(len(results), 0, "Should extract at least one result")
        
        # Check for expected room types
        room_types = set(entry["roomType"] for entry in results)
        expected_room_types = {"Plantation View", "Farm View", "Cottage"}
        self.assertTrue(any(rt in room_types for rt in expected_room_types), 
                       f"Expected at least one of {expected_room_types} in {room_types}")
        
        # Check for expected meal plans
        meal_plans = set(entry["mealPlanType"] for entry in results)
        self.assertTrue({"cp", "map"}.issubset(meal_plans), 
                       f"Expected both 'cp' and 'map' in {meal_plans}")
        
        # Check for extra charges
        self.assertTrue(any("extraAdultCharge" in entry for entry in results),
                       "Expected extraAdultCharge in at least one entry")

    def test_integration_with_sample_pdfs(self):
        """Integration test with sample PDFs"""
        # Get all PDF files in the test data directory
        pdf_files = [f for f in os.listdir(TEST_DATA_DIR) if f.endswith('.pdf')]
        
        for pdf_file in pdf_files:
            pdf_path = os.path.join(TEST_DATA_DIR, pdf_file)
            golden_path = os.path.join(GOLDEN_DATA_DIR, f"{os.path.splitext(pdf_file)[0]}.json")
            
            # Skip if golden data doesn't exist
            if not os.path.exists(golden_path):
                continue
            
            # Extract data from PDF
            extractor = TariffExtractor(pdf_path)
            results = extractor.extract()
            
            # Load golden data
            with open(golden_path, 'r') as f:
                golden_data = json.load(f)
            
            # Compare results with golden data
            self.assertEqual(len(results), len(golden_data), 
                           f"Number of entries doesn't match for {pdf_file}")
            
            # Check key fields in each entry
            for i, (result, golden) in enumerate(zip(results, golden_data)):
                for key in ['roomType', 'mealPlanType', 'roomPrice']:
                    self.assertEqual(result.get(key), golden.get(key), 
                                   f"Field {key} doesn't match in entry {i} for {pdf_file}")

    def test_validation_rules(self):
        """Test validation rules for extracted data"""
        # Create sample extracted data with some issues
        sample_data = [
            {
                "roomType": "Deluxe",
                "mealPlanType": "cp",
                "startDate": "2025-04-01",
                "endDate": "2025-03-31",  # End date before start date
                "roomPrice": 5000
            },
            {
                "roomType": "Super Deluxe",
                "mealPlanType": "cp",
                "startDate": "2025-04-01",
                "endDate": "2025-06-30",
                "roomPrice": -1000  # Negative price
            },
            {
                "roomType": "Suite",
                "mealPlanType": "cp",
                "startDate": "2025-04-01",
                "endDate": "2025-06-30",
                "roomPrice": 500000  # Outlier price
            }
        ]
        
        # Validate the data
        validation_results = validate_extracted_data(sample_data)
        
        # Check validation results
        self.assertEqual(len(validation_results), 3, "Should have 3 validation issues")
        self.assertTrue(any("date" in issue.lower() for issue in validation_results), 
                       "Should detect date range issue")
        self.assertTrue(any("negative" in issue.lower() for issue in validation_results), 
                       "Should detect negative price issue")
        self.assertTrue(any("outlier" in issue.lower() for issue in validation_results), 
                       "Should detect outlier price issue")


def validate_extracted_data(data: List[Dict[str, Any]]) -> List[str]:
    """
    Validate extracted data against business rules
    
    Args:
        data: List of extracted tariff entries
        
    Returns:
        List of validation issues found
    """
    issues = []
    
    # Skip validation if no data
    if not data:
        return ["No data to validate"]
    
    # Calculate price statistics for outlier detection
    prices = [entry.get("roomPrice", 0) for entry in data if entry.get("roomPrice", 0) > 0]
    if prices:
        avg_price = sum(prices) / len(prices)
        max_price = max(prices)
        min_price = min(prices)
    else:
        avg_price = 0
        max_price = 0
        min_price = 0
    
    # Validate each entry
    for i, entry in enumerate(data):
        # Check required fields
        for field in ["roomType", "mealPlanType", "roomPrice"]:
            if field not in entry or not entry[field]:
                issues.append(f"Entry {i}: Missing required field '{field}'")
        
        # Check date ranges
        if "startDate" in entry and "endDate" in entry:
            try:
                start_date = datetime.strptime(entry["startDate"], "%Y-%m-%d")
                end_date = datetime.strptime(entry["endDate"], "%Y-%m-%d")
                if end_date < start_date:
                    issues.append(f"Entry {i}: End date {entry['endDate']} is before start date {entry['startDate']}")
            except (ValueError, TypeError):
                issues.append(f"Entry {i}: Invalid date format")
        
        # Check price validity
        price = entry.get("roomPrice", 0)
        if price < 0:
            issues.append(f"Entry {i}: Negative price {price}")
        elif price > 0 and avg_price > 0:
            # Check for outliers (more than 5x average or less than 1/5 of average)
            if price > avg_price * 5:
                issues.append(f"Entry {i}: Outlier high price {price} (avg: {avg_price})")
            elif price < avg_price / 5:
                issues.append(f"Entry {i}: Outlier low price {price} (avg: {avg_price})")
    
    # Check for consistency across entries
    room_types = set(entry.get("roomType", "") for entry in data)
    meal_plans = set(entry.get("mealPlanType", "") for entry in data)
    
    # Check expected meal plan combinations
    if "cp" in meal_plans and "map" not in meal_plans and any("map" in entry.get("roomType", "").lower() for entry in data):
        issues.append("Missing MAP meal plan when room types suggest it should be available")
    
    return issues


if __name__ == "__main__":
    unittest.main()

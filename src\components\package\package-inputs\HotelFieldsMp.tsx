import { handleDate } from "@/components/page-components/hotel-details/room/mealPlan/handlingDates"

interface HotelFieldsMpProps {
    price: number
    mealPlan: string
    startDates: string[]
    endDates: string[]
}

// Helper function to display meal plan names with descriptions
function getMealPlanDisplay(mealPlan: string): string {
  // Ensure we have a valid string to work with
  if (!mealPlan) return '';

  // Normalize the input to lowercase for consistent lookup
  const normalizedMealPlan = mealPlan.toLowerCase();

  const mealPlanMap: Record<string, string> = {
    // Define only lowercase keys for consistent lookup
    'cp': 'CP (Continental Plan - Breakfast Only)',
    'map': 'MAP (Modified American Plan - Breakfast & Dinner)',
    'ap': 'AP (American Plan - All Meals)',
    'ep': 'EP (European Plan - Room Only)',
    'ai': 'AI (All Inclusive)',
    'bb': 'BB (Bed & Breakfast)'
  };

  // Return the display name or uppercase the original if not found
  return mealPlanMap[normalizedMealPlan] || mealPlan.toUpperCase();
}

export default function HotelFieldsMp({price, mealPlan, startDates, endDates}: HotelFieldsMpProps) {
  // Extract just the meal plan name without the ID
  const mealPlanName = mealPlan.split("**")[0];

  return (
    <div className="grid grid-cols-1 gap-3">
      <div className="flex items-center">
        <h4 className="text-sm font-semibold text-gray-700 mr-2">Selected Meal Plan:</h4>
        <span className="text-sm bg-blue-50 text-blue-700 px-2 py-1 rounded">
          {getMealPlanDisplay(mealPlanName)}
        </span>
      </div>

      <div className="flex items-center">
        <span className="text-sm font-semibold text-gray-700 mr-2">Price:</span>
        <span className="text-sm font-medium text-green-600">₹{price.toLocaleString()}</span>
      </div>

      {startDates.length > 0 && (
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h5 className="text-xs font-semibold text-gray-700 mb-1">Available Start Dates:</h5>
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {startDates.map((date, i) => (
                <div key={i} className="text-xs bg-gray-100 px-2 py-1 rounded">
                  {handleDate(date)}
                </div>
              ))}
            </div>
          </div>

          <div>
            <h5 className="text-xs font-semibold text-gray-700 mb-1">Available End Dates:</h5>
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {endDates.map((date, i) => (
                <div key={i} className="text-xs bg-gray-100 px-2 py-1 rounded">
                  {handleDate(date)}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

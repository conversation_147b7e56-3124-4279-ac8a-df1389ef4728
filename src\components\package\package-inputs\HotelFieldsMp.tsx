import { handleDate } from "@/components/page-components/hotel-details/room/mealPlan/handlingDates"

interface HotelFieldsMpProps {
    price:number
    mealPlan:string
    startDates:string[]
    endDates:string[]
}
export default function HotelFieldsMp({price,mealPlan,startDates,endDates}:HotelFieldsMpProps) {

  return (
    <>
    <div className="flex">
            <div className="font-bold">Price : </div>
            <div className="">{price}</div>
            </div>
            <div className="text-xs">
            <div className="font-bold">StartDates : </div>
            <div className="">{mealPlan?startDates.map((k:string,i:number)=><div key={i}>{handleDate(k)}</div>):<></>}</div>
            </div>
  
            <div className="text-xs">
            <div className="font-bold">EndDates : </div>
            <div className="">{mealPlan?endDates.map((k:string,i:number)=><div key={i}>{handleDate(k)}</div>):<></>}</div>
            </div>
    </>
  )
}

export const months = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","oct","Nov","Dec"]

export const differenceInDays = (date1: Date, date2: Date): number => {
    const diffTime = (date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };
  
  export const isWithin10Days = (endDate: string): boolean => {
    const today = new Date();
    const end = new Date(endDate);
    return differenceInDays(today, end) <= 10 && differenceInDays(today, end) >= 0;
  };
  
  export const isEndDatePast = (endDate: string): boolean => {
    const today = new Date();
    const end = new Date(endDate);
    return end < today;
  };

  export function handleDate(date:string){
    const d = date.split('-');
    return `${d[2]}-${months[Number(d[1])-1]}-${d[0]}`
}
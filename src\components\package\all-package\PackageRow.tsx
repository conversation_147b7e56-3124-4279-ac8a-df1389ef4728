/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  handleDate,
  isEndDatePast,
  isWithin10Days,
} from "@/components/page-components/hotel-details/room/mealPlan/handlingDates";
import { GoPencil } from "react-icons/go";
import { Link } from "react-router-dom";
export interface PackageRowProps {
  packageData: any;
}

export default function PackageRow(props: PackageRowProps) {
  return (
    <div className="flex border min-h-[50px] relative">
      <div className="m-1 flex w-full  ">
        <div className="w-1/2 flex">
          <div className="w-1/3 text-sm">{props.packageData?.packageName}</div>
          <div className="w-1/3 text-sm">
            {props.packageData?.destinationName}
          </div>
          <div className="w-1/2 flex">
            <div className="w-1/2 text-center">
              {props.packageData?.planName}
            </div>
            <div className="w-1/2 text-center">
              {props.packageData?.interestName}
            </div>
          </div>
        </div>
        <div className="w-1/2 flex text-center">
          <div className="w-1/6 ">{props.packageData?.hotelCount}</div>
          <div className="w-1/6 ">{props.packageData?.availableHotelCount}</div>
          <div className="w-1/6 ">{props.packageData?.activityCount}</div>
          <div className="w-1/6 ">
            {props.packageData?.availableActivityCount}
          </div>
          <div className="w-2/6 text-xs text">
            {props.packageData.period?.map((k: any, i: any) => (
              <div key={i} className="flex gap-5 justify-center">
              <div
                className={
                  isWithin10Days(k.startDate)
                    ? "text-yellow-600"
                    : isEndDatePast(k.startDate)
                    ? " text-red-600"
                    : ""
                }
              >
                {handleDate(k.startDate)}
              </div>
              <div
              className={
                isWithin10Days(k.endDate)
                  ? "text-yellow-600"
                  : isEndDatePast(k.endDate)
                  ? " text-red-600"
                  : ""
              }
            >
              {handleDate(k.endDate)}
            </div>
              </div>
             
            ))}
          </div>
        </div>
      </div>
      <Link
        to={`/package/${props.packageData.packageId}/edit/`}
        className="absolute right-[5px] top-[3px]"
      >
        <GoPencil />
      </Link>
    </div>
  );
}

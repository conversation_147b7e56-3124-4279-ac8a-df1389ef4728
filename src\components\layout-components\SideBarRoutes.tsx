import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'


import { Link, useLocation } from 'react-router-dom'

interface SideBarRoutesProps {
    label : string , 
    icon : LucideIcon,
    href : string
}

const SideBarRoutes = ({label , icon : Icon , href}:SideBarRoutesProps) => {
    const pathname = useLocation().pathname;
   
  return (
    <Link  to={href} className={cn("cursor-pointer  w-full h-full whitespace-nowrap px-8 flex  items-center cursor-pointer p-2 hover:bg-appprimary/10 hover:bg-opacity-20 rounded-md text-slate-600 hover:text-slate-600  gap-1",pathname === href && 'bg-white bg border-[0.5px] bg-appprimary text-white hover:bg-appprimary hover:text-white') }>
        
            <Icon  className=" "size={15}/>

         <div className="flex pl-4 justify-center items-center">
         <h1 className=' cursor-pointer'>
            {label}
            </h1>
            
         </div>
       
        
    </Link>
  )
}

export default SideBarRoutes
import { deleteHotel } from "@/utils/api-functions/hotel/delete-hotel"
import toast from "react-hot-toast"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
  } from "@/components/ui/alert-dialog"
import { useNavigate } from "react-router-dom"

  interface DeleteProps {
    hotelId : string
  }
const Delete = ({hotelId}:DeleteProps) => {
    const router = useNavigate();
    const handleDelete =async() =>{
        try {
            await deleteHotel(hotelId)
            toast.success('Hotel deleted successfully')
            router(0)
            
        } catch (error) {
            console.log('[HOTEL DELETEION ERROR]',error);
            toast.error('Error deleting Hotel')
            
        }

    }
  return (
    <AlertDialog>
        <AlertDialogTrigger onClick={(event) => {
    event.stopPropagation();
  }}>
   
        Delete
    
        </AlertDialogTrigger>
        <AlertDialogContent>
            <AlertDialogHeader>
                <AlertDialogTitle>Are you sure ?</AlertDialogTitle>
                <AlertDialogDescription>
                    This action can't be undone
                </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete}>Continue</AlertDialogAction>
        </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
  )
}

export default Delete
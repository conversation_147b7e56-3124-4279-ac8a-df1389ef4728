#!/usr/bin/env python3
"""
Generate test data for PDF extraction testing

This script generates synthetic test data for various hotel tariff formats
and creates golden files for validation testing.
"""

import os
import sys
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from validation.tariff_validator import TariffValidator

# Constants
TEST_DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_data')
GOLDEN_DATA_DIR = os.path.join(TEST_DATA_DIR, 'golden')

# Ensure directories exist
os.makedirs(TEST_DATA_DIR, exist_ok=True)
os.makedirs(GOLDEN_DATA_DIR, exist_ok=True)

# Room types for test data
ROOM_TYPES = [
    "Standard Room",
    "Deluxe Room",
    "Super Deluxe Room",
    "Executive Suite",
    "Premium Suite",
    "Cottage",
    "Villa"
]

# Meal plans for test data
MEAL_PLANS = ["ep", "cp", "map", "ap"]

# Date ranges for test data
DATE_RANGES = [
    ("2025-04-01", "2025-06-09"),
    ("2025-06-10", "2025-09-20"),
    ("2025-09-21", "2026-02-28"),
    ("2026-03-01", "2026-03-31")
]

def generate_standard_test_data() -> List[Dict[str, Any]]:
    """Generate standard test data with consistent pricing"""
    data = []
    
    # Base prices for room types
    base_prices = {
        "Standard Room": 4000,
        "Deluxe Room": 5000,
        "Super Deluxe Room": 6000,
        "Executive Suite": 8000,
        "Premium Suite": 10000,
        "Cottage": 7000,
        "Villa": 12000
    }
    
    # Meal plan multipliers
    meal_plan_multipliers = {
        "ep": 0.9,  # EP is 90% of CP
        "cp": 1.0,  # CP is base price
        "map": 1.2,  # MAP is 120% of CP
        "ap": 1.4   # AP is 140% of CP
    }
    
    # Season multipliers
    season_multipliers = {
        0: 1.0,  # Regular season
        1: 1.2,  # High season
        2: 0.8,  # Low season
        3: 1.0   # Regular season
    }
    
    # Extra charges
    extra_charges = {
        "ep": {"extraAdultCharge": 800, "extraChildWithBedCharge": 600, "extraChildWithoutBedCharge": 400},
        "cp": {"extraAdultCharge": 1000, "extraChildWithBedCharge": 800, "extraChildWithoutBedCharge": 500},
        "map": {"extraAdultCharge": 1500, "extraChildWithBedCharge": 1200, "extraChildWithoutBedCharge": 700},
        "ap": {"extraAdultCharge": 2000, "extraChildWithBedCharge": 1500, "extraChildWithoutBedCharge": 900}
    }
    
    # Generate entries for each room type, meal plan, and date range
    for room_type, base_price in base_prices.items():
        for meal_plan, meal_multiplier in meal_plan_multipliers.items():
            for i, (start_date, end_date) in enumerate(DATE_RANGES):
                # Calculate price with multipliers
                price = int(base_price * meal_multiplier * season_multipliers[i])
                
                # Create entry
                entry = {
                    "roomType": room_type,
                    "mealPlanType": meal_plan,
                    "startDate": start_date,
                    "endDate": end_date,
                    "roomPrice": price,
                    **extra_charges[meal_plan]
                }
                
                data.append(entry)
    
    return data

def generate_dew_drops_test_data() -> List[Dict[str, Any]]:
    """Generate test data for Dew Drops Farm Resorts Munnar"""
    data = []
    
    # Room types and base prices
    room_prices = {
        "Plantation View": 4000,
        "Farm View": 4500,
        "Cottage": 5000
    }
    
    # MAP supplement per person
    map_supplement = 500
    
    # Generate entries for each room type, meal plan, and date range
    for room_type, base_price in room_prices.items():
        for start_date, end_date in DATE_RANGES:
            # CP entry
            cp_entry = {
                "roomType": room_type,
                "mealPlanType": "cp",
                "startDate": start_date,
                "endDate": end_date,
                "roomPrice": base_price,
                "extraAdultCharge": 800,
                "extraChildWithBedCharge": 600,
                "extraChildWithoutBedCharge": 400
            }
            
            # MAP entry
            map_entry = {
                "roomType": room_type,
                "mealPlanType": "map",
                "startDate": start_date,
                "endDate": end_date,
                "roomPrice": base_price + (map_supplement * 2),
                "extraAdultCharge": 800 + map_supplement,
                "extraChildWithBedCharge": 600 + map_supplement,
                "extraChildWithoutBedCharge": 400
            }
            
            data.append(cp_entry)
            data.append(map_entry)
    
    return data

def generate_problematic_test_data() -> List[Dict[str, Any]]:
    """Generate test data with various issues for validation testing"""
    # Start with standard data
    data = generate_standard_test_data()
    
    # Introduce various issues
    
    # 1. Missing required fields
    data.append({
        "roomType": "Problem Room",
        "startDate": "2025-04-01",
        "endDate": "2025-06-09"
        # Missing mealPlanType and roomPrice
    })
    
    # 2. Invalid date range (end before start)
    data.append({
        "roomType": "Problem Room",
        "mealPlanType": "cp",
        "startDate": "2025-06-09",
        "endDate": "2025-04-01",  # End before start
        "roomPrice": 5000
    })
    
    # 3. Price outliers
    data.append({
        "roomType": "Standard Room",
        "mealPlanType": "cp",
        "startDate": "2025-04-01",
        "endDate": "2025-06-09",
        "roomPrice": 20000  # Much higher than other Standard Room prices
    })
    
    data.append({
        "roomType": "Deluxe Room",
        "mealPlanType": "cp",
        "startDate": "2025-04-01",
        "endDate": "2025-06-09",
        "roomPrice": 1000  # Much lower than other Deluxe Room prices
    })
    
    # 4. Inconsistent meal plan pricing
    data.append({
        "roomType": "Executive Suite",
        "mealPlanType": "map",
        "startDate": "2025-04-01",
        "endDate": "2025-06-09",
        "roomPrice": 8100  # Only slightly more than CP (should be 20% more)
    })
    
    # 5. Inconsistent room type hierarchy
    data.append({
        "roomType": "Super Deluxe Room",
        "mealPlanType": "cp",
        "startDate": "2025-09-21",
        "endDate": "2026-02-28",
        "roomPrice": 4500  # Lower than Deluxe Room in same period
    })
    
    # 6. Unusual room type name
    data.append({
        "roomType": "XYZ Special",  # Unusual name
        "mealPlanType": "cp",
        "startDate": "2025-04-01",
        "endDate": "2025-06-09",
        "roomPrice": 6000
    })
    
    # 7. Date range gap
    # Remove one date range for a room type to create a gap
    data = [entry for entry in data 
            if not (entry.get("roomType") == "Cottage" and 
                   entry.get("startDate") == "2025-06-10" and
                   entry.get("endDate") == "2025-09-20")]
    
    # 8. Date range overlap
    data.append({
        "roomType": "Villa",
        "mealPlanType": "cp",
        "startDate": "2025-05-01",  # Overlaps with existing range
        "endDate": "2025-07-15",    # Overlaps with existing range
        "roomPrice": 11000
    })
    
    return data

def validate_and_save_test_data():
    """Generate, validate, and save test data sets"""
    # Generate different test data sets
    test_sets = {
        "standard": generate_standard_test_data(),
        "dew_drops": generate_dew_drops_test_data(),
        "problematic": generate_problematic_test_data()
    }
    
    # Validate and save each test set
    for name, data in test_sets.items():
        # Save the test data
        output_path = os.path.join(GOLDEN_DATA_DIR, f"{name}.json")
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"Generated {len(data)} entries for {name} test set")
        
        # Validate the test data
        validator = TariffValidator(hotel_name=name if name == "dew_drops" else None)
        issues = validator.validate(data)
        
        # Save validation results
        validation_path = os.path.join(GOLDEN_DATA_DIR, f"{name}_validation.json")
        with open(validation_path, 'w') as f:
            json.dump(issues, f, indent=2)
        
        print(f"Found {len(issues)} validation issues for {name} test set")
        
        # Print summary of issues by severity
        severity_counts = {}
        for issue in issues:
            severity = issue.get("severity", "unknown")
            if severity not in severity_counts:
                severity_counts[severity] = 0
            severity_counts[severity] += 1
        
        for severity, count in severity_counts.items():
            print(f"  {severity}: {count}")

if __name__ == "__main__":
    validate_and_save_test_data()

"use client"
import * as z from 'zod'

import {zodResolver} from '@hookform/resolvers/zod'
import {useForm} from 'react-hook-form'
// import api from '../../../utils/api-functions/auth'

import {
    Form,FormControl,FormField,FormItem
} from '@/components/ui/form'

import { Input } from '@/components/ui/input'

import { Button } from '@/components/ui/button'

import toast from 'react-hot-toast'



import { HotelsType } from '@/types/types'
import {  HotelSchema } from '@/types/hotelSchema'
// import { HOTEL_URL, SERVER_URL } from '@/utils/urls/urls'


interface NameProps {
    hotelData : HotelsType
   
    hotelId : string
}

const NameAddressForm = ({hotelData} : NameProps) => {

 
    const form = useForm<z.infer<typeof HotelSchema>>({
        resolver:zodResolver(HotelSchema),
        defaultValues:{
            hotelName:"",
            
        }
    })
    
const {isSubmitting  } = form.formState;



    const onSubmit =  async(values:z.infer<typeof HotelSchema>) =>{
        try {
            
          
            console.log(values)
          
          
            
          
        } catch(error)  {
            console.log('Error from Component',error)
            toast.error('Something went wrong')
            
        }
       
    }
 

   
    return ( 
       <>
       
       <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 mt-4'>
                <FormField control={form.control} name="hotelName" render={({field})=>
            <FormItem>
                <FormControl>
                    <Input disabled={isSubmitting}
            
                    placeholder={`e.g ${hotelData.hotelName}'`}
                    {...field}/>
                </FormControl>
            </FormItem>}/>
            <div className='flex items-center gap-x-2 '>
                <Button 
                disabled={ isSubmitting} type='submit'>
                    Save

                </Button>
            </div>

            </form>
            </Form>
       </>
     );
}
 
export default NameAddressForm;
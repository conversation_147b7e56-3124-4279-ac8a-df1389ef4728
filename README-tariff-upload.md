# Tariff Upload Feature

This feature allows hotel administrators to upload tariff PDFs, extract pricing data, and update the database after review and approval.

## Overview

The Tariff Upload feature consists of:

1. A frontend UI for uploading, reviewing, and approving tariff PDFs
2. A backend API for processing PDFs and extracting data
3. A Python script for extracting tables from PDFs

## Setup Instructions

### Frontend Setup

The frontend components are already integrated into the Tripmilestone Admin Frontend. No additional setup is required.

### Backend Setup

1. Install Node.js dependencies:
   ```bash
   npm install
   ```

2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create a `temp` directory for storing downloaded PDFs:
   ```bash
   mkdir temp
   ```

4. Start the server:
   ```bash
   node server.js
   ```

## Usage

1. Navigate to the Hotel Edit page
2. Click on the "Rooms & Pricing" tab
3. Click on the "Tariff Upload" tab
4. Select a room and upload a tariff PDF
5. Review the extracted data in the comparison view
6. Approve or reject the tariff

## Supported PDF Formats

The system works best with digital PDFs (not scanned images) that contain tables with the following information:

- Room type
- Meal plan (EP, CP, MAP, AP)
- Date ranges (start and end dates)
- Prices

The extraction script attempts to identify these columns automatically, but well-structured tables yield the best results.

### OCR Fallback

For scanned PDFs (image-based), the system includes an OCR fallback mechanism that attempts to extract data using Tesseract OCR. This is less accurate than table extraction but provides a way to handle scanned documents.

To use OCR, you need to install additional dependencies:

```bash
pip install pytesseract pdf2image pillow
```

You'll also need to install Tesseract OCR on your system:
- Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
- Linux: `sudo apt-get install tesseract-ocr`
- macOS: `brew install tesseract`

## Technical Details

### PDF Extraction

The `extract_pdf.py` script uses `pdfplumber` to extract tables from PDFs. It:

1. Identifies tables in the PDF
2. Attempts to determine which columns contain room types, meal plans, dates, and prices
3. Normalizes meal plan types to standard formats (ep, cp, map, ap)
4. Parses dates into a standard format (YYYY-MM-DD)
5. Extracts numeric prices

### API Endpoints

- `POST /api/admin/hotel/tariff` - Create a new tariff upload
- `POST /api/admin/hotel/tariff/extract` - Extract data from a tariff PDF
- `PUT /api/admin/hotel/tariff/:tariffId` - Update tariff status (approve/reject)
- `DELETE /api/admin/hotel/tariff/:tariffId` - Delete a tariff upload
- `GET /api/admin/hotel/:hotelId/tariffs` - Get all tariffs for a hotel

## Troubleshooting

If extraction fails:

1. Ensure the PDF contains selectable text (not a scanned image)
2. Check that the PDF has well-structured tables
3. Verify that the tables include room type, meal plan, date ranges, and prices

## Comparison UI Features

The tariff comparison UI includes several features to help administrators review and approve extracted data:

### Filtering Options

- **All**: Show all extracted prices
- **Price Changes**: Show only prices that differ from existing prices
- **New Prices**: Show only prices for date ranges that don't exist in the database

### Bulk Selection

- **Select All**: Select all prices for a specific meal plan
- **Deselect All**: Deselect all prices for a specific meal plan

### Summary Statistics

The comparison UI includes a summary section showing:
- Total number of extracted prices
- Number of prices selected for update
- Number of price changes (where the new price differs from the existing price)

## Future Improvements

- Improve OCR accuracy for scanned PDFs
- Enhance table detection for complex layouts
- Add support for more date formats
- Implement AI-based extraction for non-tabular formats
- Add support for extracting room types from PDF content

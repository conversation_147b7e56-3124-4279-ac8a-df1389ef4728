import React, { useMemo, useState } from 'react';
import { HotelRoom, MealPlan } from '@/types/types';
import { AlertTriangle, Info, X } from 'lucide-react';
import { format, addDays, startOfDay } from 'date-fns';
import DailyPriceCalendar from './DailyPriceCalendar';

interface HotelPriceIssuesListProps {
  hotelId: string;
  rooms: HotelRoom[];
}

interface Issue {
  roomType: string;
  roomId: string;
  mealPlanType: string;
  issueType: 'Conflict' | 'Missing';
  details: string;
  focusDate?: string; // The date to focus in calendar
}

function getFinancialYearRange(today: Date) {
  // If today is after April 1, current year is this year, else previous year
  const year = today.getMonth() >= 3 ? today.getFullYear() : today.getFullYear() - 1;
  const fyStart = new Date(year, 3, 1); // April 1
  const fyEnd = new Date(year + 1, 2, 31); // March 31 next year
  return { fyStart, fyEnd };
}

function getNextFinancialYearRange(today: Date) {
  // Next FY: April 1 next year to March 31 year after
  const year = today.getMonth() >= 3 ? today.getFullYear() + 1 : today.getFullYear();
  const fyStart = new Date(year, 3, 1); // April 1
  const fyEnd = new Date(year + 1, 2, 31); // March 31 next year
  return { fyStart, fyEnd };
}

function detectIssuesForRoom(room: HotelRoom): Issue[] {
  if (!room.mealPlan || room.mealPlan.length === 0) {
    return [{
      roomType: room.hotelRoomType,
      roomId: room.hotelRoomId,
      mealPlanType: '-',
      issueType: 'Missing',
      details: 'No meal plans configured',
    }];
  }
  const issues: Issue[] = [];
  const today = startOfDay(new Date());
  // Group meal plans by type
  const mealPlanGroups: { [type: string]: MealPlan[] } = {};
  room.mealPlan.forEach(mp => {
    const type = mp.mealPlan.toUpperCase();
    if (!mealPlanGroups[type]) mealPlanGroups[type] = [];
    mealPlanGroups[type].push(mp);
  });
  Object.entries(mealPlanGroups).forEach(([type, plans]) => {
    // Collect all prices by date
    const datesPrices: { [date: string]: number[] } = {};
    let hasAnyData = false;
    plans.forEach(plan => {
      if (!plan.startDate || !plan.endDate || plan.startDate.length === 0 || plan.endDate.length === 0) {
        issues.push({
          roomType: room.hotelRoomType,
          roomId: room.hotelRoomId,
          mealPlanType: type,
          issueType: 'Missing',
          details: 'Missing date range in meal plan',
        });
        return;
      }
      for (let i = 0; i < plan.startDate.length; i++) {
        if (!plan.startDate[i] || !plan.endDate[i]) continue;
        const start = new Date(plan.startDate[i]);
        const end = new Date(plan.endDate[i]);
        if (isNaN(start.getTime()) || isNaN(end.getTime())) continue;
        hasAnyData = true;
        const currentDay = new Date(start);
        while (currentDay <= end) {
          const dateStr = format(currentDay, 'yyyy-MM-dd');
          if (!datesPrices[dateStr]) datesPrices[dateStr] = [];
          datesPrices[dateStr].push(plan.roomPrice);
          currentDay.setDate(currentDay.getDate() + 1);
        }
      }
    });
    // If no valid date ranges at all
    if (!hasAnyData) {
      issues.push({
        roomType: room.hotelRoomType,
        roomId: room.hotelRoomId,
        mealPlanType: type,
        issueType: 'Missing',
        details: 'No valid date ranges for meal plan',
      });
      return;
    }
    // --- Conflict detection ---
    // Find the first date with a conflict
    let conflictStart: string | null = null;
    let conflictEnd: string | null = null;
    let pricesUnion: number[] = [];
    const sortedDates = Object.keys(datesPrices).sort();
    for (const dateStr of sortedDates) {
      const prices = datesPrices[dateStr];
      const uniquePrices = Array.from(new Set(prices));
      if (uniquePrices.length > 1) {
        if (!conflictStart) conflictStart = dateStr;
        conflictEnd = dateStr;
        pricesUnion = Array.from(new Set([...pricesUnion, ...uniquePrices]));
      }
    }
    if (conflictStart && conflictEnd) {
      issues.push({
        roomType: room.hotelRoomType,
        roomId: room.hotelRoomId,
        mealPlanType: type,
        issueType: 'Conflict',
        details: `Conflict starts on ${conflictStart} (${conflictStart} to ${conflictEnd}). Prices: ${pricesUnion.join(' / ')}`,
        focusDate: conflictStart,
      });
    }
    // --- Missing price detection for current and next FY ---
    // Build a set of all dates with price
    const priceDatesSet = new Set(Object.keys(datesPrices));
    // Check for current FY
    const { fyStart, fyEnd } = getFinancialYearRange(today);
    let missingStart: string | null = null;
    let foundMissing = false;
    let foundNextMissing = false;
    for (let d = new Date(fyStart); d <= fyEnd; d = addDays(d, 1)) {
      const dateStr = format(d, 'yyyy-MM-dd');
      if (!priceDatesSet.has(dateStr)) {
        missingStart = dateStr;
        foundMissing = true;
        break;
      }
    }
    if (foundMissing && missingStart) {
      issues.push({
        roomType: room.hotelRoomType,
        roomId: room.hotelRoomId,
        mealPlanType: type,
        issueType: 'Missing',
        details: `Missing price for ${missingStart} onwards (Tariff not set for full financial year)`,
        focusDate: missingStart,
      });
    }
    // If today is after Jan 1, check for next FY
    if (today.getMonth() >= 0) {
      const { fyStart: nextFyStart, fyEnd: nextFyEnd } = getNextFinancialYearRange(today);
      let missingNextStart: string | null = null;
      for (let d = new Date(nextFyStart); d <= nextFyEnd; d = addDays(d, 1)) {
        const dateStr = format(d, 'yyyy-MM-dd');
        if (!priceDatesSet.has(dateStr)) {
          missingNextStart = dateStr;
          foundNextMissing = true;
          break;
        }
      }
      if (foundNextMissing && missingNextStart) {
        issues.push({
          roomType: room.hotelRoomType,
          roomId: room.hotelRoomId,
          mealPlanType: type,
          issueType: 'Missing',
          details: `Missing price for next FY: ${missingNextStart} onwards (Tariff not set for next financial year)`,
          focusDate: missingNextStart,
        });
      }
    }
    // If no price data at all for the FY
    if (!foundMissing && !foundNextMissing && Object.keys(datesPrices).length === 0) {
      issues.push({
        roomType: room.hotelRoomType,
        roomId: room.hotelRoomId,
        mealPlanType: type,
        issueType: 'Missing',
        details: 'No price data for any date',
      });
    }
  });
  return issues;
}

const FILTERS = ['All', 'Conflict', 'Missing'] as const;
type FilterType = typeof FILTERS[number];

const HotelPriceIssuesList: React.FC<HotelPriceIssuesListProps> = ({ hotelId, rooms }) => {
  const [filter, setFilter] = useState<FilterType>('All');
  const [modal, setModal] = useState<null | { roomId: string; mealPlanType: string; date: string }>(null);

  const issues = useMemo(() => {
    if (!rooms) return [];
    return rooms.flatMap(room => detectIssuesForRoom(room));
  }, [rooms]);

  const filteredIssues = useMemo(() => {
    if (filter === 'All') return issues;
    return issues.filter(issue => issue.issueType === filter);
  }, [issues, filter]);

  // Find room and meal plan for modal
  const modalRoom = modal ? rooms.find(r => r.hotelRoomId === modal.roomId) : null;

  if (!rooms || rooms.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-5 my-6 flex items-start gap-3 shadow-sm">
        <Info className="text-yellow-500 mt-1 h-6 w-6 flex-shrink-0" />
        <div>
          <h3 className="text-base font-semibold text-yellow-900">No rooms available</h3>
          <p className="text-sm text-yellow-800 mt-1">
            Please add rooms and meal plans to view price issues.
          </p>
        </div>
      </div>
    );
  }

  if (issues.length === 0) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-5 my-6 flex items-center gap-3 shadow-sm">
        <Info className="text-green-500 h-6 w-6 flex-shrink-0" />
        <span className="text-green-800 font-medium">No price conflicts or missing prices detected for this hotel.</span>
      </div>
    );
  }

  return (
    <div className="mt-6">
      <h3 className="text-lg font-bold text-amber-700 mb-4 flex items-center gap-2">
        <AlertTriangle className="h-5 w-5 text-amber-500" />
        Price Conflicts / Missing Prices
      </h3>
      {/* Filter Dropdown */}
      <div className="mb-4 flex items-center gap-2">
        <label className="font-medium text-amber-700 text-sm">Filter:</label>
        <select
          className="border border-amber-200 rounded px-2 py-1 text-sm"
          value={filter}
          onChange={e => setFilter(e.target.value as FilterType)}
        >
          {FILTERS.map(f => (
            <option key={f} value={f}>{f}</option>
          ))}
        </select>
      </div>
      <div className="overflow-x-auto rounded-lg border border-amber-200 shadow-sm">
        <table className="min-w-full divide-y divide-amber-200">
          <thead className="bg-amber-50">
            <tr>
              <th className="px-4 py-2 text-left text-xs font-semibold text-amber-700">Room Type</th>
              <th className="px-4 py-2 text-left text-xs font-semibold text-amber-700">Meal Plan</th>
              <th className="px-4 py-2 text-left text-xs font-semibold text-amber-700">Issue</th>
              <th className="px-4 py-2 text-left text-xs font-semibold text-amber-700">Details</th>
              <th className="px-4 py-2 text-left text-xs font-semibold text-amber-700">Action</th>
            </tr>
          </thead>
          <tbody>
            {filteredIssues.map((issue, idx) => (
              <tr key={idx} className="bg-white hover:bg-amber-50 transition-colors">
                <td className="px-4 py-2 text-sm text-amber-900 font-medium">{issue.roomType}</td>
                <td className="px-4 py-2 text-sm text-amber-900">{issue.mealPlanType}</td>
                <td className="px-4 py-2 text-sm font-semibold">
                  {issue.issueType === 'Conflict' ? (
                    <span className="text-amber-700 flex items-center gap-1"><AlertTriangle className="h-4 w-4" /> Conflict</span>
                  ) : (
                    <span className="text-red-600 flex items-center gap-1"><Info className="h-4 w-4" /> Missing</span>
                  )}
                </td>
                <td className="px-4 py-2 text-sm text-amber-800">{issue.details}</td>
                <td className="px-4 py-2 text-sm">
                  {issue.focusDate ? (
                    <button
                      className="text-blue-600 hover:underline"
                      title="Go to Price Calendar"
                      onClick={() => setModal({ roomId: issue.roomId, mealPlanType: issue.mealPlanType, date: issue.focusDate! })}
                    >
                      View Calendar
                    </button>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Calendar Modal */}
      {modal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded-2xl shadow-2xl p-0 w-full max-w-4xl min-w-[700px] relative animate-fade-in max-h-[90vh] overflow-y-auto sm:p-6 p-2">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 text-2xl font-bold z-10"
              onClick={() => setModal(null)}
              aria-label="Close"
            >
              <X />
            </button>
            <h3 className="text-lg font-bold text-blue-900 mb-4">Price Calendar for {modalRoom?.hotelRoomType} ({modal.mealPlanType})</h3>
            {/* Wrap in horizontally scrollable div for small screens */}
            <div style={{ overflowX: 'auto' }}>
              <DailyPriceCalendar hotelId={hotelId} initialDate={modal.date} initialRoomId={modal.roomId} initialMealPlanType={modal.mealPlanType} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HotelPriceIssuesList; 
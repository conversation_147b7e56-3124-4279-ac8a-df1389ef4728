#!/usr/bin/env python3
"""
Simple PDF extraction tester

Usage:
    python test_pdf.py <hotel_name>

This script will test PDF extraction on a few predefined URLs.
"""

import sys
import os
import json
import logging
import requests
import urllib.parse
from extract_pdf_improved import extract_from_tables

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('extraction_test.log')
    ]
)
logger = logging.getLogger('pdf_test')

# Predefined PDF URLs for testing
PDF_URLS = {
    "gokulam": "https://tripemilestone.in-maa-1.linodeobjects.com/hotel-tariffs/d7ec381c-99ff-473a-8aec-3950a445fb07-b9a39ec7-5904-4d84-97b0-fad7c593f6ac-4f308964-3b7b-4687-ab4b-85bb9ca88e0f-1747455277858-1.Gokulam%20Park%20-Munnar-%20May%202025.pdf",
    "dewdrop": "https://tripemilestone.in-maa-1.linodeobjects.com/hotel-tariffs/bf04ea76-f930-457f-b26b-725168596f19-97fef185-4485-4996-8b2f-66cfb3f30899-b6e3cd9f-2625-42f1-a88a-43d06a69c354-1747453812034-Dew-Drop-Tariff-25-26.pdf"
}

def download_pdf(url):
    """Download a PDF from a URL and save it locally"""
    try:
        logger.info(f"Downloading PDF from URL: {url}")
        
        # Create a downloads directory if it doesn't exist
        os.makedirs('downloads', exist_ok=True)
        
        # Get filename from URL
        parsed_url = urllib.parse.urlparse(url)
        filename = os.path.basename(parsed_url.path)
        
        # Remove URL encoding from filename
        filename = urllib.parse.unquote(filename)
        
        # Save path
        save_path = os.path.join('downloads', filename)
        
        # Download the file
        response = requests.get(url, stream=True)
        response.raise_for_status()  # Raise exception for HTTP errors
        
        # Save the file
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
                
        logger.info(f"PDF saved to: {save_path}")
        return save_path
        
    except Exception as e:
        logger.error(f"Error downloading PDF: {str(e)}")
        raise

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_pdf.py <hotel_name>")
        print("Available hotels:")
        for hotel in PDF_URLS.keys():
            print(f"  - {hotel}")
        sys.exit(1)

    hotel_name = sys.argv[1].lower()
    
    if hotel_name not in PDF_URLS:
        print(f"Error: Hotel '{hotel_name}' not found. Available hotels:")
        for hotel in PDF_URLS.keys():
            print(f"  - {hotel}")
        sys.exit(1)
    
    pdf_url = PDF_URLS[hotel_name]
    
    try:
        # Download the PDF
        pdf_path = download_pdf(pdf_url)
        
        # Extract data
        print(f"\nExtracting data from {pdf_path}...")
        results = extract_from_tables(pdf_path)
        
        # Print results
        print("\n==== EXTRACTION RESULTS ====")
        print(f"Found {len(results)} tariff entries")
        print(json.dumps(results, indent=2))
        
        # Create a results directory if it doesn't exist
        os.makedirs('results', exist_ok=True)
        
        # Save results to JSON file
        output_filename = f"{hotel_name}_results.json"
        output_path = os.path.join('results', output_filename)
        with open(output_path, 'w') as f:
            json.dump(results, indent=2, fp=f)
            
        print(f"\nResults saved to: {output_path}")
        print("Check extraction_test.log for detailed extraction logs.")
        
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 
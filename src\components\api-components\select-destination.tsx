// ./select-destination.tsx

import React from 'react';

interface Option {
  _id: string;
  destinationId: string;
  destinationType: string;
  rankNo: number;
  destinationName: string;
  __v: number;
}

interface SelectDestinationProps {
  options: Option[];
  placeholder: string;
  disabled?: boolean;
  onChange: (value: string) => void;
}

const SelectDestination: React.FC<SelectDestinationProps> = ({
  options,
  placeholder,
  disabled = false,
  onChange,
}) => {
  return (
   <div className=''>
     <select 
      disabled={disabled}
      onChange={(e) => onChange(e.target.value)}
      className=" border rounded-md p-2  w-64"
      defaultValue=""
    >
      <option value="" className='text-slate-400' disabled hidden>{placeholder}</option>
      {options?.map((option) => (
        <option key={option._id} value={option._id}>
          {option.destinationName}
        </option>
      ))}
    </select>
   </div>
  );
};

export default SelectDestination;

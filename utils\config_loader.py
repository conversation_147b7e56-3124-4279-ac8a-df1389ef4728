#!/usr/bin/env python3
"""
Configuration Loader for PDF Extraction

This module loads and manages configuration settings for the PDF extraction process.
It supports loading from JSON or YAML files and provides access to patterns, keywords,
and other configuration settings.
"""

import os
import json
import logging
import re
from typing import Dict, List, Any, Optional, Union
from pathlib import Path

# Configure logging
logger = logging.getLogger('tariff_extractor')

class ConfigLoader:
    """Configuration loader for PDF extraction"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration loader
        
        Args:
            config_path: Path to the configuration file (JSON or YAML)
                         If None, will look for config in default locations
        """
        self.config = {}
        self.compiled_patterns = {}
        
        # Default config paths to check
        self.default_paths = [
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'extraction_config.json'),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'extraction_config.json'),
            '/etc/tripmilestone/extraction_config.json',
            os.path.expanduser('~/.tripmilestone/extraction_config.json')
        ]
        
        # Load configuration
        if config_path:
            self.load_config(config_path)
        else:
            self._load_from_default_paths()
            
        # Compile regex patterns for better performance
        self._compile_patterns()
        
    def load_config(self, config_path: str) -> bool:
        """
        Load configuration from a file
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            True if configuration was loaded successfully, False otherwise
        """
        try:
            with open(config_path, 'r') as f:
                if config_path.endswith('.json'):
                    self.config = json.load(f)
                elif config_path.endswith(('.yaml', '.yml')):
                    try:
                        import yaml
                        self.config = yaml.safe_load(f)
                    except ImportError:
                        logger.error("YAML support requires PyYAML. Install with: pip install pyyaml")
                        return False
                else:
                    logger.error(f"Unsupported configuration file format: {config_path}")
                    return False
                    
            logger.info(f"Loaded configuration from {config_path}")
            
            # Recompile patterns after loading new config
            self._compile_patterns()
            
            return True
        except Exception as e:
            logger.error(f"Error loading configuration from {config_path}: {str(e)}")
            return False
            
    def _load_from_default_paths(self) -> bool:
        """
        Try to load configuration from default paths
        
        Returns:
            True if configuration was loaded successfully, False otherwise
        """
        for path in self.default_paths:
            if os.path.exists(path):
                if self.load_config(path):
                    return True
                    
        logger.warning("Could not find configuration file in default locations. Using built-in defaults.")
        self._load_builtin_defaults()
        return False
        
    def _load_builtin_defaults(self) -> None:
        """Load built-in default configuration"""
        self.config = {
            "version": "1.0.0",
            "patterns": {
                "date": {
                    "date_range": "(?:valid(?:ity)?|applicable)(?:\\s+from|\\s+for|\\s+during)?\\s+([^\\n.]+)|(?:from|between)\\s+(\\d{1,2}[./-]\\d{1,2}[./-]\\d{2,4})\\s*(?:to|-|till|until|through)\\s*(\\d{1,2}[./-]\\d{1,2}[./-]\\d{2,4})",
                    "date_format": "\\d{1,2}[./-]\\d{1,2}[./-]\\d{2,4}|\\d{1,2}\\s+(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\\s+\\d{2,4}"
                },
                "price": {
                    "price_format": "(?:₹|rs\\.?|inr|\\$)?\\s*\\d[\\d,]*\\.?\\d*\\s*(?:\\+?(?:tax|gst))?"
                },
                "meal_plan": {
                    "cp_supplement": "(?:MAP|Modified American Plan|Modified American)(?:\\s+supplement|\\s+extra|\\s+additional)(?:\\s+charge)?(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)"
                },
                "extra_charges": {
                    "extra_adult": "(?:extra|additional)(?:\\s+adult|\\s+person)(?:\\s+charge)?(?:\\s+:|\\s+-|\\s+is)?\\s*(?:Rs\\.?|INR|₹)?\\s*(\\d[\\d,]*\\.?\\d*)"
                }
            },
            "keywords": {
                "meal_plans": {
                    "ep": ["ep", "european plan", "room only", "without meals"],
                    "cp": ["cp", "continental plan", "breakfast", "bed and breakfast", "b&b"],
                    "map": ["map", "modified american plan", "half board", "breakfast and dinner"],
                    "ap": ["ap", "american plan", "full board", "all meals"]
                }
            },
            "defaults": {
                "meal_plan": "cp",
                "occupancy": "double",
                "currency": "INR"
            }
        }
        
        logger.info("Loaded built-in default configuration")
        
    def _compile_patterns(self) -> None:
        """Compile regex patterns for better performance"""
        self.compiled_patterns = {}
        
        # Get patterns from config
        patterns = self.config.get('patterns', {})
        
        # Compile each pattern category
        for category, category_patterns in patterns.items():
            self.compiled_patterns[category] = {}
            
            for pattern_name, pattern_string in category_patterns.items():
                try:
                    self.compiled_patterns[category][pattern_name] = re.compile(pattern_string, re.IGNORECASE)
                    logger.debug(f"Compiled pattern {category}.{pattern_name}")
                except re.error as e:
                    logger.error(f"Error compiling pattern {category}.{pattern_name}: {str(e)}")
                    # Use a simple fallback pattern that won't crash but won't match anything useful
                    self.compiled_patterns[category][pattern_name] = re.compile(r"^\b$")
                    
    def get_pattern(self, category: str, pattern_name: str) -> Optional[re.Pattern]:
        """
        Get a compiled regex pattern
        
        Args:
            category: Pattern category (e.g., 'date', 'price')
            pattern_name: Pattern name within the category
            
        Returns:
            Compiled regex pattern or None if not found
        """
        return self.compiled_patterns.get(category, {}).get(pattern_name)
        
    def get_pattern_string(self, category: str, pattern_name: str) -> Optional[str]:
        """
        Get the original pattern string
        
        Args:
            category: Pattern category (e.g., 'date', 'price')
            pattern_name: Pattern name within the category
            
        Returns:
            Pattern string or None if not found
        """
        return self.config.get('patterns', {}).get(category, {}).get(pattern_name)
        
    def get_keywords(self, category: str, subcategory: Optional[str] = None) -> Union[Dict[str, List[str]], List[str], None]:
        """
        Get keywords for a category
        
        Args:
            category: Keyword category (e.g., 'meal_plans')
            subcategory: Optional subcategory (e.g., 'cp')
            
        Returns:
            Dictionary of keywords, list of keywords, or None if not found
        """
        if subcategory:
            return self.config.get('keywords', {}).get(category, {}).get(subcategory)
        else:
            return self.config.get('keywords', {}).get(category)
            
    def get_default(self, key: str) -> Any:
        """
        Get a default value
        
        Args:
            key: Default key (e.g., 'meal_plan')
            
        Returns:
            Default value or None if not found
        """
        return self.config.get('defaults', {}).get(key)
        
    def get_hotel_handler(self, hotel_name: str) -> Optional[Dict[str, Any]]:
        """
        Get hotel-specific handler configuration
        
        Args:
            hotel_name: Name of the hotel
            
        Returns:
            Hotel handler configuration or None if not found
        """
        if not hotel_name:
            return None
            
        # Check for exact match
        handlers = self.config.get('hotel_specific_handlers', {})
        if hotel_name.lower() in handlers:
            return handlers[hotel_name.lower()]
            
        # Check for keyword match
        for handler_name, handler_config in handlers.items():
            trigger_keywords = handler_config.get('trigger_keywords', [])
            if any(keyword.lower() in hotel_name.lower() for keyword in trigger_keywords):
                logger.info(f"Matched hotel '{hotel_name}' to handler '{handler_name}' via keywords")
                return handler_config
                
        return None
        
    def get_ocr_settings(self) -> Dict[str, Any]:
        """
        Get OCR settings
        
        Returns:
            OCR settings dictionary
        """
        return self.config.get('ocr_settings', {})
        
    def get_ner_settings(self) -> Dict[str, Any]:
        """
        Get NER settings
        
        Returns:
            NER settings dictionary
        """
        return self.config.get('ner_settings', {})
        
    def get_config_version(self) -> str:
        """
        Get configuration version
        
        Returns:
            Configuration version string
        """
        return self.config.get('version', 'unknown')
        
    def get_full_config(self) -> Dict[str, Any]:
        """
        Get the full configuration
        
        Returns:
            Complete configuration dictionary
        """
        return self.config

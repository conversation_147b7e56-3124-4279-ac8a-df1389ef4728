interface PaginationProps {
    changePage: (pgNo: number) => void
    pageNo: number
    totalPages: number
    setPageNo:  (value: React.SetStateAction<number>) => void
}
export default function Pagination({changePage,pageNo,setPageNo,totalPages}:PaginationProps) {
    function handleBack() {
        if (pageNo >= 1) {
            setPageNo( pageNo - 1)
            changePage(pageNo-1)
        }
      }
      function handleNext() {
        if (pageNo < totalPages - 1){ 
            setPageNo( pageNo + 1)
            changePage(pageNo+1)
        }
      }
  return (
    <div className="flex justify-center m-1">
    {
      totalPages > 1&&<div className="flex text-white">
        <span
          onClick={handleBack}
          className="w-[30px] h-[30px] bg-gray-500 border flex justify-center items-center cursor-pointer"
        >
          &lt;
        </span>
        {Array.from({ length: totalPages })?.map((_k, i) => (
          <button
            onClick={() => changePage(i)}
            key={i}
            className={
              pageNo === i
                ? "bg-blue-800 w-[30px] h-[30px] border"
                : "bg-blue-500 w-[30px] h-[30px] border "
            }
          >
            {i + 1}
          </button>
        ))}
        <button
          onClick={handleNext}
          className="w-[30px] h-[30px] bg-gray-500 border"
        >
          &gt;
        </button>
      </div>
}
    </div>
  )
}

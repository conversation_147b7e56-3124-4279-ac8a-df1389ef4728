/* eslint-disable @typescript-eslint/no-explicit-any */
import {  useEffect, useState } from "react";
import { label_css } from "../PackageForm";
import ActivityForm from "./ActivityForm";
import PackageContext from "@/utils/context/PackageContext";
import React from "react";
import ActivityDetails, { ActivityDetailsType } from "./ActivityDetails";
type ActivityDayProps = {
  day: number;
  key: number;
};
export type ActivityEvent = {

  slot: number;
  activityType: "allocated" | "free";
  activityId: string | null;
  name:string,
  price?:number,
  timePeriod: "morning" | "noon" | "evening" | "noon-evening" | "full-day";
};
export default function ActivityDay(props: ActivityDayProps) {
  const [isFromSet,setIsFromSet] = useState(false);
  const { handleActivityEvents,dayActivity,handleActivityDetails }: any = React.useContext(PackageContext);

  const [events, setEvents] = useState<ActivityEvent[]>([]);
  function handleEvents(event: ActivityEvent) {
    
    if(event){
      const eve = [...events,event].sort((k,l)=>k.slot - l.slot)
      setEvents(eve);
      handleActivityEvents(props.day,eve)
    }
  }
  function handleDeleteEvent(event:ActivityEvent){
    if(event){
      const eve = events?.filter(k=>k.slot !== event.slot || k.activityId !== event.activityId)
      setEvents(eve)
      handleActivityEvents(props.day,eve)
    }
    
  }
  function setFromTo(day:number,data:ActivityDetailsType){
    handleActivityDetails(day,data);
    setIsFromSet(true);
  }

  function initialEditLoad(){
      const aa = dayActivity?.find((k: any) => k.day === props.day)
      setEvents(aa?.event)
      setIsFromSet(aa?.from && aa?.to );
  }
  useEffect(()=>{
    initialEditLoad()
  },[dayActivity])
  return (
    <div className="w-[300px] p-1 min-h-[200px] bg-slate-200 border m-1 justify-between  flex flex-col items-center">
      <div className="w-full">
        <div className={label_css +" w-full"}>Day : {props.day}</div>
        {!isFromSet && <div className="flex justify-center">
        <ActivityDetails day={props.day} setFromTo={setFromTo}/>
          </div>}
        <div className="">
          {events?.length > 0 && events?.map((k: any,i) => {
            return <div key={i} className="w-full py-2 bg-slate-300 px-2 mb-1 flex justify-between">{k.slot}. <span className="text-sm">{k.activityType !== 'free' ?k.name?.length>30? k.name.slice(0,30)+'...' : k.name || 'free':'free'}</span> <button onClick={()=>handleDeleteEvent(k)} className="bg-red-500 p-1 rounded-full h-[30px]" >X</button></div>;
          })}
          
        </div>
      </div>

<div className="flex gap-2">
{isFromSet && <ActivityForm day={props.day} handleEvents={handleEvents} sort={events?.length+1}/>}
 
</div>
     
    </div>
  );
}

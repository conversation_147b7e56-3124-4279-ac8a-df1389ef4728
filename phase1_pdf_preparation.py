#!/usr/bin/env python3
"""
Phase 1: PDF Preparation & Initial Understanding

This script implements the first phase of the PDF extraction process:
1. PDF Loading & Basic Content Retrieval
2. Preliminary Text & Data Cleaning

Usage:
    python phase1_pdf_preparation.py <pdf_file_path> [--output <output_file>] [--debug]
"""

import os
import sys
import json
import argparse
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add utils directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our new components
from utils.pdf_loader import PDFLoader
from utils.text_cleaner import TextCleaner
from utils.config_loader import ConfigLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('tariff_extractor')

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Phase 1: PDF Preparation & Initial Understanding')
    
    # Required arguments
    parser.add_argument('pdf_file', help='Path to the PDF file')
    
    # Output options
    parser.add_argument('--output', '-o', help='Output file (default: stdout)')
    
    # Configuration options
    parser.add_argument('--config', help='Path to configuration file')
    
    # Logging options
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    
    return parser.parse_args()

def main():
    """Main function"""
    args = parse_args()
    
    # Configure logging
    if args.debug:
        logger.setLevel(logging.DEBUG)
    
    # Load configuration
    config = None
    if args.config:
        config = ConfigLoader(args.config)
        logger.info(f"Loaded configuration from {args.config}")
    else:
        config = ConfigLoader()
        logger.info(f"Using default configuration")
    
    start_time = datetime.now()
    
    try:
        # Step 1: PDF Loading & Basic Content Retrieval
        logger.info(f"Step 1: Loading PDF {args.pdf_file}")
        pdf_loader = PDFLoader(args.pdf_file, config)
        text, tables, metadata = pdf_loader.load_pdf()
        
        # Log basic PDF information
        logger.info(f"PDF loaded: {len(text)} chars, {len(tables)} tables, {metadata.get('page_count', 0)} pages")
        logger.info(f"PDF is {'scanned' if metadata.get('is_scanned', False) else 'digital'}")
        logger.info(f"OCR was {'used' if metadata.get('ocr_used', False) else 'not used'}")
        
        # Sample text for debugging
        if text:
            sample_text = text[:200] + "..." if len(text) > 200 else text
            logger.info(f"Text sample: {sample_text}")
        
        # Step 2: Preliminary Text & Data Cleaning
        logger.info(f"Step 2: Cleaning text and tables")
        text_cleaner = TextCleaner(config)
        
        # Clean the full text
        cleaned_text = text_cleaner.clean_text(text)
        
        # Clean tables
        cleaned_tables = []
        for i, table in enumerate(tables):
            if table:
                cleaned_table = text_cleaner.clean_table(table)
                cleaned_tables.append(cleaned_table)
                logger.debug(f"Cleaned table {i+1}: {len(table)} rows -> {len(cleaned_table)} rows")
        
        # Log cleaning statistics
        logger.info(f"Text cleaning: {len(text)} chars -> {len(cleaned_text)} chars")
        logger.info(f"Table cleaning: {len(tables)} tables -> {len(cleaned_tables)} tables")
        
        # Prepare output
        result = {
            "pdf_path": args.pdf_file,
            "metadata": metadata,
            "text_sample": cleaned_text[:1000] if len(cleaned_text) > 1000 else cleaned_text,
            "tables_sample": cleaned_tables[:2] if len(cleaned_tables) > 2 else cleaned_tables,
            "cleaning_stats": text_cleaner.get_cleaning_stats(),
            "processing_time_seconds": (datetime.now() - start_time).total_seconds()
        }
        
        # Output the results
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2)
            logger.info(f"Results saved to {args.output}")
        else:
            # Print to stdout for piping or capture
            print(json.dumps(result, indent=2))
        
        # Log success
        logger.info(f"PDF preparation completed successfully in {(datetime.now() - start_time).total_seconds():.2f} seconds")
        
    except Exception as e:
        # Log error
        logger.error(f"Error during PDF preparation: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()

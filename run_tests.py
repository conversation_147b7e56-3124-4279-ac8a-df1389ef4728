#!/usr/bin/env python3
"""
Run the full test suite for the PDF extraction system

This script runs all tests and validation for the PDF extraction system.
"""

import os
import sys
import json
import unittest
import argparse
import logging
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('run_tests')

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Run PDF extraction tests')
    parser.add_argument('--generate', action='store_true', help='Generate test data')
    parser.add_argument('--unit-tests', action='store_true', help='Run unit tests')
    parser.add_argument('--integration-tests', action='store_true', help='Run integration tests')
    parser.add_argument('--validate', action='store_true', help='Run validation on test PDFs')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--pdf-dir', help='Directory containing test PDFs')
    parser.add_argument('--output-dir', help='Directory for test output')
    return parser.parse_args()

def run_unit_tests():
    """Run unit tests"""
    logger.info("Running unit tests...")
    
    # Run unittest discovery
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('tests', pattern='test_*.py')
    
    # Run tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Log results
    logger.info(f"Unit tests complete: {result.testsRun} tests run")
    if result.failures:
        logger.error(f"{len(result.failures)} failures")
        for test, traceback in result.failures:
            logger.error(f"FAIL: {test}")
    
    if result.errors:
        logger.error(f"{len(result.errors)} errors")
        for test, traceback in result.errors:
            logger.error(f"ERROR: {test}")
    
    return len(result.failures) == 0 and len(result.errors) == 0

def generate_test_data():
    """Generate test data"""
    logger.info("Generating test data...")
    
    try:
        # Run the generate_test_data.py script
        subprocess.run([sys.executable, 'tests/generate_test_data.py'], check=True)
        logger.info("Test data generation complete")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error generating test data: {e}")
        return False

def run_integration_tests(pdf_dir: Optional[str] = None, output_dir: Optional[str] = None):
    """Run integration tests with actual PDFs"""
    if not pdf_dir:
        pdf_dir = 'tests/test_data'
    
    if not output_dir:
        output_dir = 'tests/results'
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    logger.info(f"Running integration tests with PDFs from {pdf_dir}...")
    
    # Find all PDF files
    pdf_files = []
    for root, _, files in os.walk(pdf_dir):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    if not pdf_files:
        logger.warning(f"No PDF files found in {pdf_dir}")
        return True
    
    logger.info(f"Found {len(pdf_files)} PDF files")
    
    # Process each PDF file
    success_count = 0
    for pdf_file in pdf_files:
        pdf_name = os.path.basename(pdf_file)
        logger.info(f"Processing {pdf_name}...")
        
        output_file = os.path.join(output_dir, f"{os.path.splitext(pdf_name)[0]}.json")
        
        try:
            # Run the advanced_pdf_extractor.py script
            result = subprocess.run(
                [sys.executable, 'advanced_pdf_extractor.py', pdf_file, '--output', output_file],
                capture_output=True,
                text=True,
                check=True
            )
            
            logger.info(f"Extraction complete for {pdf_name}")
            
            # Check if output file was created
            if os.path.exists(output_file):
                # Validate the extraction results
                validate_output(output_file)
                success_count += 1
            else:
                logger.error(f"Output file not created for {pdf_name}")
        except subprocess.CalledProcessError as e:
            logger.error(f"Error processing {pdf_name}: {e}")
            logger.error(f"STDOUT: {e.stdout}")
            logger.error(f"STDERR: {e.stderr}")
    
    logger.info(f"Integration tests complete: {success_count}/{len(pdf_files)} successful")
    return success_count == len(pdf_files)

def validate_output(output_file: str):
    """Validate extraction output"""
    logger.info(f"Validating {os.path.basename(output_file)}...")
    
    try:
        # Run the validate_extraction.py script
        result = subprocess.run(
            [sys.executable, 'validation/validate_extraction.py', output_file, '--html'],
            capture_output=True,
            text=True,
            check=True
        )
        
        logger.info(f"Validation complete for {os.path.basename(output_file)}")
        
        # Check for validation issues
        if "Found 0 validation issues" in result.stdout:
            logger.info("No validation issues found")
        else:
            # Extract number of issues
            import re
            match = re.search(r"Found (\d+) validation issues", result.stdout)
            if match:
                issue_count = int(match.group(1))
                logger.warning(f"Found {issue_count} validation issues")
            else:
                logger.warning("Validation issues found (count unknown)")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error validating {os.path.basename(output_file)}: {e}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")

def main():
    """Main function"""
    args = parse_args()
    
    # If no specific tests are selected, run all
    if not (args.generate or args.unit_tests or args.integration_tests or args.validate):
        args.all = True
    
    # Track overall success
    success = True
    
    # Generate test data if requested
    if args.generate or args.all:
        if not generate_test_data():
            success = False
    
    # Run unit tests if requested
    if args.unit_tests or args.all:
        if not run_unit_tests():
            success = False
    
    # Run integration tests if requested
    if args.integration_tests or args.all:
        if not run_integration_tests(args.pdf_dir, args.output_dir):
            success = False
    
    # Run validation if requested
    if args.validate or args.all:
        # Validation is done as part of integration tests
        pass
    
    # Report overall status
    if success:
        logger.info("All tests completed successfully")
        sys.exit(0)
    else:
        logger.error("Some tests failed")
        sys.exit(1)

if __name__ == "__main__":
    main()

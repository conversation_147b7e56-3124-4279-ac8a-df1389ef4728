/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import {useState} from "react";
import DialogTitle from "@mui/material/DialogTitle";
import PackageSearchSelect from "../package-inputs/PackageSearchSelect";
import PackageContext from "@/utils/context/PackageContext";
import { TextField } from "@mui/material";

export type ActivityDetailsType = {
  from: string;
  to: string;
  startDateWise: number;
};
type ActivityDetailsProps = {
  day: number;
  setFromTo: (day:number,data: ActivityDetailsType) => void;
};
export default function ActivityDetails(props: ActivityDetailsProps) {
    const { destination,allActivity,destinationIds }  :any= React.useContext(PackageContext);
  const [open, setOpen] = useState(false);
    const [from,setFrom] = useState("");
    const [to,setTo] = useState("");
    const [startDateWise,setStartDateWise] = useState(0);
    const [destActivities,setDestActivities] = useState<any[]>([]);

    const [fromDestinationId,setFromDestiationId] = useState("");   
    const [toDestinationId,setToDestiationId] = useState("");
  const handleClickOpen = () => {
    setOpen(true);
  };
 

  const handleClose = () => {
    setOpen(false);
    console.log(fromDestinationId,toDestinationId);
  };
  function handleActivities(ids:any){
    if(destinationIds?.length > 0){
      const aa:any = allActivity.filter((k: any) => ids.some((l:any)=> l.destinationId === k.destinationId))
      setDestActivities(aa)
    }
  }
  React.useEffect(()=>{
    handleActivities(destinationIds)
  },[destinationIds])
  return (
    <React.Fragment>
      <Button variant="outlined" color="error" onClick={handleClickOpen}>
        Set Activity Details
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        PaperProps={{
          component: "form",
          onSubmit: (event: React.FormEvent<HTMLFormElement>) => {
            event.preventDefault();
       
            if (
              
                from && to && startDateWise >= 0
            ) {
              const eve = {
                from,to,startDateWise
              };
              props.setFromTo(props.day,eve);
              handleClose();
            }
          },
        }}
      >
        <DialogTitle>Activity Day:{props.day}</DialogTitle>
        <div className="flex flex-col w-[300px] gap-2 m-2">
        <PackageSearchSelect
            setDataName={setFrom}
            inputName={from}
            allData={destination}
            dataName="destinationName"
            dataId="destinationId"
            setData={setFromDestiationId}
            pHolder="From"
          />
            <PackageSearchSelect
            setDataName={setTo}
            inputName={to}
            allData={destActivities}
            dataName="name"
            dataId="ActivityId"
            setData={setToDestiationId}
            pHolder="To"
          />
            <TextField
            autoFocus
            required
            margin="dense"
            id="name"
            name="startDateWise"
            label="Start Date Wise"
            type="number"
            variant="standard"
            value={startDateWise}
            onInput={(e: any) => setStartDateWise(e.target.valueAsNumber)}
          />
          
          
        </div>

        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="submit">Save Event</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}

/**
 * Enhanced logger utility for server-side logging
 * Provides structured logging with timestamps and log levels
 */

const fs = require('fs');
const path = require('path');
const winston = require('winston');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// Create Winston logger
const winstonLogger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'tripmilestone-admin-backend' },
  transports: [
    // Write all logs to console
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(({ level, message, timestamp, ...meta }) => {
          return `${timestamp} ${level}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
        })
      )
    }),
    // Write all logs to file
    new winston.transports.File({ 
      filename: path.join(logsDir, 'error.log'), 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: path.join(logsDir, 'combined.log') 
    })
  ]
});

// Simple logger interface that uses Winston under the hood
const logger = {
  info: (message, data = {}) => {
    winstonLogger.info(message, data);
  },
  
  warn: (message, data = {}) => {
    winstonLogger.warn(message, data);
  },
  
  error: (message, data = {}) => {
    winstonLogger.error(message, data);
  },
  
  debug: (message, data = {}) => {
    winstonLogger.debug(message, data);
  },
  
  // Add a method to log API requests
  logRequest: (req, res, next) => {
    const start = Date.now();
    
    // Log when the request completes
    res.on('finish', () => {
      const duration = Date.now() - start;
      const logData = {
        method: req.method,
        url: req.originalUrl || req.url,
        status: res.statusCode,
        duration: `${duration}ms`,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('user-agent') || ''
      };
      
      // Log at appropriate level based on status code
      if (res.statusCode >= 500) {
        winstonLogger.error(`API Request: ${req.method} ${req.originalUrl || req.url}`, logData);
      } else if (res.statusCode >= 400) {
        winstonLogger.warn(`API Request: ${req.method} ${req.originalUrl || req.url}`, logData);
      } else {
        winstonLogger.info(`API Request: ${req.method} ${req.originalUrl || req.url}`, logData);
      }
    });
    
    next();
  }
};

module.exports = logger;

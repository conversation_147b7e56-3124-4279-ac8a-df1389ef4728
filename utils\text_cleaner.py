#!/usr/bin/env python3
"""
Text Cleaner for Tariff Extraction

This module provides enhanced text cleaning and preprocessing functionality,
with improved handling of currency symbols, whitespace, and common patterns.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Union

# Configure logging
logger = logging.getLogger('tariff_extractor')

class TextCleaner:
    """Enhanced text cleaner with improved preprocessing capabilities"""

    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the text cleaner

        Args:
            config: Optional configuration object
        """
        self.config = config

        # Default patterns for cleaning
        self.patterns = {
            'currency': r'(?i)Rs\.?|₹|INR|\$|€|£',
            'tax': r'(?i)\+?(?:GST|tax|taxes|VAT|service\s+charge)',
            'price_suffix': r'(?i)/-|per\s+(?:night|room|day|person|pax|adult|child)',
            'whitespace': r'\s+',
            'special_chars': r'[^\w\s.,;:()%+\-]',
            'multiple_dots': r'\.{2,}',
            'multiple_dashes': r'-{2,}',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'url': r'https?://(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)',
            'phone': r'(?:\+\d{1,3}[-.\s]?)?\(?\d{3,4}\)?[-.\s]?\d{3,4}[-.\s]?\d{3,4}',
            'disclaimer': r'(?i)(?:terms\s+and\s+conditions|cancellation\s+policy|disclaimer|subject\s+to\s+change)'
        }

        # Load patterns from config if available
        if config and hasattr(config, 'get_pattern_string'):
            for category, pattern_dict in config.get_full_config().get('patterns', {}).items():
                for pattern_name, pattern in pattern_dict.items():
                    self.patterns[f"{category}_{pattern_name}"] = pattern

        # Compile regex patterns for better performance
        self.compiled_patterns = {
            name: re.compile(pattern) for name, pattern in self.patterns.items()
        }

        # Statistics for cleaning operations
        self.cleaning_stats = {
            'total_cleaned_texts': 0,
            'currency_replacements': 0,
            'tax_replacements': 0,
            'whitespace_normalizations': 0,
            'special_char_removals': 0,
            'disclaimer_removals': 0
        }

    def clean_text(self, text: str, preserve_currency: bool = False,
                  preserve_tax: bool = False, context: str = "general") -> str:
        """
        Clean and normalize text with context-aware processing

        Args:
            text: Text to clean
            preserve_currency: Whether to preserve currency symbols
            preserve_tax: Whether to preserve tax mentions
            context: Context of the text (e.g., "price", "room_type", "date", "general")

        Returns:
            Cleaned text
        """
        if not text:
            return ""

        self.cleaning_stats['total_cleaned_texts'] += 1
        original_text = text

        # Remove URLs and emails (almost always noise in tariff PDFs)
        text = self.compiled_patterns['url'].sub('', text)
        text = self.compiled_patterns['email'].sub('', text)

        # Context-specific cleaning
        if context == "price":
            # For price context, we want to keep only numbers and decimal points
            # First, remove currency symbols if not preserving them
            if not preserve_currency:
                text = self.compiled_patterns['currency'].sub('', text)
                self.cleaning_stats['currency_replacements'] += 1

            # Remove tax mentions if not preserving them
            if not preserve_tax:
                text = self.compiled_patterns['tax'].sub('', text)
                self.cleaning_stats['tax_replacements'] += 1

            # Remove price suffixes
            text = self.compiled_patterns['price_suffix'].sub('', text)

            # Keep only digits, commas, dots, and currency symbols if preserved
            if preserve_currency:
                text = re.sub(r'[^\d.,₹$€£Rs]', '', text)
            else:
                text = re.sub(r'[^\d.,]', '', text)

            # Remove commas for numeric processing
            text = text.replace(',', '')

        elif context == "room_type":
            # For room type context, preserve meaningful text but remove noise
            # Remove special characters except parentheses
            text = re.sub(r'[^\w\s()]', ' ', text)

            # Normalize whitespace
            text = self.compiled_patterns['whitespace'].sub(' ', text)

        elif context == "date":
            # For date context, preserve date-related characters
            # Remove special characters except those used in dates
            text = re.sub(r'[^\w\s./-]', ' ', text)

            # Normalize whitespace
            text = self.compiled_patterns['whitespace'].sub(' ', text)

        else:  # General context
            # Remove currency symbols if not preserving them
            if not preserve_currency:
                text = self.compiled_patterns['currency'].sub('', text)
                self.cleaning_stats['currency_replacements'] += 1

            # Remove tax mentions if not preserving them
            if not preserve_tax:
                text = self.compiled_patterns['tax'].sub('', text)
                self.cleaning_stats['tax_replacements'] += 1

                # Also remove any "+" that might be left after removing tax
                text = re.sub(r'\s*\+\s*$', '', text)

            # Replace price suffixes with a space to avoid word joining
            text = re.sub(r'/-', ' ', text)
            text = re.sub(r'(?i)per\s+night|per\s+room|per\s+day', ' ', text)

            # Remove disclaimers and common irrelevant text
            text = self.compiled_patterns['disclaimer'].sub('', text)
            self.cleaning_stats['disclaimer_removals'] += 1

            # Normalize multiple dots and dashes
            text = self.compiled_patterns['multiple_dots'].sub('.', text)
            text = self.compiled_patterns['multiple_dashes'].sub('-', text)

            # Normalize whitespace
            text = self.compiled_patterns['whitespace'].sub(' ', text)
            self.cleaning_stats['whitespace_normalizations'] += 1

        # Remove leading/trailing whitespace
        text = text.strip()

        # Log significant changes for debugging
        if len(original_text) > 20 and len(text) < len(original_text) * 0.5:
            logger.debug(f"Significant text reduction during cleaning: {len(original_text)} -> {len(text)} chars")
            logger.debug(f"Original: '{original_text[:50]}...'")
            logger.debug(f"Cleaned:  '{text[:50]}...'")

        return text

    def clean_table(self, table: List[List[str]], preserve_header: bool = True) -> List[List[str]]:
        """
        Clean and normalize a table

        Args:
            table: Table to clean (list of rows, each row is a list of cells)
            preserve_header: Whether to preserve the header row with minimal cleaning

        Returns:
            Cleaned table
        """
        if not table:
            return []

        cleaned_table = []

        # Process header row with minimal cleaning if preserve_header is True
        if preserve_header and len(table) > 0:
            header_row = table[0]
            cleaned_header = [self.clean_text(cell, preserve_currency=True, preserve_tax=True) for cell in header_row]
            cleaned_table.append(cleaned_header)

            # Process data rows
            for row_idx, row in enumerate(table[1:], 1):
                cleaned_row = []
                for col_idx, cell in enumerate(row):
                    # Determine context based on column index and header
                    context = "general"
                    if col_idx < len(cleaned_header):
                        header_text = cleaned_header[col_idx].lower()
                        if any(price_term in header_text for price_term in ['price', 'rate', 'tariff', 'cost']):
                            context = "price"
                        elif any(room_term in header_text for room_term in ['room', 'type', 'category', 'accommodation']):
                            context = "room_type"
                        elif any(date_term in header_text for date_term in ['date', 'period', 'season', 'validity']):
                            context = "date"

                    # Clean cell with appropriate context
                    # For price cells, don't remove commas in table context
                    if context == "price":
                        # First clean with price context
                        cleaned_cell = self.clean_text(cell, context=context)
                        # Then add commas back for readability if they were in the original
                        if ',' in cell and cleaned_cell.isdigit():
                            # Format the number with commas
                            try:
                                num = int(cleaned_cell)
                                cleaned_cell = f"{num:,}"
                            except ValueError:
                                pass
                    else:
                        cleaned_cell = self.clean_text(cell, context=context)
                    cleaned_row.append(cleaned_cell)

                cleaned_table.append(cleaned_row)
        else:
            # Process all rows with general cleaning
            for row in table:
                cleaned_row = [self.clean_text(cell) for cell in row]
                cleaned_table.append(cleaned_row)

        return cleaned_table

    def extract_price(self, price_text: str) -> float:
        """
        Extract numeric price from string

        Args:
            price_text: Text containing a price

        Returns:
            Extracted price as float, or 0 if no price found
        """
        if not price_text:
            return 0

        # Clean the price text first
        cleaned_price = self.clean_text(price_text, context="price")

        # Extract all numbers from the string
        numbers = re.findall(r'[\d,]+\.?\d*', cleaned_price)
        if not numbers:
            return 0

        # Take the first number and convert to float
        price_str = numbers[0].replace(',', '')
        try:
            return float(price_str)
        except ValueError:
            return 0

    def get_cleaning_stats(self) -> Dict[str, int]:
        """
        Get statistics about cleaning operations

        Returns:
            Dictionary of cleaning statistics
        """
        return self.cleaning_stats

import EditHotelLayout from "@/components/layout-components/edit-hotel/edit-hotel-layout";
import EditRooms from "@/components/page-components/hotel-edit/EditRooms";
import { Button } from "@/components/ui/button";

import { useAppSelector } from "@/store/store";
import { HotelRoom } from "@/types/types";
import { fetchAllRooms } from "@/utils/api-functions/fetch-rooms";
import { updateHotel } from "@/utils/api-functions/hotel/edit-hotel";
import { fetchSingleHotel } from "@/utils/api-functions/hotel/fetch-hotel";
import { SERVER_IMAGE_URL } from "@/utils/urls/urls";
import { BedDouble, Building, FileText, Info, Map, Save, Settings, Tags, Upload } from "lucide-react";

import { ChangeEvent, useEffect, useReducer, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useDispatch } from "react-redux";
import { setTab } from "@/store/features/hotelEditSlice";

// Tab interface
interface TabItem {
  id: string;
  label: string;
  icon: React.ReactNode;
}

const EditHotel = () => {
  const { hotelId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { cuurentTab } = useAppSelector((state) => state.HotelEdit);
  const [rooms, setRooms] = useState<HotelRoom[]>([]);
  
  // Map the old tab names to our new tab IDs
  const tabMapping = {
    'hotel': 'details',
    'room': 'rooms',
    'mp': 'mealplans'
  };
  
  // Initialize active tab based on Redux state
  const [activeTab, setActiveTab] = useState<string>(tabMapping[cuurentTab as keyof typeof tabMapping] || 'details');

  // Define tabs for the header navigation
  const tabs: TabItem[] = [
    { id: "details", label: "Hotel Details", icon: <Building size={16} /> },
    { id: "rooms", label: "Rooms & Pricing", icon: <BedDouble size={16} /> },
    { id: "amenities", label: "Amenities", icon: <Tags size={16} /> },
    { id: "location", label: "Location", icon: <Map size={16} /> },
    { id: "policies", label: "Policies", icon: <FileText size={16} /> },
    { id: "photos", label: "Photos & Media", icon: <Upload size={16} /> },
    { id: "settings", label: "Settings", icon: <Settings size={16} /> },
  ];

  // Update both our local state and the Redux state when changing tabs
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    
    // Map back to the old tab format for Redux state if needed
    if (tabId === 'details') {
      dispatch(setTab('hotel'));
    } else if (tabId === 'rooms') {
      dispatch(setTab('room'));
    } else if (tabId === 'mealplans') {
      dispatch(setTab('mp'));
    }
  };

  useEffect(() => {
    const fetchRooms = async () => {
      if (hotelId !== undefined) {
        const response = await fetchAllRooms(hotelId);
        setRooms(response);
      }
    };
    fetchRooms();
  }, [hotelId]);

  const initialHotelState = {
    _id: "",
    hotelId: "",
    hotelName: "",
    location: {
      destinationId: "",
      lat: "",
      lon: "",
      address: "",
      state: "",
      country: "",
      _id: "",
    },
    viewPoint: [],
    image: "",
    contract: {
      businessEmail: "",
      additionalEmail: "",
      maintainerPhoneNo: "0",
      _id: "",
    },
    amenities: [],
    __v: 0,
  };

  type Action =
    | { type: "UPDATE_HOTEL"; payload: Partial<typeof initialHotelState> }
    | { type: "RESET_HOTEL"; payload?: undefined }
    | { type: "UPDATE_HOTEL_NAME"; payload: string }
    | {
        type: "UPDATE_CONTRACT_FIELD";
        payload: { field: string; value: string };
      }
    | {
        type: "UPDATE_LOCATION_FIELD";
        payload: { field: string; value: string };
      };

  const hotelReducer = (
    state: typeof initialHotelState,
    action: Action
  ): typeof initialHotelState => {
    switch (action.type) {
      case "UPDATE_HOTEL":
        return { ...state, ...action.payload };
      case "RESET_HOTEL":
        return initialHotelState;
      case "UPDATE_HOTEL_NAME":
        return { ...state, hotelName: action.payload };
      case "UPDATE_CONTRACT_FIELD":
        return {
          ...state,
          contract: {
            ...state.contract,
            [action.payload.field]: action.payload.value,
          },
        };
      case "UPDATE_LOCATION_FIELD":
        return {
          ...state,
          location: {
            ...state.location,
            [action.payload.field]: action.payload.value,
          },
        };
      default:
        return state;
    }
  };

  const [hotelState, dispatchReducer] = useReducer(hotelReducer, initialHotelState);

  useEffect(() => {
    const fetchHotel = async () => {
      try {
        if (hotelId !== undefined) {
          const response = await fetchSingleHotel(hotelId);
          dispatchReducer({ type: "UPDATE_HOTEL", payload: response });
        }
      } catch (error) {
        console.error("Error fetching hotel:", error);
      }
    };

    fetchHotel();
  }, [hotelId]);

  useEffect(() => {
    // Update active tab based on Redux state changes
    const newActiveTab = tabMapping[cuurentTab as keyof typeof tabMapping];
    if (newActiveTab) {
      setActiveTab(newActiveTab);
    }
  }, [cuurentTab]);

  const handleHotelNameChange = (value: string) => {
    dispatchReducer({ type: "UPDATE_HOTEL_NAME", payload: value });
  };

  const handleContractFieldChange = (field: string, value: string) => {
    dispatchReducer({ type: "UPDATE_CONTRACT_FIELD", payload: { field, value } });
  };

  const handleLocationFieldChange = (field: string, value: string) => {
    dispatchReducer({ type: "UPDATE_LOCATION_FIELD", payload: { field, value } });
  };
  
  function handleLatLong(e:string){
    const latLong  = e.split(",");
    if(latLong?.length ==2){
      const lattitude = latLong[0].trim();
      const Longitude = latLong[1].trim();
      handleLocationFieldChange("lat",lattitude);
      handleLocationFieldChange("lon",Longitude);
    }
  }

  // Render tab content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case "details":
  return (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-4">
          {rooms?.length === 0 && (
              <div className="bg-yellow-100 text-orange-600 w-full text-sm mb-6 p-3 rounded-md flex items-center justify-between">
                <div className="flex items-center">
                  <Info size={18} className="mr-2" />
                  <span>No rooms have been added to this hotel yet.</span>
                </div>
              <Link
                to={`/hotels/${hotelId}`}
                  className="bg-white text-gray-800 px-3 py-1.5 text-sm rounded-md shadow-sm flex items-center hover:bg-gray-50 transition-colors"
              >
                  <Upload className="text-blue-600 mr-1.5" size={14} /> Add Rooms
              </Link>
            </div>
          )}
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div>
              <img
                src={`${SERVER_IMAGE_URL}${hotelState.image}`}
                    alt="Hotel"
                    className="w-full h-48 object-cover rounded-lg shadow-sm"
              />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="hotelName" className="block text-sm font-medium text-gray-700">
                    Hotel Name
                  </label>
              <input
                    id="hotelName"
                value={hotelState.hotelName}
                type="text"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                onChange={(e) => handleHotelNameChange(e.target.value)}
                    placeholder="Enter hotel name"
              />
            </div>
              </div>

              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-700">Address Information</h3>

                <div className="space-y-2">
                  <label htmlFor="latlon" className="block text-sm font-medium text-gray-700">
                    Latitude and Longitude
                  </label>
                <input 
                    id="latlon"
                  type="text"
                  disabled={true}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md bg-gray-50"
                  defaultValue={`${hotelState?.location?.lat},${hotelState?.location?.lon || ''}`}
                    onInput={(e: ChangeEvent<HTMLInputElement>) => handleLatLong(e.target.value)}
                    placeholder="Latitude and Longitude"
                />
              </div>

                <div className="space-y-2">
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                    Address
                  </label>
                <input
                  id="address"
                  type="text"
                  value={hotelState.location.address}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    onChange={(e) => handleLocationFieldChange("address", e.target.value)}
                    placeholder="Enter hotel address"
                />
              </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                      Country
                    </label>
                <input
                  id="country"
                  type="text"
                  value={hotelState.location.country}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      onChange={(e) => handleLocationFieldChange("country", e.target.value)}
                      placeholder="Country"
                />
              </div>

                  <div className="space-y-2">
                    <label htmlFor="state" className="block text-sm font-medium text-gray-700">
                      State
                    </label>
                <input
                  id="state"
                  type="text"
                  value={hotelState.location.state}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      onChange={(e) => handleLocationFieldChange("state", e.target.value)}
                      placeholder="State/Province"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t">
              <h3 className="text-lg font-medium text-gray-700 mb-4">Contact Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="businessEmail" className="block text-sm font-medium text-gray-700">
                    Business Email
                  </label>
                <input
                  id="businessEmail"
                    type="email"
                  value={hotelState.contract.businessEmail}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    onChange={(e) => handleContractFieldChange("businessEmail", e.target.value)}
                    placeholder="Business email address"
                />
              </div>

                <div className="space-y-2">
                  <label htmlFor="additionalEmail" className="block text-sm font-medium text-gray-700">
                    Additional Email
                  </label>
                <input
                  id="additionalEmail"
                    type="email"
                  value={hotelState.contract.additionalEmail}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    onChange={(e) => handleContractFieldChange("additionalEmail", e.target.value)}
                    placeholder="Additional email address"
                />
              </div>

                <div className="space-y-2">
                  <label htmlFor="maintainerPhone" className="block text-sm font-medium text-gray-700">
                    Contact Phone
                  </label>
                <input
                    id="maintainerPhone"
                    type="tel"
                  value={hotelState.contract.maintainerPhoneNo}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    onChange={(e) => handleContractFieldChange("maintainerPhoneNo", e.target.value)}
                    placeholder="Contact phone number"
                  />
                </div>
              </div>
            </div>
            
            <div className="mt-8 flex justify-end">
              <button 
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                onClick={() => hotelId && updateHotel(hotelState, hotelId)}
              >
                <Save size={16} className="mr-2" />
                Save Changes
              </button>
            </div>
          </div>
        );
      case "rooms":
        return (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-4">
            <h2 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-6">Rooms & Pricing</h2>
            {hotelId ? <EditRooms hotelId={hotelId} /> : <p>No hotel ID found</p>}
          </div>
        );
      case "amenities":
        return (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-4">
            <h2 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-6">Amenities</h2>
            <p className="text-gray-600">Manage hotel amenities here.</p>
          </div>
        );
      case "location":
        return (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-4">
            <h2 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-6">Location</h2>
            <p className="text-gray-600">Manage location details here.</p>
          </div>
        );
      case "policies":
        return (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-4">
            <h2 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-6">Policies</h2>
            <p className="text-gray-600">Manage hotel policies here.</p>
          </div>
        );
      case "photos":
        return (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-4">
            <h2 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-6">Photos & Media</h2>
            <p className="text-gray-600">Manage hotel photos and media here.</p>
          </div>
        );
      case "settings":
        return (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-4">
            <h2 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-6">Settings</h2>
            <p className="text-gray-600">Manage hotel settings here.</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <EditHotelLayout>
      <div className="p-6 bg-gray-100 min-h-screen">
        {/* Header with hotel name and back button */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold text-gray-800">
            {hotelState.hotelName || "Edit Hotel"}
          </h1>
            <Button
            variant="outline" 
            className="gap-1"
            onClick={() => navigate(-1)}
            >
            Back
            </Button>
        </div>

        {/* Tabs header */}
        <div className="bg-white rounded-t-lg shadow-sm">
          <div className="border-b overflow-x-auto">
            <nav className="flex">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={cn(
                    "flex items-center py-4 px-6 text-sm font-medium border-b-2 transition-colors whitespace-nowrap",
                    activeTab === tab.id
                      ? "border-blue-600 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  )}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab content */}
        {renderTabContent()}
      </div>
    </EditHotelLayout>
  );
};

export default EditHotel;

#!/usr/bin/env python3
"""
Data Aggregator for Tariff Extraction

This module provides enhanced data aggregation and structuring functionality,
with improved handling of derived meal plans and extra charges.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Union, Set
from datetime import datetime

# Configure logging
logger = logging.getLogger('tariff_extractor')

class DataAggregator:
    """Enhanced data aggregator with improved handling of derived meal plans and extra charges"""
    
    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the data aggregator
        
        Args:
            config: Optional configuration object
        """
        self.config = config
        
        # Statistics for aggregation operations
        self.aggregation_stats = {
            'total_entries_before': 0,
            'total_entries_after': 0,
            'duplicates_removed': 0,
            'derived_meal_plans_added': 0,
            'extra_charges_added': 0,
            'entries_with_missing_data': 0,
            'entries_with_warnings': 0
        }
    
    def aggregate_data(self, prices: List[Dict[str, Any]], 
                      room_types: List[str], 
                      date_ranges: List[Dict[str, Any]], 
                      extra_charges: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Aggregate and structure extracted data
        
        Args:
            prices: List of extracted prices
            room_types: List of extracted room types
            date_ranges: List of extracted date ranges
            extra_charges: Dictionary of extracted extra charges
            
        Returns:
            List of aggregated and structured data entries
        """
        # Update statistics
        self.aggregation_stats['total_entries_before'] = len(prices)
        
        # Step 1: Remove duplicates
        unique_entries = self._remove_duplicates(prices)
        
        # Step 2: Add derived meal plans
        entries_with_derived_meal_plans = self._add_derived_meal_plans(unique_entries, extra_charges)
        
        # Step 3: Add extra charges
        entries_with_extra_charges = self._add_extra_charges(entries_with_derived_meal_plans, extra_charges)
        
        # Step 4: Validate and fix entries
        validated_entries = self._validate_and_fix_entries(entries_with_extra_charges, room_types, date_ranges)
        
        # Update statistics
        self.aggregation_stats['total_entries_after'] = len(validated_entries)
        
        return validated_entries
    
    def _remove_duplicates(self, entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Remove duplicate entries
        
        Args:
            entries: List of entries to deduplicate
            
        Returns:
            List of unique entries
        """
        unique_entries = []
        seen_keys = set()
        
        for entry in entries:
            # Create a key for deduplication
            key_parts = [
                str(entry.get('room_type', '')),
                str(entry.get('meal_plan', '')),
                str(entry.get('price', 0)),
                str(entry.get('start_date', '')),
                str(entry.get('end_date', ''))
            ]
            key = '|'.join(key_parts)
            
            if key not in seen_keys:
                seen_keys.add(key)
                unique_entries.append(entry)
        
        # Update statistics
        self.aggregation_stats['duplicates_removed'] = len(entries) - len(unique_entries)
        
        return unique_entries
    
    def _add_derived_meal_plans(self, entries: List[Dict[str, Any]], 
                               extra_charges: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Add derived meal plans based on supplements
        
        Args:
            entries: List of entries to process
            extra_charges: Dictionary of extra charges
            
        Returns:
            List of entries with derived meal plans
        """
        result_entries = entries.copy()
        derived_entries = []
        
        # Get meal plan supplements
        map_supplement = extra_charges.get('map_supplement', 0)
        ap_supplement = extra_charges.get('ap_supplement', 0)
        
        # Process each entry
        for entry in entries:
            room_type = entry.get('room_type', '')
            meal_plan = entry.get('meal_plan', '')
            price = entry.get('price', 0)
            
            # Skip non-CP entries and entries with zero or negative price
            if meal_plan != 'cp' or price <= 0:
                continue
            
            # Check if we already have MAP and AP entries for this room type and date range
            start_date = entry.get('start_date', '')
            end_date = entry.get('end_date', '')
            
            # Check for existing MAP entry
            has_map = any(e.get('room_type') == room_type and 
                         e.get('meal_plan') == 'map' and 
                         e.get('start_date') == start_date and 
                         e.get('end_date') == end_date 
                         for e in result_entries)
            
            # Check for existing AP entry
            has_ap = any(e.get('room_type') == room_type and 
                        e.get('meal_plan') == 'ap' and 
                        e.get('start_date') == start_date and 
                        e.get('end_date') == end_date 
                        for e in result_entries)
            
            # Add MAP entry if needed
            if map_supplement > 0 and not has_map:
                map_entry = entry.copy()
                map_entry['meal_plan'] = 'map'
                map_entry['price'] = price + map_supplement
                map_entry['is_derived'] = True
                map_entry['confidence'] = min(entry.get('confidence', 1.0), 0.9)  # Slightly lower confidence
                derived_entries.append(map_entry)
                self.aggregation_stats['derived_meal_plans_added'] += 1
            
            # Add AP entry if needed
            if ap_supplement > 0 and not has_ap:
                ap_entry = entry.copy()
                ap_entry['meal_plan'] = 'ap'
                ap_entry['price'] = price + ap_supplement
                ap_entry['is_derived'] = True
                ap_entry['confidence'] = min(entry.get('confidence', 1.0), 0.9)  # Slightly lower confidence
                derived_entries.append(ap_entry)
                self.aggregation_stats['derived_meal_plans_added'] += 1
        
        # Add derived entries to result
        result_entries.extend(derived_entries)
        
        return result_entries
    
    def _add_extra_charges(self, entries: List[Dict[str, Any]], 
                          extra_charges: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Add extra charges to entries
        
        Args:
            entries: List of entries to process
            extra_charges: Dictionary of extra charges
            
        Returns:
            List of entries with extra charges
        """
        result_entries = []
        
        # Get extra charges
        extra_adult_cp = extra_charges.get('extra_adult_cp', 0)
        extra_adult_map = extra_charges.get('extra_adult_map', 0)
        extra_adult_ap = extra_charges.get('extra_adult_ap', 0)
        extra_child_with_bed = extra_charges.get('extra_child_with_bed', 0)
        extra_child_without_bed = extra_charges.get('extra_child_without_bed', 0)
        map_supplement = extra_charges.get('map_supplement', 0)
        ap_supplement = extra_charges.get('ap_supplement', 0)
        
        # Process each entry
        for entry in entries:
            meal_plan = entry.get('meal_plan', '')
            
            # Add extra adult charge based on meal plan
            if meal_plan == 'cp' and extra_adult_cp > 0:
                entry['extra_adult_price'] = extra_adult_cp
                self.aggregation_stats['extra_charges_added'] += 1
            elif meal_plan == 'map':
                if extra_adult_map > 0:
                    entry['extra_adult_price'] = extra_adult_map
                    self.aggregation_stats['extra_charges_added'] += 1
                elif extra_adult_cp > 0 and map_supplement > 0:
                    # Derive MAP extra adult from CP extra adult and MAP supplement
                    entry['extra_adult_price'] = extra_adult_cp + map_supplement
                    entry['extra_adult_is_derived'] = True
                    self.aggregation_stats['extra_charges_added'] += 1
            elif meal_plan == 'ap':
                if extra_adult_ap > 0:
                    entry['extra_adult_price'] = extra_adult_ap
                    self.aggregation_stats['extra_charges_added'] += 1
                elif extra_adult_cp > 0 and ap_supplement > 0:
                    # Derive AP extra adult from CP extra adult and AP supplement
                    entry['extra_adult_price'] = extra_adult_cp + ap_supplement
                    entry['extra_adult_is_derived'] = True
                    self.aggregation_stats['extra_charges_added'] += 1
            
            # Add extra child charges
            if extra_child_with_bed > 0:
                entry['extra_child_with_bed_price'] = extra_child_with_bed
                self.aggregation_stats['extra_charges_added'] += 1
            
            if extra_child_without_bed > 0:
                entry['extra_child_without_bed_price'] = extra_child_without_bed
                self.aggregation_stats['extra_charges_added'] += 1
            
            result_entries.append(entry)
        
        return result_entries
    
    def _validate_and_fix_entries(self, entries: List[Dict[str, Any]], 
                                 room_types: List[str], 
                                 date_ranges: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate and fix entries
        
        Args:
            entries: List of entries to validate
            room_types: List of valid room types
            date_ranges: List of valid date ranges
            
        Returns:
            List of validated and fixed entries
        """
        validated_entries = []
        
        for entry in entries:
            has_warning = False
            
            # Check for missing room type
            if not entry.get('room_type'):
                entry['warnings'] = entry.get('warnings', [])
                entry['warnings'].append({
                    'message': 'Missing room type',
                    'severity': 'WARNING'
                })
                has_warning = True
                self.aggregation_stats['entries_with_missing_data'] += 1
            
            # Check for missing meal plan
            if not entry.get('meal_plan'):
                entry['meal_plan'] = 'cp'  # Default to CP
                entry['warnings'] = entry.get('warnings', [])
                entry['warnings'].append({
                    'message': 'Missing meal plan, defaulted to CP',
                    'severity': 'INFO'
                })
                has_warning = True
                self.aggregation_stats['entries_with_missing_data'] += 1
            
            # Check for missing price
            if not entry.get('price') or entry.get('price') <= 0:
                entry['warnings'] = entry.get('warnings', [])
                entry['warnings'].append({
                    'message': 'Invalid or missing price',
                    'severity': 'ERROR'
                })
                has_warning = True
                self.aggregation_stats['entries_with_missing_data'] += 1
                continue  # Skip entries with invalid price
            
            # Check for missing date range
            if not entry.get('start_date') or not entry.get('end_date'):
                # Try to add global date range if available
                if date_ranges:
                    global_date_range = next((dr for dr in date_ranges if dr.get('is_global', False)), None)
                    if global_date_range:
                        entry['start_date'] = global_date_range.get('start_date')
                        entry['end_date'] = global_date_range.get('end_date')
                        entry['warnings'] = entry.get('warnings', [])
                        entry['warnings'].append({
                            'message': 'Missing date range, used global date range',
                            'severity': 'INFO'
                        })
                        has_warning = True
                        self.aggregation_stats['entries_with_missing_data'] += 1
                    else:
                        # Use first date range if no global date range
                        entry['start_date'] = date_ranges[0].get('start_date')
                        entry['end_date'] = date_ranges[0].get('end_date')
                        entry['warnings'] = entry.get('warnings', [])
                        entry['warnings'].append({
                            'message': 'Missing date range, used first available date range',
                            'severity': 'WARNING'
                        })
                        has_warning = True
                        self.aggregation_stats['entries_with_missing_data'] += 1
                else:
                    entry['warnings'] = entry.get('warnings', [])
                    entry['warnings'].append({
                        'message': 'Missing date range and no global date range available',
                        'severity': 'ERROR'
                    })
                    has_warning = True
                    self.aggregation_stats['entries_with_missing_data'] += 1
                    continue  # Skip entries with missing date range and no global date range
            
            # Update statistics
            if has_warning:
                self.aggregation_stats['entries_with_warnings'] += 1
            
            # Add validated entry
            validated_entries.append(entry)
        
        return validated_entries
    
    def get_aggregation_stats(self) -> Dict[str, int]:
        """
        Get statistics about aggregation operations
        
        Returns:
            Dictionary of aggregation statistics
        """
        return self.aggregation_stats

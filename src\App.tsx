import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Auth from './pages/Auth';
import Hotels from './pages/Hotels';
import Dashboard from './pages/Dashboard';
import Vehicles from './pages/Vehicles';
import Amenities from './pages/Amenities';
import Packages from './pages/Packages';
import AddHotels from './pages/AddHotels';
import { Toaster } from 'react-hot-toast';
import { QueryClient, QueryClientProvider } from 'react-query';
import { DataProvider } from './utils/context/AppContext';
import HotelDetails from './pages/HotelDetails';
import RoomDetails from './pages/RoomDetails';
import ProtectedRoute from './protectiveRouter';
import EditHotel from './pages/EditHotel';
import { Provider } from 'react-redux';
import { store } from './store/store';
import EditRoom from './pages/EditRoom';
import Activity from './pages/Activity';
import Layout from './pages/Layout';
import PackageFormPage from './components/package/PackageFormPage';
import ActivityFormPage from './components/activity/ActivityFormPage';
import CouponsAdd from './components/coupons/CouponsAdd';
import Coupons from './pages/Coupons';
import Bookings from './pages/Bookings';
import PriceIssues from './pages/PriceIssues';

const queryClient = new QueryClient();

const App = () => {
  return (
    <>
    <Provider store={store}>
      <DataProvider>
          <QueryClientProvider client={queryClient}>
            <Toaster />

            <Router>
              <Routes>
                <Route element={<ProtectedRoute />}>
                  <Route element={<Layout/>}>
                  <Route path='/hotels' element={<Hotels />} />
                  <Route path='/hotels/add' element={<AddHotels />} />
                  <Route path='/dashboard' element={<Dashboard />} />
                  <Route path='/vehicles' element={<Vehicles />} />
                  <Route path='/activities' element={<Activity />} />
                  <Route path='/activity/add' element={<ActivityFormPage />} />
                  <Route path='/activity/:activityId/edit/' element={<ActivityFormPage />} />
                  <Route path='/packages' element={<Packages />} />
                  <Route path='/packages/add' element={<PackageFormPage />} />
                  <Route path='/package/:id/edit/' element={<PackageFormPage />}/>
                  <Route path='/amenities' element={<Amenities />} />
                  <Route path='/hotels/edit/:hotelId' element={<EditHotel/>}/>
                  <Route path='/hotels/edit/:hotelId/:hotelRoomId' element={<EditHotel/>}/>
                  <Route path='/hotels/edit/:hotelId/:hotelRoomId/:hotelMealId' element={<EditHotel/>}/>
                  <Route path='/hotels/:id' element={<HotelDetails />} />
                  <Route
                    path='/hotels/:id/room/:id2'
                    element={<RoomDetails />}
                  />
                  <Route path='/coupons/add' element={<CouponsAdd/>} />
                  <Route path='/coupons/' element={<Coupons/>} />
                  <Route path='/bookings' element={<Bookings/>} />
                  <Route path='/hotels/hotelRoom/:hotelRoomId/edit' element={<EditRoom/>} />
                  <Route path='/price-issues' element={<PriceIssues />} />
                  </Route>
                  

                </Route>
        

                <Route path='/' element={<Auth />} />
              </Routes>
            </Router>
          </QueryClientProvider>
      </DataProvider>
      </Provider>
    </>
  );
};

export default App;

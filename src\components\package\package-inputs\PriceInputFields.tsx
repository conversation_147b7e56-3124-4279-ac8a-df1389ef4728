/* eslint-disable @typescript-eslint/no-explicit-any */
import PackageContext from "@/utils/context/PackageContext"
import { useContext } from "react"
import { input_field_css, label_css } from "../PackageForm"

export default function PriceInputFields() {
    const {offer,setoffer,marketingPer,setMarketingPer,transPer,setTransPer,agentCommission,setAgentCommission,gstPer,setGstPer
      }:any = useContext(PackageContext)
  return (
    <div className="border-2 m-2">
          <div className="flex">
        <div className=" m-1">
          <label htmlFor="marketing-per" className={label_css}>MarketingPer</label>
          <input type="number" min={0} value={marketingPer} onInput={(e:any)=>setMarketingPer(e.target.value)} id="marketing-per" className={input_field_css} placeholder="No. of Days..."/>
        </div>
        <div className=" m-1">
          <label htmlFor="trans-per" className={label_css}>TransPer</label>
          <input type="number" min={0} value={transPer} onInput={(e:any)=>setTransPer(e.target.value)} id="trans-per" className={input_field_css} placeholder="No. of Nights..."/>
        </div>
        </div>
        <div className="flex">
        <div className=" m-1">
          <label htmlFor="agent-c" className={label_css}>Agent Commission</label>
          <input type="number" min={0} value={agentCommission} onInput={(e:any)=>setAgentCommission(e.target.value)} id="agent-c" className={input_field_css} placeholder="No. of Adults..."/>
        </div>
        <div className=" m-1">
          <label htmlFor="gst" className={label_css}>GST per</label>
          <input type="number" min={0} value={gstPer} onInput={(e:any)=>setGstPer(e.target.value)} id="gst" className={input_field_css} placeholder="No. of Child..."/>
        </div>
        </div>

        <div className=" m-1">
          <label htmlFor="offer" className={label_css}>Offer</label>
          <input type="number" min={0} value={offer} onInput={(e:any)=>setoffer(e.target.value)} id="offer" className={input_field_css} placeholder="Enter offer..."/>
        </div>
        </div>
  )
}

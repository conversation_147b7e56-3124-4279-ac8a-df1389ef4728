import { HotelUploadData } from "@/types/types";
import toast from "react-hot-toast";
import api from '../auth';

export async function updateHotel( data : HotelUploadData, hotelId : string) {
    try {
        const response = await api.put(
            `admin/hotel/${hotelId}`,
          data
        );
        toast.success('Updated Hotel Data')
        return Promise.resolve(response);
      } catch (error) {
        toast.error('An Error occurred');
        return Promise.reject('error');
      }
}
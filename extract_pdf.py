#!/usr/bin/env python3
"""
PDF Tariff Extractor for Tripmilestone

This script extracts tariff data from hotel PDF files.
It handles different formats and extracts room type, meal plan, date range, and price.
Includes OCR fallback for scanned PDFs.

Usage:
    python extract_pdf.py <pdf_file_path>

Output:
    JSON array of extracted tariff data
"""

import sys
import os
import json
import re
import io
import logging
from datetime import datetime
import pdfplumber
import pandas as pd

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('tariff_extractor')

def clean_text(text):
    """Clean and normalize text"""
    if not text:
        return ""
    # Replace multiple spaces with a single space
    text = re.sub(r'\s+', ' ', text)
    # Remove leading/trailing whitespace
    return text.strip()

def normalize_meal_plan(meal_plan):
    """Normalize meal plan to standard format (ep, cp, map, ap)"""
    meal_plan = meal_plan.lower()

    # Map common variations to standard formats
    if re.search(r'ep|european|room only|without meals', meal_plan):
        return 'ep'
    elif re.search(r'cp|continental|breakfast|bed and breakfast|b&b', meal_plan):
        return 'cp'
    elif re.search(r'map|american|half board|breakfast.*dinner|dinner.*breakfast', meal_plan):
        return 'map'
    elif re.search(r'ap|all inclusive|full board|all meals', meal_plan):
        return 'ap'

    # Default to EP if unknown
    return 'ep'

def parse_date(date_str):
    """Parse date string into standard format (YYYY-MM-DD)"""
    date_str = clean_text(date_str)

    # Try different date formats
    date_formats = [
        '%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y',
        '%m/%d/%Y', '%m-%d-%Y', '%m.%d.%Y',
        '%d/%m/%y', '%d-%m-%y', '%d.%m.%y',
        '%m/%d/%y', '%m-%d-%y', '%m.%d.%y',
        '%d %b %Y', '%d %B %Y',
        '%b %d, %Y', '%B %d, %Y'
    ]

    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt).strftime('%Y-%m-%d')
        except ValueError:
            continue

    # If all formats fail, return the original string
    return date_str

def extract_price(price_str):
    """Extract numeric price from string"""
    if not price_str:
        return 0

    # Extract all numbers from the string
    numbers = re.findall(r'[\d,]+\.?\d*', str(price_str))
    if not numbers:
        return 0

    # Take the first number and convert to float
    price = numbers[0].replace(',', '')
    try:
        return float(price)
    except ValueError:
        return 0

def try_ocr_extraction(pdf_path):
    """
    Try to extract text using OCR for scanned PDFs
    This is a placeholder - in a real implementation, you would:
    1. Use pytesseract or another OCR library
    2. Process the image to improve OCR accuracy
    3. Extract structured data from the OCR text
    """
    try:
        # Import OCR libraries only when needed
        try:
            import pytesseract
            from pdf2image import convert_from_path
            from PIL import Image
        except ImportError:
            logger.warning("OCR libraries not installed. Install with: pip install pytesseract pdf2image pillow")
            return []

        logger.info(f"Attempting OCR extraction for {pdf_path}")

        # Convert PDF to images
        images = convert_from_path(pdf_path)

        results = []
        for i, image in enumerate(images):
            # Perform OCR on the image
            text = pytesseract.image_to_string(image)

            # Look for patterns in the OCR text
            # This is a simplified example - real implementation would be more complex

            # Look for meal plan patterns
            meal_plans = {
                'ep': re.findall(r'(?i)ep|european plan|room only', text),
                'cp': re.findall(r'(?i)cp|continental|breakfast', text),
                'map': re.findall(r'(?i)map|american|half board', text),
                'ap': re.findall(r'(?i)ap|all inclusive|full board', text)
            }

            # Look for date ranges
            date_ranges = re.findall(r'(\d{1,2}[/-\.]\d{1,2}[/-\.]\d{2,4})\s*(?:to|-|till|until)\s*(\d{1,2}[/-\.]\d{1,2}[/-\.]\d{2,4})', text)

            # Look for prices
            prices = re.findall(r'(?:rs\.?|inr|₹)\s*(\d[\d,]*\.?\d*)', text, re.IGNORECASE)

            # Create entries based on found patterns
            for meal_type, instances in meal_plans.items():
                if not instances:
                    continue

                for i, (start, end) in enumerate(date_ranges[:3]):  # Limit to first 3 date ranges
                    if i < len(prices):
                        price = extract_price(prices[i])
                        if price > 0:
                            results.append({
                                "mealPlanType": meal_type,
                                "startDate": parse_date(start),
                                "endDate": parse_date(end),
                                "roomPrice": price
                            })

        logger.info(f"OCR extraction found {len(results)} potential tariff entries")
        return results
    except Exception as e:
        logger.error(f"OCR extraction failed: {str(e)}")
        return []

def map_meal_plan_headers(header_row):
    """Map header names to meal plan types (ep, cp, map, ap) and ignore rack rate columns."""
    plan_map = {}
    for idx, header in enumerate(header_row):
        header_clean = str(header).strip().lower()
        if re.search(r'rack\s*rate', header_clean):
            continue  # Ignore rack rate columns
        if re.search(r'ep\b|european', header_clean):
            plan_map['ep'] = idx
        elif re.search(r'cp\b|continental|breakfast', header_clean):
            plan_map['cp'] = idx
        elif re.search(r'map\b|half board|american', header_clean):
            plan_map['map'] = idx
        elif re.search(r'ap\b|all inclusive|full board', header_clean):
            plan_map['ap'] = idx
        # Add more mappings as needed
    return plan_map

def extract_from_tables(pdf_path):
    """Extract tariff data from tables in PDF"""
    results = []
    extraction_success = False

    try:
        with pdfplumber.open(pdf_path) as pdf:
            logger.info(f"Processing PDF with {len(pdf.pages)} pages")

            for page_num, page in enumerate(pdf.pages):
                logger.info(f"Processing page {page_num+1}")
                tables = page.extract_tables()

                if not tables:
                    logger.info(f"No tables found on page {page_num+1}")
                    continue

                logger.info(f"Found {len(tables)} tables on page {page_num+1}")

                for table_idx, table in enumerate(tables):
                    # Skip empty tables
                    if not table or len(table) <= 1:
                        logger.info(f"Skipping empty table {table_idx+1} on page {page_num+1}")
                        continue

                    header_row = table[0]
                    plan_map = map_meal_plan_headers(header_row)

                    # Identify room type column
                    room_col = -1
                    for idx, header in enumerate(header_row):
                        if re.search(r'room|type|category|accommodation|view|cottage', str(header).lower()):
                            room_col = idx
                            break
                    if room_col == -1:
                        room_col = 0  # fallback

                    # Identify date columns (try to find two for start/end)
                    date_cols = []
                    for idx, header in enumerate(header_row):
                        if re.search(r'date|period|from|to|valid', str(header).lower()):
                            date_cols.append(idx)
                    # fallback: no date columns

                    # Process each row
                    row_count = 0
                    for row in table[1:]:
                        # Skip empty or header-like rows
                        if not any(str(val).strip() for val in row):
                            continue
                        row_text = ' '.join(str(val).lower() for val in row)
                        if 'rack rate' in row_text:
                            logger.info(f"Skipping rack rate row: {row_text[:50]}...")
                            continue
                        room_type = clean_text(str(row[room_col])) if room_col < len(row) else ''
                        # For each meal plan column, extract price
                        for plan, idx in plan_map.items():
                            if idx < len(row):
                                price = extract_price(row[idx])
                                if price > 0:
                                    # Extract dates if available
                                    start_date, end_date = '', ''
                                    if len(date_cols) >= 2:
                                        start_date = parse_date(str(row[date_cols[0]]))
                                        end_date = parse_date(str(row[date_cols[1]]))
                                    elif len(date_cols) == 1:
                                        date_range = str(row[date_cols[0]])
                                        date_parts = re.split(r'\s*to\s*|\s*-\s*|\s*till\s*|\s*until\s*', date_range)
                                        if len(date_parts) >= 2:
                                            start_date = parse_date(date_parts[0])
                                            end_date = parse_date(date_parts[1])
                                    results.append({
                                        "roomType": room_type,
                                        "mealPlanType": plan,
                                        "startDate": start_date,
                                        "endDate": end_date,
                                        "roomPrice": price
                                    })
                                    row_count += 1
                    logger.info(f"Extracted {row_count} tariff entries from table {table_idx+1}")
                    if row_count > 0:
                        extraction_success = True

        # If no data was extracted from tables, try OCR as fallback
        if not extraction_success and len(results) == 0:
            logger.info("No data extracted from tables, attempting OCR fallback")
            ocr_results = try_ocr_extraction(pdf_path)
            if ocr_results:
                results.extend(ocr_results)
                logger.info(f"OCR extraction added {len(ocr_results)} entries")

    except Exception as e:
        logger.error(f"Error extracting tables: {str(e)}")
        # Try OCR as fallback if table extraction fails completely
        ocr_results = try_ocr_extraction(pdf_path)
        if ocr_results:
            results.extend(ocr_results)
            logger.info(f"OCR fallback extraction added {len(ocr_results)} entries after table extraction failure")

    logger.info(f"Total extracted entries: {len(results)}")
    return results

def main():
    if len(sys.argv) < 2:
        logger.error("Usage: python extract_pdf.py <pdf_file_path>")
        print("Usage: python extract_pdf.py <pdf_file_path>")
        sys.exit(1)

    pdf_path = sys.argv[1]

    if not os.path.exists(pdf_path):
        logger.error(f"Error: File {pdf_path} not found")
        print(f"Error: File {pdf_path} not found")
        sys.exit(1)

    logger.info(f"Starting extraction from {pdf_path}")

    try:
        # Extract data from the PDF
        results = extract_from_tables(pdf_path)

        # Add room type if missing (using filename as fallback)
        filename = os.path.basename(pdf_path)
        room_type_guess = re.sub(r'[_\-\d]', ' ', os.path.splitext(filename)[0]).strip()

        for result in results:
            if 'roomType' not in result or not result['roomType']:
                result['roomType'] = room_type_guess

        # Output the results as JSON
        print(json.dumps(results, indent=2))
        logger.info(f"Extraction complete. Found {len(results)} entries.")

    except Exception as e:
        error_msg = f"Error extracting data: {str(e)}"
        logger.error(error_msg)
        print(error_msg, file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()

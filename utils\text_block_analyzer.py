#!/usr/bin/env python3
"""
Text Block Analyzer for PDF Extraction

This module provides functions to identify and categorize text blocks in PDF content,
helping to target extraction efforts for non-tabular data.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Set
from collections import defaultdict

# Configure logging
logger = logging.getLogger('tariff_extractor')

def identify_text_blocks(full_text: str, page_texts: Optional[List[str]] = None) -> Dict[str, Dict[str, Any]]:
    """
    Identify and categorize text blocks in PDF content
    
    Args:
        full_text: The full text content of the PDF
        page_texts: Optional list of text content for each page
        
    Returns:
        Dictionary mapping block types to block information
    """
    blocks = {}
    
    # If we have individual page texts, use them for better context
    if page_texts:
        # Header info is usually on the first page
        if len(page_texts) > 0:
            header_info = _extract_header_info(page_texts[0])
            if header_info:
                blocks['Header_Info'] = header_info
                
        # Footer info is usually on the last page
        if len(page_texts) > 0:
            footer_info = _extract_footer_info(page_texts[-1])
            if footer_info:
                blocks['Footer_Info'] = footer_info
    else:
        # Extract from full text if page texts not available
        header_info = _extract_header_info(full_text)
        if header_info:
            blocks['Header_Info'] = header_info
            
        footer_info = _extract_footer_info(full_text)
        if footer_info:
            blocks['Footer_Info'] = footer_info
    
    # Extract date validity information
    date_validity = _extract_date_validity(full_text)
    if date_validity:
        blocks['Date_Validity_Overall'] = date_validity
        
    # Extract meal plan definitions
    meal_plan_info = _extract_meal_plan_info(full_text)
    if meal_plan_info:
        blocks['Meal_Plan_Definitions'] = meal_plan_info
        
    # Extract extra charges information
    extra_charges = _extract_extra_charges(full_text)
    if extra_charges:
        blocks['Extra_Charges_Section'] = extra_charges
        
    # Extract cancellation policy
    cancellation_policy = _extract_cancellation_policy(full_text)
    if cancellation_policy:
        blocks['Cancellation_Policy'] = cancellation_policy
        
    # Extract general terms and conditions
    terms = _extract_terms_and_conditions(full_text)
    if terms:
        blocks['General_Terms'] = terms
        
    return blocks

def _extract_header_info(text: str) -> Dict[str, Any]:
    """Extract header information from text"""
    # First few lines are usually header
    lines = text.split('\n')
    header_lines = lines[:min(5, len(lines))]
    header_text = '\n'.join(header_lines)
    
    # Try to extract hotel name
    hotel_name = None
    hotel_patterns = [
        r'(?:hotel|resort|inn|lodge|villa|cottage)\s+([A-Za-z0-9\s&]+)',
        r'([A-Za-z0-9\s&]+)(?:hotel|resort|inn|lodge|villa|cottage)'
    ]
    
    for pattern in hotel_patterns:
        match = re.search(pattern, header_text, re.I)
        if match:
            hotel_name = match.group(1).strip()
            break
            
    # Try to extract location
    location = None
    location_patterns = [
        r'(?:at|in)\s+([A-Za-z\s]+)',
        r'([A-Za-z\s]+)(?:tariff|rate card|price list)'
    ]
    
    for pattern in location_patterns:
        match = re.search(pattern, header_text, re.I)
        if match:
            location = match.group(1).strip()
            break
            
    return {
        'text': header_text,
        'hotel_name': hotel_name,
        'location': location
    }

def _extract_footer_info(text: str) -> Dict[str, Any]:
    """Extract footer information from text"""
    # Last few lines are usually footer
    lines = text.split('\n')
    footer_lines = lines[-min(5, len(lines)):]
    footer_text = '\n'.join(footer_lines)
    
    # Try to extract contact information
    contact_info = None
    contact_patterns = [
        r'(?:tel|phone|contact|call)(?::|.)?(?:\s+)?([\d\s+-]+)',
        r'(?:email|e-mail|mail)(?::|.)?(?:\s+)?([a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+)'
    ]
    
    for pattern in contact_patterns:
        match = re.search(pattern, footer_text, re.I)
        if match:
            contact_info = match.group(1).strip()
            break
            
    return {
        'text': footer_text,
        'contact_info': contact_info
    }

def _extract_date_validity(text: str) -> Dict[str, Any]:
    """Extract date validity information from text"""
    validity_blocks = []
    
    # Look for validity sections
    validity_patterns = [
        r'(?:valid(?:ity)?|applicable)(?:\s+from|\s+for|\s+during)?\s+([^\n.]+)',
        r'(?:tariff|rate|price)(?:\s+valid|\s+applicable)(?:\s+from|\s+for|\s+during)?\s+([^\n.]+)',
        r'(?:period|season)(?:\s+of)?\s+(?:validity|applicability)(?:\s+from|\s+for|\s+during)?\s+([^\n.]+)',
        r'(?:valid|applicable)(?:\s+for|\s+during)?\s+(?:the\s+)?(?:period|season)(?:\s+of)?\s+([^\n.]+)'
    ]
    
    for pattern in validity_patterns:
        matches = re.finditer(pattern, text, re.I)
        for match in matches:
            validity_text = match.group(1).strip()
            
            # Extract the context (surrounding text)
            start_pos = max(0, match.start() - 50)
            end_pos = min(len(text), match.end() + 50)
            context = text[start_pos:end_pos].strip()
            
            validity_blocks.append({
                'text': validity_text,
                'context': context,
                'position': match.start()
            })
            
    # Look for date ranges
    date_range_patterns = [
        r'(\d{1,2}[./-]\d{1,2}[./-]\d{2,4})\s*(?:to|-|till|until|through)\s*(\d{1,2}[./-]\d{1,2}[./-]\d{2,4})',
        r'(\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4})\s*(?:to|-|till|until|through)\s*(\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4})'
    ]
    
    for pattern in date_range_patterns:
        matches = re.finditer(pattern, text, re.I)
        for match in matches:
            start_date = match.group(1).strip()
            end_date = match.group(2).strip()
            
            # Extract the context (surrounding text)
            start_pos = max(0, match.start() - 50)
            end_pos = min(len(text), match.end() + 50)
            context = text[start_pos:end_pos].strip()
            
            validity_blocks.append({
                'text': f"{start_date} to {end_date}",
                'start_date': start_date,
                'end_date': end_date,
                'context': context,
                'position': match.start()
            })
            
    # Sort by position in text
    validity_blocks.sort(key=lambda x: x['position'])
    
    return {
        'blocks': validity_blocks
    }

def _extract_meal_plan_info(text: str) -> Dict[str, Any]:
    """Extract meal plan information from text"""
    meal_plan_blocks = []
    
    # Look for meal plan definitions
    meal_plan_patterns = [
        r'(?:CP|Continental Plan|Continental)(?:\s+includes|\s+means|\s+-|\s+:)?\s+([^\n.]+)',
        r'(?:MAP|Modified American Plan|Modified American)(?:\s+includes|\s+means|\s+-|\s+:)?\s+([^\n.]+)',
        r'(?:AP|American Plan|American)(?:\s+includes|\s+means|\s+-|\s+:)?\s+([^\n.]+)',
        r'(?:EP|European Plan|European)(?:\s+includes|\s+means|\s+-|\s+:)?\s+([^\n.]+)',
        r'(?:meal plan|meal plans|meal)(?:\s+includes|\s+means|\s+-|\s+:)?\s+([^\n.]+)'
    ]
    
    for pattern in meal_plan_patterns:
        matches = re.finditer(pattern, text, re.I)
        for match in matches:
            definition = match.group(1).strip()
            
            # Extract the context (surrounding text)
            start_pos = max(0, match.start() - 50)
            end_pos = min(len(text), match.end() + 50)
            context = text[start_pos:end_pos].strip()
            
            # Determine meal plan type
            meal_plan_type = 'unknown'
            if re.search(r'CP|Continental Plan|Continental', match.group(0), re.I):
                meal_plan_type = 'cp'
            elif re.search(r'MAP|Modified American Plan|Modified American', match.group(0), re.I):
                meal_plan_type = 'map'
            elif re.search(r'AP|American Plan|American', match.group(0), re.I):
                meal_plan_type = 'ap'
            elif re.search(r'EP|European Plan|European', match.group(0), re.I):
                meal_plan_type = 'ep'
                
            meal_plan_blocks.append({
                'type': meal_plan_type,
                'definition': definition,
                'context': context,
                'position': match.start()
            })
            
    # Look for meal plan supplements
    supplement_patterns = [
        r'(?:MAP|Modified American Plan|Modified American)(?:\s+supplement|\s+extra|\s+additional)(?:\s+charge)?(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
        r'(?:AP|American Plan|American)(?:\s+supplement|\s+extra|\s+additional)(?:\s+charge)?(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
        r'(?:supplement|extra|additional)(?:\s+charge)?(?:\s+for)?\s+(?:MAP|Modified American Plan|Modified American)(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
        r'(?:supplement|extra|additional)(?:\s+charge)?(?:\s+for)?\s+(?:AP|American Plan|American)(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
    ]
    
    for pattern in supplement_patterns:
        matches = re.finditer(pattern, text, re.I)
        for match in matches:
            amount = match.group(1).strip()
            
            # Extract the context (surrounding text)
            start_pos = max(0, match.start() - 50)
            end_pos = min(len(text), match.end() + 50)
            context = text[start_pos:end_pos].strip()
            
            # Determine meal plan type
            meal_plan_type = 'unknown'
            if re.search(r'MAP|Modified American Plan|Modified American', match.group(0), re.I):
                meal_plan_type = 'map'
            elif re.search(r'AP|American Plan|American', match.group(0), re.I):
                meal_plan_type = 'ap'
                
            meal_plan_blocks.append({
                'type': meal_plan_type,
                'supplement': amount,
                'context': context,
                'position': match.start()
            })
            
    # Sort by position in text
    meal_plan_blocks.sort(key=lambda x: x['position'])
    
    return {
        'blocks': meal_plan_blocks
    }

def _extract_extra_charges(text: str) -> Dict[str, Any]:
    """Extract extra charges information from text"""
    extra_charge_blocks = []
    
    # Look for extra person charges
    extra_person_patterns = [
        r'(?:extra|additional)(?:\s+adult|\s+person)(?:\s+charge)?(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
        r'(?:extra|additional)(?:\s+adult|\s+person)(?:\s+on)?\s+(?:CP|Continental Plan|Continental)(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
        r'(?:extra|additional)(?:\s+adult|\s+person)(?:\s+on)?\s+(?:MAP|Modified American Plan|Modified American)(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
        r'(?:extra|additional)(?:\s+adult|\s+person)(?:\s+on)?\s+(?:AP|American Plan|American)(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
    ]
    
    for pattern in extra_person_patterns:
        matches = re.finditer(pattern, text, re.I)
        for match in matches:
            amount = match.group(1).strip()
            
            # Extract the context (surrounding text)
            start_pos = max(0, match.start() - 50)
            end_pos = min(len(text), match.end() + 50)
            context = text[start_pos:end_pos].strip()
            
            # Determine charge type and meal plan
            charge_type = 'extra_adult'
            meal_plan = 'cp'  # Default
            
            if re.search(r'MAP|Modified American Plan|Modified American', match.group(0), re.I):
                meal_plan = 'map'
            elif re.search(r'AP|American Plan|American', match.group(0), re.I):
                meal_plan = 'ap'
                
            extra_charge_blocks.append({
                'type': charge_type,
                'meal_plan': meal_plan,
                'amount': amount,
                'context': context,
                'position': match.start()
            })
            
    # Look for extra child charges
    extra_child_patterns = [
        r'(?:extra|additional)(?:\s+child)(?:\s+charge)?(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
        r'(?:extra|additional)(?:\s+child)(?:\s+with)?\s+(?:bed)(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)',
        r'(?:extra|additional)(?:\s+child)(?:\s+without)?\s+(?:bed)(?:\s+:|\s+-|\s+is)?\s*(?:Rs\.?|INR|₹)?\s*(\d[\d,]*\.?\d*)'
    ]
    
    for pattern in extra_child_patterns:
        matches = re.finditer(pattern, text, re.I)
        for match in matches:
            amount = match.group(1).strip()
            
            # Extract the context (surrounding text)
            start_pos = max(0, match.start() - 50)
            end_pos = min(len(text), match.end() + 50)
            context = text[start_pos:end_pos].strip()
            
            # Determine charge type
            charge_type = 'extra_child'
            if re.search(r'with\s+bed', match.group(0), re.I):
                charge_type = 'extra_child_with_bed'
            elif re.search(r'without\s+bed', match.group(0), re.I):
                charge_type = 'extra_child_without_bed'
                
            extra_charge_blocks.append({
                'type': charge_type,
                'amount': amount,
                'context': context,
                'position': match.start()
            })
            
    # Sort by position in text
    extra_charge_blocks.sort(key=lambda x: x['position'])
    
    return {
        'blocks': extra_charge_blocks
    }

def _extract_cancellation_policy(text: str) -> Dict[str, Any]:
    """Extract cancellation policy information from text"""
    cancellation_blocks = []
    
    # Look for cancellation policy sections
    cancellation_patterns = [
        r'(?:cancellation|cancel)(?:\s+policy|\s+policies|\s+charges|\s+terms)(?:[^\n.]+)',
        r'(?:policy|policies|terms)(?:\s+for)?\s+(?:cancellation|cancel)(?:[^\n.]+)'
    ]
    
    for pattern in cancellation_patterns:
        matches = re.finditer(pattern, text, re.I)
        for match in matches:
            # Extract the context (surrounding text)
            start_pos = max(0, match.start() - 100)
            end_pos = min(len(text), match.end() + 200)
            context = text[start_pos:end_pos].strip()
            
            cancellation_blocks.append({
                'text': match.group(0).strip(),
                'context': context,
                'position': match.start()
            })
            
    # Sort by position in text
    cancellation_blocks.sort(key=lambda x: x['position'])
    
    return {
        'blocks': cancellation_blocks
    }

def _extract_terms_and_conditions(text: str) -> Dict[str, Any]:
    """Extract general terms and conditions from text"""
    terms_blocks = []
    
    # Look for terms and conditions sections
    terms_patterns = [
        r'(?:terms|conditions|terms\s+and\s+conditions|terms\s+&\s+conditions)(?:[^\n.]+)',
        r'(?:general|hotel)(?:\s+terms|\s+conditions|\s+terms\s+and\s+conditions|\s+terms\s+&\s+conditions)(?:[^\n.]+)'
    ]
    
    for pattern in terms_patterns:
        matches = re.finditer(pattern, text, re.I)
        for match in matches:
            # Extract the context (surrounding text)
            start_pos = max(0, match.start() - 100)
            end_pos = min(len(text), match.end() + 200)
            context = text[start_pos:end_pos].strip()
            
            terms_blocks.append({
                'text': match.group(0).strip(),
                'context': context,
                'position': match.start()
            })
            
    # Sort by position in text
    terms_blocks.sort(key=lambda x: x['position'])
    
    return {
        'blocks': terms_blocks
    }

import { ChangeEvent, useState } from "react";
import { IoIosHome } from "react-icons/io";

interface SearchHeaderProps {
  handleSearch: (inp:string) => void;
}
export default function SearchHeader({handleSearch}:SearchHeaderProps) {
  const [inputText,setInputText] = useState("")
  return (
    <header className="flex items-center justify-between">
<div className="flex gap-2">
  <a href="/hotels">
  <IoIosHome className="w-[35px] h-[35px] ml-2"/>
  </a>
  <input
    type="text"
    className="border w-[300px] ml-2 text-xl px-2 py-1"
    placeholder="Search Hotel..."
    value={inputText}
    onInput={(e:ChangeEvent<HTMLInputElement>)=>setInputText(e.target.value)}
  />
  <button className="bg-blue-500 px-2 " onClick={()=>handleSearch(inputText)}>Search</button>
</div>
<a href={"/hotels/add"}>
  <button className="w-[200px] py-4 bg-black text-white m-2 rounded-lg">
    Create Hotel
  </button>
</a>
</header>
  )
}

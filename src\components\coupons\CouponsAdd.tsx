import React from 'react'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import api from '../../utils/api-functions/auth'
import { yupResolver } from "@hookform/resolvers/yup";

import { COUPON_URL } from '@/utils/urls/urls';
import toast from 'react-hot-toast';

const CouponsAdd: React.FC = () => {
  const couponSchema = yup.object().shape({
    couponName: yup.string().required('Coupon name is required'),
    isPublic: yup.boolean().required('isPublic field is required'),
    userId: yup.string().nullable(),
    valueType: yup
      .string()
      .oneOf(['percentage', 'fixed'], 'Value type must be either percentage or fixed')
      .required('Value type is required'),
    value: yup.number().positive('Value must be a positive number').required('Value is required'),
    validDate: yup.date().required('Valid date is required')
  });

  const { register, handleSubmit, formState: { errors }, reset } = useForm({
    resolver: yupResolver(couponSchema),
    mode: "onBlur",
    reValidateMode: "onChange",
    shouldFocusError: true
  });

  const onSubmit = async(values : yup.InferType<typeof couponSchema>) => {
   try { 
    const response = await api.post(COUPON_URL,values)
    const data = response.data;
    if(data){
        toast.success('Added Coupon')
    }
    reset();
    
   } catch (error) {
    console.log(error)
    toast.error('Error adding coupon')
    
   }
  };

  return (
    <div className="max-w-md  p-5 ">
      <h2 className="text-xl font-semibold text-neutral-600 mb-4">Create Coupon</h2>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <label className="block mb-1" htmlFor="couponName">Coupon Name</label>
          <input
            type="text"
            id="couponName"
            className={`border rounded px-3 py-2 w-full ${errors.couponName ? 'border-red-500' : 'border-appprimary'}`}
            {...register('couponName')}
          />
          {errors.couponName && <p className="text-red-500 text-sm">{errors.couponName.message}</p>}
        </div>

        <div className="mb-4">
          <label className="block mb-1">Is Public</label>
          <select
            className={`border rounded px-3 py-2 w-full ${errors.isPublic ? 'border-red-500' : 'border-appprimary'}`}
            {...register('isPublic')}
          >
            <option value="">Select</option>
            <option value="true">Yes</option>
            <option value="false">No</option>
          </select>
          {errors.isPublic && <p className="text-red-500 text-sm">{errors.isPublic.message}</p>}
        </div>

        <div className="mb-4">
          <label className="block mb-1" htmlFor="userId">User ID (Optional)</label>
          <input
            type="text"
            id="userId"
            className={`border rounded px-3 py-2 w-full ${errors.userId ? 'border-red-500' : 'border-appprimary'}`}
            {...register('userId')}
          />
          {errors.userId && <p className="text-red-500 text-sm">{errors.userId.message}</p>}
        </div>

        <div className="mb-4">
          <label className="block mb-1" htmlFor="valueType">Value Type</label>
          <select
            id="valueType"
            className={`border rounded px-3 py-2 w-full ${errors.valueType ? 'border-red-500' : 'border-appprimary'}`}
            {...register('valueType')}
          >
            <option value="">Select</option>
            <option value="percentage">Percentage</option>
            <option value="fixed">Fixed Amount</option>
          </select>
          {errors.valueType && <p className="text-red-500 text-sm">{errors.valueType.message}</p>}
        </div>

        <div className="mb-4">
          <label className="block mb-1" htmlFor="value">Value</label>
          <input
            type="number"
            id="value"
            className={`border rounded px-3 py-2 w-full ${errors.value ? 'border-red-500' : 'border-appprimary'}`}
            {...register('value')}
          />
          {errors.value && <p className="text-red-500 text-sm">{errors.value.message}</p>}
        </div>

        <div className="mb-4">
          <label className="block mb-1" htmlFor="validDate">Valid Date</label>
          <input
            type="date"
            id="validDate"
            className={`border rounded px-3 py-2 w-full ${errors.validDate ? 'border-red-500' : 'border-appprimary'}`}
            {...register('validDate')}
          />
          {errors.validDate && <p className="text-red-500 text-sm">{errors.validDate.message}</p>}
        </div>

        <button type="submit" className="bg-appprimary text-white rounded px-4 py-2">Submit</button>
      </form>
    </div>
  );
}

export default CouponsAdd;

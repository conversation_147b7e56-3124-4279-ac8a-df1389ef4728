/* eslint-disable @typescript-eslint/no-explicit-any */
import PackageContext from '@/utils/context/PackageContext';
import { ChangeEvent, ReactElement, useContext, useState } from 'react';
import PackageSelect from './package-inputs/packageSelect';
import PackageMultiSelect from './package-inputs/PackageMultiSelect';
import NoInputFields from './package-inputs/NoInputFields';
import HotelFields from './package-inputs/HotelFields';
import AddImage from './package-inputs/AddImage';
import PackageActivity from './package-activity/PackageActivity';
import PackagePrices from './package-prices/PackagePrices';
import { useParams } from 'react-router-dom';
import Loading from './Loading';
import PackageDelete from './PackageDelete';
import PackageSearchSelect from './package-inputs/PackageSearchSelect';
// Removed unused import
import toast from 'react-hot-toast';
import { packageTitlesData } from '@/utils/constants/packageTitleData';
import EnhancedHotelSelect from './package-inputs/EnhancedHotelSelect';
import EnhancedDateSelect from './package-inputs/EnhancedDateSelect';
import EnhancedVehicleMultiSelect from './package-inputs/EnhancedVehicleMultiSelect';
import EnhancedActivityMultiSelect from './package-inputs/EnhancedActivityMultiSelect';
import EnhancedDestinationSelect from './package-inputs/EnhancedDestinationSelect';
import NumberDropdown from './package-inputs/NumberDropdown';
export const input_field_css = 'text-base border border-gray-300 rounded-md w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm';
export const label_css = 'text-sm font-medium text-gray-700 mb-1';
export default function PackageForm() {
  const {
    packageName,
    setPackageName,
    plan,
    setPlanId,
    destinationIds,
    interest,sort,setSort,
    setInterestId, dayCount,setDayCount,
    setDestinationId,status,
    addDestination,removeDestination,setStatus,perRoom,setPerRoom,redeemPoints,setRedeemPoints,
    destination,allVehicles,availableHotels,vehicles,availableVehicles,inclusion,exclusion,availableActivity,
    allHotels,setAvailableHotels,setVehicles,setAvailableVehicles,  startsFrom,setStartsFrom,setStartsFromId,
    allInclusion,allExclusion,allActivity,setInclusion,setExclusion,setAvailableActivity,disableClone,
    setStory,story,handleSubmit,disableSubmit,packageId,setDates,deleteDates,period,planId,interestId,isLoading,destinationName,setDestinationName,
    hotelSaveData, packageImages
  }: any = useContext(PackageContext);


  const [packageImageComp,setPackageImageComp] = useState<ReactElement[]>([
    <AddImage />,
  ])

  function handleAddImage(){
      setPackageImageComp((pre)=>[...pre,<AddImage />])
  }
  const [isInputFocused,setInputFocused] = useState(false)
  const [packageNames,setPackageNames] = useState(packageTitlesData)
  function handleChangeRoonType(inp:string){
    setInputFocused(true)
    const data = packageTitlesData.filter((k)=>{
      return k.toLowerCase()?.includes(inp.toLowerCase())
    })
    setPackageNames(data);
    setPackageName(inp)
  }
  function handleInputText(type:string){
    setPackageName(type)
    setInputFocused(false)
  }
  const {id} = useParams()
  return !isLoading ?(
    <>
      <div className="flex">
        <div className="w-1/4 border pb-2 flex flex-col">
          {/* <div className=" p-2 m-1">
            <label htmlFor="package-name" className={label_css}>
              Package Name
            </label>
            <input
              type="text"
              value={packageName}
              onInput={(e: any) => setPackageName(e.target.value)}
              id="package-name"
              className={input_field_css}
              placeholder="Enter Package Name..."
            />
          </div> */}
          <div className="relative flex flex-col p-3">
            <label htmlFor="package-name" className={label_css}>
              Package Name <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="text"
                id="package-name"
                placeholder="e.g Romantic Manali Honeymoon: A 3N Blissful Retreat"
                className={`${input_field_css} ${!packageName ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}`}
                value={packageName}
                onInput={(e:ChangeEvent<HTMLInputElement>) => handleChangeRoonType(e.target.value)}
                onFocus={() => setInputFocused(true)}
                onBlur={() => setTimeout(() => setInputFocused(false), 200)}
                required
              />
              {packageName && (
                <button
                  type="button"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  onClick={() => {
                    setPackageName('');
                    setInputFocused(false);
                  }}
                >
                  ×
                </button>
              )}
            </div>

            {isInputFocused && packageNames.length > 0 && (
              <div className="absolute top-[70px] left-0 right-0 z-30 mx-3 bg-white border border-gray-200 rounded-md shadow-lg max-h-[300px] overflow-y-auto">
                <div className="p-2 border-b border-gray-100 text-xs text-gray-500">
                  Suggested package names:
                </div>
                {packageNames.map((type) => (
                  <div
                    key={type}
                    onMouseDown={() => handleInputText(type)}
                    className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
                  >
                    {type}
                  </div>
                ))}
              </div>
            )}
          </div>

          <PackageSelect
          defaultValue={planId}
            name="Plan Id"
            apiData={plan}
            dataId="planId"
            dataName="planName"
            setId={setPlanId}
          />
          <PackageSelect
          defaultValue={interestId}
            name="Interest Id"
            apiData={interest}
            dataId="interestId"
            dataName="interestName"
            setId={setInterestId}
          />
          <div className="mb-4">
            <div className={label_css + " mb-2"}>Destinations</div>

            {/* Selected Destinations */}
            {destinationIds?.length > 0 ? (
              <div className="mb-3 space-y-2">
                {destinationIds?.map((k: any) => (
                  <div
                    key={k.destinationId}
                    className="flex justify-between items-center px-3 py-2 bg-blue-50 border border-blue-200 rounded-md"
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-2 text-xs font-medium">
                        {k?.destinationName.charAt(0)}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-800">{k?.destinationName}</div>
                        <div className="text-xs text-gray-500">
                          {k?.noOfNight} {k?.noOfNight === 1 ? 'night' : 'nights'}
                        </div>
                      </div>
                    </div>
                    <button
                      className="text-red-500 hover:text-red-700"
                      onClick={() => removeDestination(k.destinationId)}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-gray-500 text-center py-3 border border-dashed border-gray-300 rounded-md mb-3">
                No destinations added. Use the form below to add destinations.
              </div>
            )}

            {/* Enhanced Destination Select */}
            <EnhancedDestinationSelect
              setDataName={setDestinationName}
              inputName={destinationName}
              allData={destination}
              dataName="destinationName"
              dataId="destinationId"
              setData={setDestinationId}
              pHolder="Select destination"
              dayCount={dayCount}
              setDayCount={setDayCount}
              addDestination={addDestination}
            />
          </div>
          <NoInputFields />
          <div className="px-2">
          <div className={label_css}>Story</div>
          <input type="text" className={input_field_css} placeholder="Enter story URL.." value={story} onInput={(e:any)=>setStory(e.target.value)} />
          </div>
          <div className="mx-2">
          <PackageSearchSelect
            setDataName={setStartsFrom}
            inputName={startsFrom}
            allData={destination}
            dataName="destinationName"
            dataId="destinationId"
            setData={setStartsFromId}
            pHolder="Starting destination"
          />
          </div>
        </div>
        <section className="w-2/4 border ">
          <div className="">
            <div className="text-sm font-bold mx-2 mt-2">Hotels</div>

           <HotelFields />

          </div>
          <div className="flex flex-col md:flex-row">
            <div className="w-full md:w-1/2 p-2">
              {/* Enhanced Hotel Selection */}
              <EnhancedHotelSelect
                initialData={availableHotels}
                allData={allHotels}
                setDatas={setAvailableHotels}
              />

              <div className="mt-4">
                <div className={label_css}>Available Activities</div>
                <EnhancedActivityMultiSelect
                  initialData={availableActivity}
                  allData={allActivity}
                  dataName="name"
                  dataId="activityId"
                  pHolder="Select Available Activities"
                  setDatas={setAvailableActivity}
                />
              </div>

              <div className="mt-4">
                <div className={label_css}>Inclusion</div>
                <PackageMultiSelect
                  initialData={inclusion}
                  allData={allInclusion}
                  dataName="name"
                  dataId="inclusionId"
                  pHolder="Select Inclusions"
                  setDatas={setInclusion}
                />
              </div>
            </div>

            <div className="w-full md:w-1/2 p-2">
              <div className="">
                <div className={label_css}>Vehicles</div>
                <EnhancedVehicleMultiSelect
                  initialData={vehicles}
                  allData={allVehicles}
                  dataName="vehicleName"
                  dataId="vehicleId"
                  pHolder="Select Vehicles"
                  setDatas={setVehicles}
                />
              </div>

              <div className="mt-4">
                <div className={label_css}>Available Vehicles</div>
                <EnhancedVehicleMultiSelect
                  initialData={availableVehicles}
                  allData={allVehicles}
                  dataName="vehicleName"
                  dataId="vehicleId"
                  pHolder="Select Available Vehicles"
                  setDatas={setAvailableVehicles}
                />
              </div>

              <div className="mt-4">
                <div className={label_css}>Exclusion</div>
                <PackageMultiSelect
                  initialData={exclusion}
                  allData={allExclusion}
                  dataName="name"
                  dataId="exclusionId"
                  pHolder="Select Exclusions"
                  setDatas={setExclusion}
                />
              </div>
            </div>
          </div>
        </section>
        <section className="w-1/4 border ">
        <PackagePrices />
          <div className="">
            <div className={label_css}>Package Images <span className="text-red-500">*</span></div>
            <div className="">
          {packageImageComp?.map((k,i)=>(
            <div className="" key={i}>{k}</div>
          ))}
          <button
            onClick={handleAddImage}
            className="flex items-center bg-yellow-500 hover:bg-yellow-600 transition-colors duration-200 text-white px-3 py-2 mx-1 rounded-md shadow-sm"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className="text-xs font-medium">Add Image</span>
          </button>
            </div>
          </div>

          <div className=" p-2 m-1">
            <label htmlFor="package-id" className={label_css}>
              Package Id
            </label>
            <input
              type="text"
              value={packageId}
              id="package-id"
              disabled={true}
              className={input_field_css}
            />
          </div>

          <EnhancedDateSelect setDates={setDates} deleteDates={deleteDates} period={period}/>

          <div className="p-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="publish-status"
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                value={1}
                checked={status}
                onChange={() => setStatus(!status)}
              />
              <label htmlFor="publish-status" className="ml-2 text-sm font-medium text-gray-700">
                Publish Package
              </label>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              {status ? 'Package will be visible to users' : 'Package will be hidden from users'}
            </p>
          </div>
            <div className="grid grid-cols-2 gap-4 p-2">
              <NumberDropdown
                label="Sort"
                value={sort}
                onChange={setSort}
                min={0}
                max={20}
                defaultValue={0}
                id="package-sort"
              />

              <NumberDropdown
                label="Per Room"
                value={perRoom}
                onChange={setPerRoom}
                min={0}
                max={10}
                defaultValue={0}
                id="per-room"
              />
            </div>

            <div className="p-3">
              <label htmlFor="redeem-points" className={label_css}>
                Redeem Points
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={redeemPoints}
                  onInput={(e:any) => setRedeemPoints(e.target.valueAsNumber)}
                  id="redeem-points"
                  className={input_field_css}
                  placeholder="Enter redeem points"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>


          <div className="my-6 flex flex-col items-center gap-3">
            {/* Form validation message */}
            <div className="mb-2 text-sm text-gray-600">
              <p>All fields marked with * are required</p>
            </div>

            <div className="flex gap-3">
              <button
                disabled={disableSubmit}
                onClick={() => {
                  // Basic validation before submitting
                  if (!packageName) {
                    toast.error("Package name is required");
                    return;
                  }
                  if (destinationIds.length === 0) {
                    toast.error("At least one destination is required");
                    return;
                  }
                  if (hotelSaveData.length === 0) {
                    toast.error("At least one hotel is required");
                    return;
                  }
                  if (packageImages.length === 0) {
                    toast.error("At least one package image is required");
                    return;
                  }
                  if (period.length === 0) {
                    toast.error("At least one date period is required");
                    return;
                  }

                  handleSubmit(id ? "update" : "create");
                }}
                className={`
                  flex items-center justify-center px-4 py-2 rounded-md shadow-sm transition-all duration-200 text-sm
                  ${!disableSubmit
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-blue-300 cursor-not-allowed text-white'}
                `}
              >
                {id ? (
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                )}
                <span className="font-medium">{id ? "Update" : "Create"}</span>
              </button>

              {id && (
                <>
                  <PackageDelete packageName={packageName} packageId={id} />

                  <button
                    disabled={disableClone}
                    onClick={() => {
                      // Basic validation before cloning
                      if (!packageName) {
                        toast.error("Package name is required");
                        return;
                      }
                      if (destinationIds.length === 0) {
                        toast.error("At least one destination is required");
                        return;
                      }
                      if (hotelSaveData.length === 0) {
                        toast.error("At least one hotel is required");
                        return;
                      }
                      if (packageImages.length === 0) {
                        toast.error("At least one package image is required");
                        return;
                      }
                      if (period.length === 0) {
                        toast.error("At least one date period is required");
                        return;
                      }

                      handleSubmit("clone");
                    }}
                    className={`
                      flex items-center justify-center px-4 py-2 rounded-md shadow-sm transition-all duration-200 text-sm
                      ${!disableClone
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-green-300 cursor-not-allowed text-white'}
                    `}
                  >
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                    </svg>
                    <span className="font-medium">Clone</span>
                  </button>
                </>
              )}
            </div>
          </div>
        </section>
      </div>
      <PackageActivity/>

    </>
  ):<>
  <>
    <Loading />
  </>
  </>
}

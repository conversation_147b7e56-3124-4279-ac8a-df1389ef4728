/* eslint-disable @typescript-eslint/no-explicit-any */
import PackageContext from '@/utils/context/PackageContext';
import { ChangeEvent, useContext, useState } from 'react';
import PackageSelect from './package-inputs/packageSelect';
import PackageMultiSelect from './package-inputs/PackageMultiSelect';
import NoInputFields from './package-inputs/NoInputFields';
import HotelFields from './package-inputs/HotelFields';

import PackageActivity from './package-activity/PackageActivity';
import PackagePrices from './package-prices/PackagePrices';
import { useParams } from 'react-router-dom';
import Loading from './Loading';
import PackageDelete from './PackageDelete';
import PackageSearchSelect from './package-inputs/PackageSearchSelect';
import SelectPackageDates from './package-inputs/SelectPackageDates';
import { packageTitlesData } from '@/utils/constants/packageTitleData';
export const input_field_css = 'text-xl border-2 rounded-lg w-full px-2';
export const label_css = 'text-xs font-bold';
export default function PackageForm() {
  const {
    packageName,
    setPackageName,
    plan,
    setPlanId,
    destinationIds,
    interest,sort,setSort,
    setInterestId, dayCount,setDayCount,
    destinationId,setDestinationId,status,
    addDestination,removeDestination,setStatus,perRoom,setPerRoom,redeemPoints,setRedeemPoints,
    destination,allVehicles,availableHotels,vehicles,availableVehicles,inclusion,exclusion,availableActivity,
    allHotels,setAvailableHotels,setVehicles,setAvailableVehicles,  startsFrom,setStartsFrom,setStartsFromId,
    allInclusion,allExclusion,allActivity,setInclusion,setExclusion,setAvailableActivity,disableClone,
    setStory,story,handleSubmit,disableSubmit,packageId,setDates,deleteDates,period,planId,interestId,isLoading,destinationName,setDestinationName
  }: any = useContext(PackageContext);



  const [isInputFocused,setInputFocused] = useState(false)
  const [packageNames,setPackageNames] = useState(packageTitlesData)
  function handleChangeRoonType(inp:string){
    setInputFocused(true)
    const data = packageTitlesData.filter((k)=>{
      return k.toLowerCase()?.includes(inp.toLowerCase())
    })
    setPackageNames(data);
    setPackageName(inp)
  }
  function handleInputText(type:string){
    setPackageName(type)
    setInputFocused(false)
  }
  const {id} = useParams()
  return !isLoading ?(
    <>
      <div className="flex">
        <div className="w-1/4 border pb-2 flex flex-col">
          {/* <div className=" p-2 m-1">
            <label htmlFor="package-name" className={label_css}>
              Package Name
            </label>
            <input
              type="text"
              value={packageName}
              onInput={(e: any) => setPackageName(e.target.value)}
              id="package-name"
              className={input_field_css}
              placeholder="Enter Package Name..."
            />
          </div> */}
          <div className="relative flex flex-col px-2">
            <label htmlFor="room-type" className={label_css}>
              Package Name
           
            </label>
            <input
              type="text"
              id="room-type"
              placeholder="e.g Romantic Manali Honeymoon: A 3N Blissful Retreat"
              className=" border text-md px-2 py-1 rounded  placeholder-slate-400"
              value={packageName}
              onInput={(e:ChangeEvent<HTMLInputElement>)=>handleChangeRoonType(e.target.value)}
              onBlur={()=>setInputFocused(false)}
            />
            {
              isInputFocused&& <div className="shadow-2xl rounded-lg max-h-[300px] overflow-y-auto p-1 absolute top-[70px] w-full z-30 bg-white">
              {
                packageNames?.length >0 ?
                packageNames?.map((type)=>{
                 return <div key={type} onMouseDown={()=>handleInputText(type)} className="px-2 py-1 hover:bg-gray-200 cursor-pointer rounded-md">{type}</div>
                }):""
              }
                </div>
            }

            </div>

          <PackageSelect
          defaultValue={planId}
            name="Plan Id"
            apiData={plan}
            dataId="planId"
            dataName="planName"
            setId={setPlanId}
          />
          <PackageSelect
          defaultValue={interestId}
            name="Interest Id"
            apiData={interest}
            dataId="interestId"
            dataName="interestName"
            setId={setInterestId}
          />
          <div className="">
            <div className={label_css}>Destination</div>
          {
          destinationIds?.map((k:any)=>
          <div className=" flex justify-between mx-2 px-2 bg-blue-500 rounded-md mb-1 text-white">
            <div className="flex gap-2">
            <div className="font-bold">{k?.destinationName}</div>
            <div className="">days :{k?.noOfNight}</div>
            </div>
            
            <button className="font-bold" onClick={()=> removeDestination(k.destinationId)}>x</button>
          </div>

          )}
            <div className="flex mx-1 items-end gap-1 border ">
            <PackageSearchSelect
            setDataName={setDestinationName}
            inputName={destinationName}
            allData={destination}
            dataName="destinationName"
            dataId="destinationId"
            setData={setDestinationId}
            pHolder="Select destination"
          />
          <div className="">
          <div className={label_css}>Day Count</div>
          <input type="number" name="" id="" value={dayCount} onInput={(e:ChangeEvent<HTMLInputElement>)=>setDayCount(e.target.valueAsNumber)} className={input_field_css+" h-9 w-[100px]"} />
          </div>
          <button className=" m-1 w-[50px] h-[40px] bg-green-500 rounded-sm" onClick={()=>addDestination(destinationName,destinationId,dayCount)}>+</button>

          </div>  
          </div>
          <NoInputFields />
          <div className="px-2">
          <div className={label_css}>Story</div>
          <input type="text" className={input_field_css} placeholder="Enter story URL.." value={story} onInput={(e:any)=>setStory(e.target.value)} />
          </div>
          <div className="mx-2">
          <PackageSearchSelect
            setDataName={setStartsFrom}
            inputName={startsFrom}
            allData={destination}
            dataName="destinationName"
            dataId="destinationId"
            setData={setStartsFromId}
            pHolder="Starting destination"
          />
          </div>
        </div>
        <section className="w-2/4 border ">
          <div className="">
            <div className="text-sm font-bold mx-2 mt-2">Hotels</div>
           
           <HotelFields />
         
          </div>
          <div className="flex">
            <div className="w-1/2 p-2">
              <div className="">
                <div className={label_css}>Available Hotels</div>
                <PackageMultiSelect initialData={availableHotels} allData={allHotels} dataName="hotelName" dataId="hotelId" pHolder="Select Available Hotels" setDatas={setAvailableHotels}/>
              </div>
            
              <div className="">
                <div className={label_css}>Available Activities</div>
                <PackageMultiSelect initialData={availableActivity} allData={allActivity} dataName="name" dataId="activityId" pHolder="Select Available Activities" setDatas={setAvailableActivity}/>
              </div>
              <div className="">
                <div className={label_css}>Inclusion</div>
                <PackageMultiSelect initialData={inclusion} allData={allInclusion} dataName="name" dataId="inclusionId" pHolder="Select Inclusions" setDatas={setInclusion}/>
              </div>
             
            </div>
            <div className="w-1/2 p-2">
            <div className="">
                <div className={label_css}>Vehicles</div>
                <PackageMultiSelect initialData={vehicles} allData={allVehicles} dataName="vehicleName" dataId="vehicleId" pHolder="Select Vehicles" setDatas={setVehicles}/>
              </div>
              <div className="">
                <div className={label_css}>Available Vehicles</div>
                <PackageMultiSelect initialData={availableVehicles} allData={allVehicles} dataName="vehicleName" dataId="vehicleId" pHolder="Select Available Vehicles" setDatas={setAvailableVehicles}/>
              </div>
            
              <div className="">
                <div className={label_css}>Exclusion</div>
                <PackageMultiSelect initialData={exclusion} allData={allExclusion} dataName="name" dataId="exclusionId" pHolder="Select Exclusions" setDatas={setExclusion}/>
              </div>

            </div>
          </div>
        </section>
        <section className="w-1/4 border ">
        <PackagePrices />


          <div className=" p-2 m-1">
            <label htmlFor="package-id" className={label_css}>
              Package Id
            </label>
            <input
              type="text"
              value={packageId}
              id="package-id"
              disabled={true}
              className={input_field_css}
            />
          </div>
          
          <SelectPackageDates setDates={setDates} deleteDates={deleteDates} period={period}/>

          <div className="flex items-center gap-2 px-2 m-1">
            <div className={label_css}>Publish Status : </div>
              <input type="checkbox" className='w-[25px] h-[25px]' id="isAc" name="isAc" value={1} defaultChecked={status} onChange={()=>setStatus(!status)}/>
            </div>
            <div className="flex">
            <div className="w-1/2">
          <div className="flex flex-col mx-2"><div className={label_css}>Sort</div><input type="number" className={input_field_css} value={sort} onInput={(e:ChangeEvent<HTMLInputElement>)=>setSort(e.target.valueAsNumber)}/></div>

            </div>
            <div className="w-1/2">
            <div className="flex flex-col mx-2"><div className={label_css}>perRoom</div><input type="number" className={input_field_css} value={perRoom} onInput={(e:ChangeEvent<HTMLInputElement>)=>setPerRoom(e.target.valueAsNumber)}/></div>

            </div>
            </div>

            <div className=" p-2 m-1">
            <label htmlFor="redeem-points" className={label_css}>
              Redeem Points
            </label>
            <input
              type="number"
              value={redeemPoints}
              onInput={(e:any)=>setRedeemPoints(e.target.valueAsNumber)}
              id="redeem-points"
              className={input_field_css}
            />
          </div>
            

          <div className="my-[30px] h-[60px] flex justify-center  gap-2">

            <button disabled={disableSubmit} onClick={()=>handleSubmit(id?"update":"create")} className={!disableSubmit?'bg-blue-700 text-white px-6  rounded-lg shadow-lg':'bg-blue-500 cursor-not-allowed text-white px-6 py-3 rounded-lg shadow-lg'}>{id?"Update":"Create"}</button>
            {
              id? <>
              <PackageDelete packageName={packageName} packageId={id}/>
            <button disabled={disableClone} onClick={()=>handleSubmit("clone")} className={!disableClone?'bg-lime-500 text-white px-6  rounded-lg shadow-lg':'bg-lime-300 cursor-not-allowed text-white px-6 py-3 rounded-lg shadow-lg'}>Clone</button>
              </>:<></>
            }
          </div>
        </section>
      </div>
      <PackageActivity/>

    </>
  ):<>
  <>
    <Loading />
  </>
  </>
}

import { ColumnDef } from "@tanstack/react-table"
import { GoPencil } from "react-icons/go"
import { Link } from "react-router-dom"
import {
  handleDate,
  isEndDatePast,
  isWithin10Days
} from "@/components/page-components/hotel-details/room/mealPlan/handlingDates"

// Define the Package type based on the API response
export type Package = {
  _id: string
  packageId: string
  packageName: string
  destinationName: string
  planName: string
  interestName: string
  hotelCount: number
  availableHotelCount: number
  activityCount: number
  availableActivityCount: number
  period: {
    startDate: string
    endDate: string
  }[]
}

export const columns: ColumnDef<Package>[] = [
  {
    accessorKey: "packageName",
    header: () => <div className="text-left font-semibold">Package Name</div>,
    cell: ({ row }) => (
      <div className="font-medium text-blue-800">
        {row.getValue("packageName")}
      </div>
    ),
  },
  {
    accessorKey: "destinationName",
    header: () => <div className="text-left font-semibold">Destinations</div>,
    cell: ({ row }) => (
      <div className="text-gray-700">
        {row.getValue("destinationName")}
      </div>
    ),
  },
  {
    accessorKey: "planName",
    header: () => <div className="text-center font-semibold">Plan</div>,
    cell: ({ row }) => (
      <div className="text-center px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium inline-block">
        {row.getValue("planName")}
      </div>
    ),
  },
  {
    accessorKey: "interestName",
    header: () => <div className="text-center font-semibold">Interest</div>,
    cell: ({ row }) => (
      <div className="text-center px-2 py-1 bg-green-50 text-green-700 rounded-full text-xs font-medium inline-block">
        {row.getValue("interestName")}
      </div>
    ),
  },
  {
    accessorKey: "hotelCount",
    header: () => <div className="text-center font-semibold">Hotels</div>,
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.getValue("hotelCount")}
      </div>
    ),
  },
  {
    accessorKey: "availableHotelCount",
    header: () => <div className="text-center font-semibold">Avail. Hotels</div>,
    cell: ({ row }) => (
      <div className="text-center text-gray-600">
        {row.getValue("availableHotelCount")}
      </div>
    ),
  },
  {
    accessorKey: "activityCount",
    header: () => <div className="text-center font-semibold">Activities</div>,
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.getValue("activityCount")}
      </div>
    ),
  },
  {
    accessorKey: "availableActivityCount",
    header: () => <div className="text-center font-semibold">Avail. Activities</div>,
    cell: ({ row }) => (
      <div className="text-center text-gray-600">
        {row.getValue("availableActivityCount")}
      </div>
    ),
  },
  {
    accessorKey: "period",
    header: () => <div className="text-center font-semibold">Start/End Dates</div>,
    cell: ({ row }) => {
      const periods = row.getValue("period") as { startDate: string, endDate: string }[];

      return (
        <div className="text-xs space-y-2">
          {periods?.map((period, i) => {
            const startDatePast = isEndDatePast(period.startDate);
            const startDateSoon = isWithin10Days(period.startDate);
            const endDatePast = isEndDatePast(period.endDate);
            const endDateSoon = isWithin10Days(period.endDate);

            return (
              <div key={i} className="flex gap-1 justify-center items-center">
                <div
                  className={`px-2 py-1 rounded-md ${
                    startDatePast
                      ? "bg-red-50 text-red-700"
                      : startDateSoon
                      ? "bg-yellow-50 text-yellow-700"
                      : "bg-gray-50 text-gray-700"
                  } font-medium`}
                >
                  {handleDate(period.startDate)}
                </div>
                <span className="text-gray-400">→</span>
                <div
                  className={`px-2 py-1 rounded-md ${
                    endDatePast
                      ? "bg-red-50 text-red-700"
                      : endDateSoon
                      ? "bg-yellow-50 text-yellow-700"
                      : "bg-gray-50 text-gray-700"
                  } font-medium`}
                >
                  {handleDate(period.endDate)}
                </div>
              </div>
            );
          })}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: () => <div className="text-center font-semibold">Actions</div>,
    cell: ({ row }) => {
      const package_ = row.original;

      return (
        <div className="flex justify-center">
          <Link
            to={`/package/${package_.packageId}/edit/`}
            className="p-2 text-blue-600 hover:text-white hover:bg-blue-600 rounded-full transition-colors duration-200"
            title="Edit Package"
          >
            <GoPencil size={16} />
          </Link>
        </div>
      );
    },
  },
]

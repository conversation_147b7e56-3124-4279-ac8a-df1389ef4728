import { HOTEL_ROOM_GET_URL } from '../urls/urls';
import { RoomData } from '@/components/page-components/hotel-details/room/AddRoom';
import api from './auth'

export async function getHotelRooms(hotelId:string) {
  try {
    const URL = HOTEL_ROOM_GET_URL(hotelId);
    const response = await api.get(URL);

    return Promise.resolve(response.data.result);
  } catch (error) {
    console.log(error);
    return Promise.reject('error');
  }
}
export async function getHotelRoomById(hotelId:string,roomId:string) {
  const rooms = await getHotelRooms(hotelId);
  const filter = rooms.find((k:RoomData)=>k.hotelRoomId === roomId)

  return filter;
}
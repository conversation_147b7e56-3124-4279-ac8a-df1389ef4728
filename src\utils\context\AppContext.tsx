import { Room } from "@/components/page-components/hotel-details/room/AddRoom";
import { MealPlanData } from "@/components/page-components/hotel-details/room/mealPlan/AddMealPlan";
import { createContext, ReactNode } from "react";
import { useState } from "react";
const AppContext = createContext({});

export const DataProvider = ({ children }: { children: ReactNode }) => {
  const [showPopup, setShowPopup] = useState(false);
  const [message, setMessage] = useState("hi hello");
  const [status, setStatus] = useState("");
  const [selectedPage, setSelectedPage] = useState("");
  const [startDates, setStartDates] = useState<string[]>([]);
  const [endDates, setEndDates] = useState<string[]>([]);
  const [roomData, setRoomData] = useState<Room[]>([]);
  const [mealPlanData, setMealPlanData] = useState<MealPlanData[]>([]);
  function setDates(startDate: string, endDate: string) {
    setStartDates([ ...startDates,startDate]);
    setEndDates([...endDates,endDate]);
  }
  function deleteDates(startDate:string,endDate:string){
    setStartDates((pre)=>pre.filter((k)=>k!==startDate))
    setEndDates((pre)=>pre.filter((k)=>k!==endDate))
  }
  function handleRoomData(RoomData: Room) {
    setRoomData([RoomData, ...roomData]);
  }
  function handleRoomDelete(roomName: string) {
    setRoomData((prevRoomData) =>
      prevRoomData.filter((room) => room.hotelRoomType !== roomName)
    );
  }
  function handleMealPlanData(mealPlan: MealPlanData) {
    setMealPlanData([ ...mealPlanData,mealPlan]);
  }
  function handleMealPlanDelete(mealPlan: string) {
    setMealPlanData((prevRoomData) =>
      prevRoomData.filter((meal) => meal.mealPlan !== mealPlan)
    );
  }
  return (
    <AppContext.Provider
      value={{
        showPopup,
        setShowPopup,
        message,
        setMessage,
        status,
        setStatus,
        selectedPage,
        setSelectedPage,
        startDates,
        setStartDates,
        endDates,
        setEndDates,
        setDates,
        roomData,
        deleteDates,
        setRoomData,
        handleRoomData,
        handleRoomDelete,
        handleMealPlanData,
        handleMealPlanDelete,
        mealPlanData,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};
export default AppContext;

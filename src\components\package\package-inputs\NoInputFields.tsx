/* eslint-disable @typescript-eslint/no-explicit-any */
import PackageContext from "@/utils/context/PackageContext"
import { useContext } from "react"
import { input_field_css, label_css } from "../PackageForm"

export default function NoInputFields() {
    const {noDays,setNoDays,noNights,setNoNights,
        noAdults,setNoAdults,noChild,setNoChild
      }:any = useContext(PackageContext)
  return (
    <div className="border-2 m-2">
          <div className="flex">
        <div className=" m-1">
          <label htmlFor="days" className={label_css}>No. of Days</label>
          <input type="number" min={0} value={noDays} onInput={(e:any)=>setNoDays(e.target.value)} id="days" className={input_field_css} placeholder="No. of Days..."/>
        </div>
        <div className=" m-1">
          <label htmlFor="nights" className={label_css}>No. of Nights</label>
          <input type="number" min={0} value={noNights} onInput={(e:any)=>setNoNights(e.target.value)} id="nights" className={input_field_css} placeholder="No. of Nights..."/>
        </div>
        </div>
        <div className="flex">
        <div className=" m-1">
          <label htmlFor="adults" className={label_css}>No. of Adults</label>
          <input type="number" min={0} value={noAdults} onInput={(e:any)=>setNoAdults(e.target.value)} id="adults" className={input_field_css} placeholder="No. of Adults..."/>
        </div>
        <div className=" m-1">
          <label htmlFor="child" className={label_css}>No. of Child</label>
          <input type="number" min={0} value={noChild} onInput={(e:any)=>setNoChild(e.target.value)} id="child" className={input_field_css} placeholder="No. of Child..."/>
        </div>
        </div>

        </div>
  )
}

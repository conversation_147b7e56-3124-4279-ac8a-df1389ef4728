/* eslint-disable @typescript-eslint/no-explicit-any */
import PackageContext from "@/utils/context/PackageContext"
import { useContext } from "react"
// No imports needed from PackageForm
import NumberDropdown from "./NumberDropdown"

export default function NoInputFields() {
    const {noDays,setNoDays,noNights,setNoNights,
        noAdults,setNoAdults,noChild,setNoChild
      }:any = useContext(PackageContext)
  return (
    <div className="border-2 m-2 p-3">
      <div className="grid grid-cols-2 gap-4">
        <NumberDropdown
          label="No. of Days"
          value={Number(noDays) || 0}
          onChange={(value) => setNoDays(value)}
          min={0}
          max={10}
          defaultValue={0}
          id="days"
        />

        <NumberDropdown
          label="No. of Nights"
          value={Number(noNights) || 0}
          onChange={(value) => setNoNights(value)}
          min={0}
          max={10}
          defaultValue={0}
          id="nights"
        />

        <NumberDropdown
          label="No. of Adults"
          value={Number(noAdults) || 0}
          onChange={(value) => setNoAdults(value)}
          min={0}
          max={10}
          defaultValue={0}
          id="adults"
        />

        <NumberDropdown
          label="No. of Child"
          value={Number(noChild) || 0}
          onChange={(value) => setNoChild(value)}
          min={0}
          max={10}
          defaultValue={0}
          id="child"
        />
      </div>
    </div>
  )
}

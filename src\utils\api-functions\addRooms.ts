import { Room } from "@/components/page-components/hotel-details/room/AddRoom";
import api from './auth'
import toast from "react-hot-toast";

export async function handleAddRoomsApi(hotelId:string,rooms:Room[]) {
        try {
            const response = await api.post(
                `admin/hotel/${hotelId}/hotelRoom/create`,
              rooms
            );
        
            return Promise.resolve(response);
          } catch (error) {
            toast.error('An Error occurred');
            return Promise.reject('error');
          }
}
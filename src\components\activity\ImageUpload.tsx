/* eslint-disable @typescript-eslint/no-explicit-any */

import { useContext, useState } from "react";
import { label_css } from "../package/PackageForm";
import ActivityContext from "@/utils/context/ActivityContext";
import { handleImageUpload } from "@/utils/api-functions/upload-image";
import toast from "react-hot-toast";

export default function ImageUpload() {

    const {imageUrl,setImageUrl}:any = useContext(ActivityContext)
  const [image,setImage] = useState<File | null>()
  const [disableUpload,setDisableUpload] = useState(false)
    async function handleUpload(){
      setDisableUpload(true)
      if(image){
        try {
          const resp = await handleImageUpload("activity",image.type,image.name,image);  
          setImageUrl(resp)
          setImage(null)
        } catch (error) {
        setDisableUpload(false)
          console.log(error)
        }
         
          
      }else{
        toast.error("Please select an image")
        setDisableUpload(false)
      }
  }

  function handleImage(img:File){
    const supportedFormats = ['jpg','jpeg','png','webp']
    const format = img?.type.split('/')[1]
    console.log(img)
    if (supportedFormats?.includes(format as string)) {
      setImage(img)
    }else{
      if(format){
        toast.error(format+" is not supported")
        setImage(null)
      }
    }
  }

  function handleDeleteImage(){
    setImageUrl("")
    setDisableUpload(false)
  }
  return (
    <div className="flex flex-col">
      <label htmlFor="activity-img" className={label_css}>Image</label>
      {
        
        imageUrl !=="" && (
           <div className="text-sm py-3 bg-gray-200 my-1 px-2 flex justify-between">
            <a target="_blank" href={"https://tripemilestone.in-maa-1.linodeobjects.com/"+imageUrl} className="hover:underline">{imageUrl.split("-").pop()}</a>
            <button onClick={handleDeleteImage} className="w-[25px] h-[25px] rounded-full font-bold bg-red-500 text-white">X</button>
            </div>
        )
      }
      <div className="flex mt-2">
        <div className="w-full">

        <label htmlFor="activity-img" className={!imageUrl?"px-2 py-1 bg-lime-300":"px-2 py-1 bg-red-300"}>Choose Image</label>
        {
        image?.name && <span>&nbsp;&nbsp;{image?.name}</span>
        }
        <input disabled={imageUrl}  type="file" id="activity-img" className="hidden"  onChange={(e:any)=>handleImage(e.target.files[0])}/>
        </div>
        <button disabled={disableUpload} onClick={handleUpload} className={!disableUpload?"bg-green-500 w-[80px] rounded-r-full rounded-l-full":"bg-red-500 w-[80px] rounded-r-full rounded-l-full  cursor-not-allowed"}>Upload</button>
      </div>
    </div>
  )
}

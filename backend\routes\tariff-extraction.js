/**
 * Tariff Extraction Routes
 * Handles PDF upload and extraction for hotel tariffs
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const logger = require('../utils/logger');
const linodeStorage = require('../utils/linodeStorage');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/tariffs');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `${req.body.hotelId || 'hotel'}-${req.body.roomId || 'room'}-${uniqueSuffix}-${file.originalname}`);
  }
});

const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    // Accept only PDFs
    if (file.mimetype !== 'application/pdf') {
      return cb(new Error('Only PDF files are allowed'));
    }
    cb(null, true);
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

/**
 * Extract data from a tariff PDF with enhanced extraction
 *
 * POST /api/admin/hotel/tariff/extract-enhanced
 *
 * Request body:
 * - multipart/form-data with PDF file
 * - hotelId: string
 * - roomId: string
 *
 * Response:
 * {
 *   success: boolean,
 *   result: {
 *     extractedData: {
 *       roomTypes: string[],
 *       dateRanges: { startDate: string, endDate: string }[],
 *       prices: { roomType: string, mealPlanType: string, startDate: string, endDate: string, roomPrice: number }[],
 *       extraCharges: { type: string, amount: number }[]
 *     },
 *     filePath: string,
 *     confidence: number
 *   }
 * }
 */
router.post('/extract-enhanced', upload.single('pdfFile'), async (req, res) => {
  try {
    const { hotelId, roomId } = req.body;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No PDF file uploaded'
      });
    }

    // Log the extraction request
    logger.info(`[PDF_EXTRACTION] Starting enhanced extraction for hotel ${hotelId}, room ${roomId}`, {
      hotel_id: hotelId,
      room_id: roomId,
      file_path: req.file.path,
      file_name: req.file.originalname,
      file_size: req.file.size
    });

    // Upload the file to Linode Object Storage
    const remoteFilePath = `hotel-tariffs/${path.basename(req.file.path)}`;
    let fileUrl;

    try {
      fileUrl = await linodeStorage.uploadFile(req.file.path, remoteFilePath);
      logger.info(`[PDF_EXTRACTION] File uploaded to Linode: ${fileUrl}`, {
        hotel_id: hotelId,
        room_id: roomId,
        local_path: req.file.path,
        remote_path: remoteFilePath
      });
    } catch (uploadError) {
      logger.error(`[PDF_EXTRACTION] Error uploading file to Linode: ${uploadError.message}`, {
        error_stack: uploadError.stack,
        hotel_id: hotelId,
        room_id: roomId,
        local_path: req.file.path
      });

      // Continue with local file if upload fails
      logger.info(`[PDF_EXTRACTION] Continuing with local file: ${req.file.path}`);
      fileUrl = req.file.path;
    }

    // Run the Python extraction script
    const extractionResult = await runPythonExtraction(req.file.path, hotelId, roomId);

    // Return the extraction result
    return res.status(200).json({
      success: true,
      result: {
        extractedData: extractionResult,
        filePath: fileUrl,
        confidence: extractionResult.confidence || 0.8
      }
    });
  } catch (error) {
    logger.error(`[PDF_EXTRACTION] Error extracting data from PDF: ${error.message}`, {
      error_stack: error.stack
    });

    return res.status(500).json({
      success: false,
      message: 'Error extracting data from PDF',
      error: error.message
    });
  }
});

/**
 * Run the Python extraction script
 *
 * @param {string} filePath - Path to the PDF file
 * @param {string} hotelId - Hotel ID
 * @param {string} roomId - Room ID
 * @returns {Promise<Object>} - Extraction result
 */
async function runPythonExtraction(filePath, hotelId, roomId) {
  return new Promise((resolve, reject) => {
    // Path to the Python script
    const scriptPath = path.join(__dirname, '../scripts/phase4_post_processing.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      // Fall back to the original script if the advanced one doesn't exist
      const fallbackScriptPath = path.join(__dirname, '../extract_pdf.py');

      if (fs.existsSync(fallbackScriptPath)) {
        logger.info(`[PDF_EXTRACTION] Advanced script not found, using fallback: ${fallbackScriptPath}`);
        scriptPath = fallbackScriptPath;
      } else {
        logger.error(`[PDF_EXTRACTION] No extraction script found at ${scriptPath} or ${fallbackScriptPath}`);
        reject(new Error('PDF extraction script not found'));
        return;
      }
    }

    // Run the Python script
    const pythonProcess = spawn('python', [
      scriptPath,
      filePath,
      '--hotel-id', hotelId,
      '--room-id', roomId,
      '--format', 'rich'
    ]);

    let dataString = '';
    let errorString = '';

    // Collect data from stdout
    pythonProcess.stdout.on('data', (data) => {
      dataString += data.toString();
    });

    // Collect errors from stderr
    pythonProcess.stderr.on('data', (data) => {
      errorString += data.toString();
    });

    // Handle process completion
    pythonProcess.on('close', (code) => {
      if (code !== 0) {
        logger.error(`[PDF_EXTRACTION] Python process exited with code ${code}`, {
          error: errorString,
          hotel_id: hotelId,
          room_id: roomId,
          file_path: filePath
        });

        // If the process failed but we have some data, try to parse it
        if (dataString) {
          try {
            const result = JSON.parse(dataString);
            resolve(result);
            return;
          } catch (e) {
            // If parsing fails, reject with the error
            reject(new Error(`Python extraction failed: ${errorString}`));
            return;
          }
        }

        reject(new Error(`Python extraction failed: ${errorString}`));
        return;
      }

      try {
        // Parse the JSON output
        const result = JSON.parse(dataString);
        resolve(result);
      } catch (error) {
        logger.error(`[PDF_EXTRACTION] Error parsing Python output: ${error.message}`, {
          error_stack: error.stack,
          output: dataString.substring(0, 1000) // Log first 1000 chars of output
        });
        reject(new Error(`Error parsing extraction result: ${error.message}`));
      }
    });
  });
}

/**
 * Upload a PDF file to Linode Object Storage
 *
 * POST /api/admin/hotel/tariff-extraction/upload
 *
 * Request body:
 * - multipart/form-data with PDF file
 * - hotelId: string
 * - roomId: string
 *
 * Response:
 * {
 *   success: boolean,
 *   result: {
 *     filePath: string,
 *     fileUrl: string
 *   }
 * }
 */
router.post('/upload', upload.single('pdfFile'), async (req, res) => {
  try {
    const { hotelId, roomId } = req.body;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No PDF file uploaded'
      });
    }

    // Log the upload request
    logger.info(`[PDF_UPLOAD] Starting upload for hotel ${hotelId}, room ${roomId}`, {
      hotel_id: hotelId,
      room_id: roomId,
      file_path: req.file.path,
      file_name: req.file.originalname,
      file_size: req.file.size
    });

    // Generate a unique file path for local storage
    const localFilePath = req.file.path;
    const fileName = path.basename(req.file.path);
    const remoteFilePath = `hotel-tariffs/${fileName}`;

    // Try to upload to Linode Object Storage, but continue even if it fails
    let fileUrl = null;
    try {
      fileUrl = await linodeStorage.uploadFile(localFilePath, remoteFilePath);
      logger.info(`[PDF_UPLOAD] File uploaded to Linode: ${fileUrl}`, {
        hotel_id: hotelId,
        room_id: roomId,
        local_path: localFilePath,
        remote_path: remoteFilePath
      });
    } catch (uploadError) {
      logger.error(`[PDF_UPLOAD] Error uploading file to Linode: ${uploadError.message}`, {
        error_stack: uploadError.stack,
        hotel_id: hotelId,
        room_id: roomId,
        local_path: localFilePath
      });

      // Continue with local file path instead
      fileUrl = `file://${localFilePath}`;
      logger.info(`[PDF_UPLOAD] Using local file path instead: ${localFilePath}`);
    }

    // Return success response with file information
    // Always return a valid JSON response
    return res.status(200).json({
      success: true,
      result: {
        filePath: fileUrl ? remoteFilePath : localFilePath,
        fileUrl: fileUrl || `file://${localFilePath}`,
        localPath: localFilePath,
        isLocal: !fileUrl || fileUrl.startsWith('file://')
      }
    });
  } catch (error) {
    logger.error(`[PDF_UPLOAD] Error processing upload: ${error.message}`, {
      error_stack: error.stack
    });

    // Ensure we always return a valid JSON response
    return res.status(500).json({
      success: false,
      message: 'Error processing upload',
      error: error.message || 'Unknown error'
    });
  }
});

module.exports = router;

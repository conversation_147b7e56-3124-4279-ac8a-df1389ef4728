/* eslint-disable @typescript-eslint/no-explicit-any */
import { viewPointsDefault } from "@/utils/constants/optionsData";
import { useEffect, useState } from "react";

export default function AddViewPoints(props:any) {
  const [selectedViewPoint, setSelectedViewPoints] = useState<string[]>([]);
  const [allViewPoints, setAllViewPoints] =
    useState<string[]>(viewPointsDefault);
  const [showView, setShowView] = useState(false);
  useEffect(() => {
    const data = viewPointsDefault.filter((k) => {
      return !selectedViewPoint?.includes(k);
    });
    setAllViewPoints(data);
  }, [selectedViewPoint]);
  function handleInput(inp: string) {
    props.setViewPoints([...selectedViewPoint, inp])
    setSelectedViewPoints([...selectedViewPoint, inp]);
  }
  function handleClose(inp:string){
    const data = selectedViewPoint.filter((k) => {
        return k!==inp
      });
      props.setViewPoints(data)
      setSelectedViewPoints(data)
  }
  return (
    <div className="w-full border relative py-2 text-xl px-2 flex flex-wrap">
         <div className="flex flex-wrap">
        {
            selectedViewPoint?.length>0?selectedViewPoint?.map((k)=><div key={k} className="text-sm text-white bg-blue-500 m-1 p-1 rounded-lg flex items-center">{k}<div onClick={()=>handleClose(k)} className="bg-blue-600 flex justify-center items-center cursor-pointer w-[20px] h-[20px] ml-4 rounded-full font-bold">x</div></div>):""
        }
        </div>
      <div className="relative">
      <button
        className="border bg-slate-300 p-1 rounded-lg"
        onClick={() => setShowView(!showView)}
      >
        + View Point
      </button>
    {
        showView&& <div  className="w-[200px] max-h-[200px] border overflow-y-auto z-30 bg-white shadow-2xl absolute top-[50px] m-1">
        { allViewPoints?.length > 0 ? allViewPoints?.map((k)=><div key={k} onClick={()=>handleInput(k)} className="p-1 cursor-pointer hover:bg-slate-100 text-lg">{k}</div>): ""}
          </div>
    }  
      </div> 
    </div>
  );
}

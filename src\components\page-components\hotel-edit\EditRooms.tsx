import { fetchAllRooms } from "@/utils/api-functions/fetch-rooms";
import RoomItems from "./RoomItems";
import DailyPriceCalendar from "./DailyPriceCalendar";
import { HotelRoom } from "@/types/types";
import { Button } from "@/components/ui/button";
import { BedDouble, Calendar, FileText, Loader2, Plus, AlertTriangle } from "lucide-react";
import { Link } from "react-router-dom";
import { useQuery } from "react-query";
import { useState } from "react";
import HotelPriceIssuesList from "./HotelPriceIssuesList";
import TariffUploadList from "./TariffUploadList";

interface EditRoomProps {
    hotelId: string;
}

const EditRooms = ({ hotelId }: EditRoomProps) => {
    const [activeView, setActiveView] = useState<'list' | 'calendar' | 'issues' | 'tariff'>('list');

    const { data: rooms, isLoading, error, isError } = useQuery<HotelRoom[]>(
        ["rooms", hotelId],
        () => {
            return fetchAllRooms(hotelId);
        },
        {
            enabled: <PERSON><PERSON><PERSON>(hotelId),
            staleTime: 1000 * 60 * 5, // 5 minutes
        }
    );

    // Loading state
    if (isLoading) {
        return (
            <div className="flex flex-col items-center justify-center py-12">
                <Loader2 className="h-10 w-10 text-blue-600 animate-spin mb-4" />
                <p className="text-gray-600">Loading room data...</p>
            </div>
        );
    }

    // Error state
    if (isError) {
        return (
            <div className="bg-red-50 rounded-lg p-6 text-center">
                <div className="text-red-500 mb-4">
                    <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load rooms</h3>
                <p className="text-gray-600 mb-4">There was an error loading the room data. Please try again later.</p>
                <div className="text-xs text-gray-500 bg-gray-100 p-2 rounded-md">
                    {error instanceof Error ? error.message : "Unknown error"}
                </div>
            </div>
        );
    }

    // Empty state
    if (!rooms || rooms.length === 0) {
        return (
            <div className="bg-gray-50 rounded-lg p-8 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                    <BedDouble className="h-16 w-16 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No rooms added yet</h3>
                    <p className="text-gray-500 max-w-md mx-auto mb-6">
                        This hotel doesn't have any rooms configured. Add rooms to manage pricing and availability.
                    </p>
                    <Link
                        to={`/hotels/${hotelId}`}
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                        <Plus size={16} className="mr-2" />
                        Add Room
                    </Link>
                </div>
            </div>
        );
    }

    // Rooms list and Price Calendar
    return (
        <div>
            {/* View Toggle Tabs */}
            <div className="flex border-b mb-6">
                <button
                    onClick={() => setActiveView('list')}
                    className={`px-4 py-2 border-b-2 font-medium text-sm ${
                        activeView === 'list'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                >
                    <span className="flex items-center">
                        <BedDouble size={16} className="mr-2" />
                        Room List
                    </span>
                </button>
                <button
                    onClick={() => setActiveView('calendar')}
                    className={`px-4 py-2 border-b-2 font-medium text-sm ${
                        activeView === 'calendar'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                >
                    <span className="flex items-center">
                        <Calendar size={16} className="mr-2" />
                        Price Calendar
                    </span>
                </button>
                <button
                    onClick={() => setActiveView('issues')}
                    className={`px-4 py-2 border-b-2 font-medium text-sm ${
                        activeView === 'issues'
                            ? 'border-amber-500 text-amber-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                >
                    <span className="flex items-center">
                        <AlertTriangle size={16} className="mr-2" />
                        Price Issues
                    </span>
                </button>
                <button
                    onClick={() => setActiveView('tariff')}
                    className={`px-4 py-2 border-b-2 font-medium text-sm ${
                        activeView === 'tariff'
                            ? 'border-green-500 text-green-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                >
                    <span className="flex items-center">
                        <FileText size={16} className="mr-2" />
                        Tariff Upload
                    </span>
                </button>
            </div>

            {activeView === 'list' ? (
                <div className="grid grid-cols-1 gap-6">
                    {/* Actions Bar */}
                    <div className="flex justify-between items-center mb-2">
                        <div>
                            <h3 className="text-sm font-medium text-gray-500">
                                {rooms.length} {rooms.length === 1 ? 'Room' : 'Rooms'} Available
                            </h3>
                        </div>
                        <Link to={`/hotels/${hotelId}`}>
                            <Button
                                variant="outline"
                                size="sm"
                                className="text-blue-600 border-blue-200 hover:bg-blue-50"
                            >
                                <Plus size={16} className="mr-1.5" />
                                Add New Room
                            </Button>
                        </Link>
                    </div>

                    {/* Room Cards */}
                    {rooms.map((room) => (
                        <RoomItems key={room.hotelRoomId} roomData={room} />
                    ))}
                </div>
            ) : activeView === 'calendar' ? (
                <DailyPriceCalendar hotelId={hotelId} />
            ) : activeView === 'issues' ? (
                <HotelPriceIssuesList hotelId={hotelId} rooms={rooms} />
            ) : (
                (() => {
                    // Verify and filter rooms to ensure they belong to the current hotel
                    const verifiedRooms = rooms.filter(room => room.hotelId === hotelId);

                    // Log verification for debugging
                    console.log('[HOTEL_VERIFICATION] EditRooms - Rooms verification before passing to TariffUploadList', {
                        hotel_id: hotelId,
                        total_rooms: rooms.length,
                        verified_rooms: verifiedRooms.length,
                        all_rooms_belong_to_hotel: rooms.length === verifiedRooms.length,
                        rooms_with_different_hotel_id: rooms.length - verifiedRooms.length,
                        different_hotel_ids: [...new Set(rooms.filter(r => r.hotelId !== hotelId).map(r => r.hotelId))]
                    });

                    // Pass only verified rooms to TariffUploadList
                    return <TariffUploadList hotelId={hotelId} rooms={verifiedRooms} />;
                })()
            )}
        </div>
    );
};

export default EditRooms;

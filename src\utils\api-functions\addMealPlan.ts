import { MealPlanData } from '@/components/page-components/hotel-details/room/mealPlan/AddMealPlan';
import toast from 'react-hot-toast';
import api from './auth';

export const handleMealPlanApi = async (roomId: string, mealPlan: MealPlanData[]) => {
  try {
    const response = await api.post(
      `admin/hotel/hotelRoom/${roomId}/mealPlan/add`,
      mealPlan
    );

    return Promise.resolve(response);
  } catch (error) {
    toast.error('An Error occurred');
    return Promise.reject('error');
  }
};

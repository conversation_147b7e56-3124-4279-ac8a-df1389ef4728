import api from './auth';

export const login = async (email: string, password: string) => {
  // Temporary mock authentication since production API is down (502 error)
  if (import.meta.env.VITE_MOCK_AUTH === 'true') {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock successful login for any email/password
    if (email && password) {
      return {
        data: {
          accessToken: 'mock-access-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          user: {
            id: '1',
            email: email,
            name: 'Admin User'
          }
        }
      };
    } else {
      throw new Error('Invalid credentials');
    }
  }

  try {
    const response = await api.put('admin/auth/login', { email, password });
    return response;
  } catch (error) {
    return Promise.reject(error);
  }
};
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactElement, useContext, useState } from "react";
import AddMealPlan from "./AddMealPlan";
import { useParams } from "react-router-dom";
import { handleMealPlanApi } from "@/utils/api-functions/addMealPlan";
import AppContext from "@/utils/context/AppContext";
import { Plus, Save, Maximize2, Minimize2 } from "lucide-react";
import toast from "react-hot-toast";

export default function AddMealPlans() {
  const { id2 } = useParams();
  const { mealPlanData }: any = useContext(AppContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [addedMealPlans, setAddedMealPlans] = useState<ReactElement[]>([
    <AddMealPlan key={0} isMaximized={isMaximized} />,
  ]);
  const [isDisabled, setDisabled] = useState(false);

  function handleAddMealPlans() {
    const newIndex = addedMealPlans.length;
    setAddedMealPlans([
      <AddMealPlan key={newIndex} isMaximized={isMaximized} />,
      ...addedMealPlans,
    ]);
  }

  async function handleSubmitData() {
    setDisabled(true);
    try {
      // Show loading toast
      const loadingToast = toast.loading("Submitting meal plans...");

      const resp = await handleMealPlanApi(id2 as string, mealPlanData);
      console.log(resp);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success("All meal plans submitted successfully!", {
        duration: 3000,
        icon: '🎉',
        style: {
          borderRadius: '10px',
          background: '#E8F5E9',
          color: '#1B5E20',
        },
      });

      // Reload after a short delay to show the success message
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      // Show error toast
      toast.error("Failed to submit meal plans. Please try again.", {
        duration: 4000,
        style: {
          borderRadius: '10px',
          background: '#FFEBEE',
          color: '#B71C1C',
        },
      });
      setDisabled(false);
    }
  }

  // Update all meal plans when maximized state changes
  const handleMaximizeToggle = () => {
    const newMaximizedState = !isMaximized;
    setIsMaximized(newMaximizedState);
    
    // Re-render all meal plans with new maximized state
    setAddedMealPlans(prevPlans => 
      prevPlans.map((_, index) => (
        <AddMealPlan key={index} isMaximized={newMaximizedState} />
      ))
    );
  };

  return (
    <div className={`${isMaximized ? 'min-h-screen bg-gray-50' : ''}`}>
      {/* Header with controls */}
      <div className="flex items-center justify-between mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
        <div>
          <h2 className="text-xl font-bold text-gray-800">Add Meal Plans</h2>
          <p className="text-sm text-gray-600 mt-1">
            {mealPlanData?.length > 0 
              ? `${mealPlanData.length} meal plan(s) ready to submit`
              : "Create meal plans for this room"
            }
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Maximize Toggle */}
          <button
            onClick={handleMaximizeToggle}
            className="inline-flex items-center gap-2 px-3 py-2 text-sm rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-200"
            title={isMaximized ? "Exit full screen" : "Enter full screen"}
          >
            {isMaximized ? (
              <>
                <Minimize2 className="w-4 h-4" />
                Exit Full Screen
              </>
            ) : (
              <>
                <Maximize2 className="w-4 h-4" />
                Full Screen
              </>
            )}
          </button>

          {/* Add Meal Plan Button */}
          <button
            className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg shadow hover:bg-blue-700 transition-all duration-200 active:transform active:scale-95"
            onClick={handleAddMealPlans}
          >
            <Plus className="w-4 h-4" />
            Add Meal Plan
          </button>

          {/* Submit Button */}
          {mealPlanData?.length > 0 && (
            <button
              onClick={handleSubmitData}
              disabled={isDisabled}
              className={`inline-flex items-center gap-2 px-4 py-2 rounded-lg shadow transition-all duration-200 ${
                !isDisabled
                  ? "bg-green-600 text-white hover:bg-green-700 active:transform active:scale-95"
                  : "bg-green-400 text-white cursor-not-allowed opacity-75"
              }`}
            >
              <Save className="w-4 h-4" />
              {isDisabled ? "Submitting..." : "Submit All"}
            </button>
          )}
        </div>
      </div>

      {/* Meal Plans Container */}
      <div className={`space-y-6 ${isMaximized ? 'p-4' : ''}`}>
        {addedMealPlans?.map((mealPlan, i) => (
          <div key={i} className={`${isMaximized ? 'bg-white rounded-lg shadow-lg border border-gray-200' : ''}`}>
            {mealPlan}
          </div>
        ))}
      </div>

      {/* Empty State */}
      {addedMealPlans.length === 0 && (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Plus className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No meal plans yet</h3>
          <p className="text-gray-500 mb-4">Get started by adding your first meal plan.</p>
          <button
            onClick={handleAddMealPlans}
            className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Add First Meal Plan
          </button>
        </div>
      )}

      {/* Summary Footer */}
      {mealPlanData?.length > 0 && (
        <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-900">Ready to Submit</h4>
              <p className="text-sm text-blue-700">
                {mealPlanData.length} meal plan{mealPlanData.length !== 1 ? 's' : ''} configured and ready to be added to the room.
              </p>
            </div>
            <button
              onClick={handleSubmitData}
              disabled={isDisabled}
              className={`inline-flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                !isDisabled
                  ? "bg-green-600 text-white hover:bg-green-700 shadow-md active:transform active:scale-95"
                  : "bg-green-400 text-white cursor-not-allowed opacity-75"
              }`}
            >
              <Save className="w-5 h-5" />
              {isDisabled ? "Submitting..." : `Submit ${mealPlanData.length} Meal Plan${mealPlanData.length !== 1 ? 's' : ''}`}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

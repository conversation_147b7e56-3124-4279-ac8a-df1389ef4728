/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactElement, useContext, useState } from "react";
import AddMealPlan from "./AddMealPlan";
import { useParams } from "react-router-dom";
import { handleMealPlan<PERSON>pi } from "@/utils/api-functions/addMealPlan";
import AppContext from "@/utils/context/AppContext";

export default function AddMealPlans() {

  const {id2 }= useParams();
    const {mealPlanData}:any = useContext(AppContext)
  const [addedRooms, setAddedRooms] = useState<ReactElement[]>([
    <AddMealPlan
    />,
  ]);
  const [isDisabled,setDisabled] = useState(false);
  function handleAddRoooms() {
    setAddedRooms([
      <AddMealPlan
   
      />,
      ...addedRooms,
    ]);
  }
  async function handleSubmitData() {
    setDisabled(true)
    try {
      const resp = await handleMeal<PERSON>lan<PERSON><PERSON>(id2 as string,mealPlanData)
      console.log(resp);
      window.location.reload();
    } catch (error) {
      setDisabled(false)
    }
  }
 
  return (
    <div>
      {addedRooms?.map((k, i) => (
        <div key={i}>{k}</div>
      ))}
      <button
        className="bg-yellow-500  px-4 py-2 rounded-lg border shadow m-2"
        onClick={handleAddRoooms}
      >
        add Meal Plan
      </button>

      {mealPlanData?.length > 0 ? (
        <button
          onClick={handleSubmitData}
          className={!isDisabled?"bg-green-500  px-4 py-2 rounded-lg border shadow m-2":"bg-green-500 opacity-75 cursor-not-allowed  px-4 py-2 rounded-lg border shadow m-2"}
          disabled={isDisabled}
        >
          Submit
        </button>
      ) : (
        <></>
      )}
    </div>
  );
}

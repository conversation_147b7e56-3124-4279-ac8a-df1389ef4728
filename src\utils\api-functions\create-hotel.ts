import { HotelUploadData } from "@/types/types";
import toast from "react-hot-toast";
import api from './auth'

export async function handleCreateHotel( data : HotelUploadData) {
    try {
        const response = await api.post(
            "admin/hotel",
          data
        );
        return Promise.resolve(response);
      } catch (error) {
        toast.error('An Error occurred');
        return Promise.reject('error');
      }
}
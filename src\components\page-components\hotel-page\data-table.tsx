import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  useReactTable,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"


import React, { useState } from "react"
import { Input } from "@/components/ui/input"
import { ListFilter } from "lucide-react"
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  noOfHotels : number
}

export function DataTable<TData, TValue>({
  columns,
  data,
  noOfHotels

}: DataTableProps<TData, TValue>) {
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [pageSize, setPageSize] = useState(25)
  const [pageIndex, setPageIndex] = useState(0)

  // Reset to first page when filters change
  React.useEffect(() => {
    setPageIndex(0)
  }, [columnFilters])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      columnFilters,
      pagination: { pageIndex, pageSize },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const next = updater({ pageIndex, pageSize })
        setPageIndex(next.pageIndex)
        setPageSize(next.pageSize)
      } else {
        setPageIndex(updater.pageIndex)
        setPageSize(updater.pageSize)
      }
    },
    manualPagination: false,
    pageCount: Math.ceil(noOfHotels / pageSize),
  })

  // Get the filtered rows count for pagination
  const filteredRowsCount = table.getFilteredRowModel().rows.length

  // Calculate total pages based on filtered rows when search is active
  // or total rows when no search is applied
  const hasActiveFilters = columnFilters.length > 0
  const totalPages = hasActiveFilters
    ? Math.ceil(filteredRowsCount / pageSize)
    : table.getPageCount()

  // Ensure we don't stay on a non-existent page after filtering
  React.useEffect(() => {
    if (hasActiveFilters && pageIndex >= totalPages && totalPages > 0) {
      setPageIndex(Math.max(0, totalPages - 1))
    }
  }, [hasActiveFilters, pageIndex, totalPages])

  // Page size options
  const pageSizeOptions = [25, 50, 100, 200]

  return (
    <>

      <div className="flex items-center justify-between py-4">
        <div className="flex items-center  gap-10">
        <div className="">
     <h1 className="font-bold text-xl ">
<span className="bg-appprimary text-white p-2 rounded-full">
{hasActiveFilters ? filteredRowsCount : noOfHotels}</span> <span className="font-medium text-neutral-700">Hotels</span>
     </h1>


    </div>
    <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
             <h1 className="text-lg text-neutral-600">
             <ListFilter size={18}/> </h1>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>
        <div className="relative w-96">
          <Input
            placeholder="Search Hotel Name"
            value={(table.getColumn("hotelName")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("hotelName")?.setFilterValue(event.target.value)
            }
            className="w-full focus:outline-none active:outline-none outline-none pr-8"
          />
          {(table.getColumn("hotelName")?.getFilterValue() as string) && (
            <button
              onClick={() => table.getColumn("hotelName")?.setFilterValue("")}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          )}
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
             <h1 className="text-lg text-neutral-600">
             <ListFilter size={18}/> </h1>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                )
              })}
          </DropdownMenuContent>
        </DropdownMenu>

        </div>

        <a href="/hotels/add" className="bg-appprimary text-white px-4 py-3 rounded-md">
        Create a Hotel
        </a>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex flex-col items-center gap-4 mt-6">
        {/* Pagination controls */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
            className="px-2 py-1 rounded border border-blue-200 text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
          >
            {'<<'}
          </button>
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="px-2 py-1 rounded border border-blue-200 text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
          >
            {'<'}
          </button>
          {/* Only show up to 5 page buttons at a time with ellipsis for better UX */}
          {Array.from({ length: totalPages }).map((_, i) => {
            // Show first page, last page, current page, and pages around current page
            const showPageButton =
              i === 0 || // First page
              i === totalPages - 1 || // Last page
              (i >= pageIndex - 1 && i <= pageIndex + 1); // Current page and adjacent pages

            // Show ellipsis for page gaps
            const showEllipsisBefore = i === pageIndex - 2 && pageIndex > 2;
            const showEllipsisAfter = i === pageIndex + 2 && pageIndex < totalPages - 3;

            if (showPageButton) {
              return (
                <button
                  key={i}
                  onClick={() => table.setPageIndex(i)}
                  className={`px-3 py-1 rounded border ${i === pageIndex ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-blue-700 border-blue-200 hover:bg-blue-50'} font-semibold`}
                >
                  {i + 1}
                </button>
              );
            } else if (showEllipsisBefore || showEllipsisAfter) {
              return <span key={i} className="px-2">...</span>;
            }

            return null;
          }).filter(Boolean)}
          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="px-2 py-1 rounded border border-blue-200 text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
          >
            {'>'}
          </button>
          <button
            onClick={() => table.setPageIndex(totalPages - 1)}
            disabled={!table.getCanNextPage()}
            className="px-2 py-1 rounded border border-blue-200 text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
          >
            {'>>'}
          </button>
        </div>
        {/* Page size selector */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">Show</span>
          <select
            value={pageSize}
            onChange={e => {
              setPageSize(Number(e.target.value))
              setPageIndex(0)
            }}
            className="border border-blue-200 rounded px-2 py-1 text-blue-900 bg-white focus:ring-2 focus:ring-blue-400"
          >
            {pageSizeOptions.map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
          <span className="text-sm text-gray-700">per page</span>
        </div>
      </div>
    </>
  )
}

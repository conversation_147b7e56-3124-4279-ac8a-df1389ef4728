import React, { useState } from 'react';
import { HotelRoom } from '@/types/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  AlertCircle,
  ArrowRight,
  Check,
  ChevronRight,
  Edit,
  Save
} from 'lucide-react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';

interface RoomTypeMappingProps {
  extractedRoomTypes: string[];
  hotelRooms: HotelRoom[];
  roomMappings: Record<string, string>;
  onMapRoom: (extractedRoomType: string, dbRoomId: string) => void;
  onCreateNewRoom: (extractedRoomType: string, newRoomName: string) => void;
  onNext: () => void;
}

const RoomTypeMapping: React.FC<RoomTypeMappingProps> = ({
  extractedRoomTypes,
  hotelRooms,
  roomMappings,
  onMapRoom,
  onCreateNewRoom,
  onNext
}) => {
  const [newRoomNames, setNewRoomNames] = useState<Record<string, string>>({});
  const [editingRoom, setEditingRoom] = useState<string | null>(null);

  // Check if all room types are mapped
  const allRoomsMapped = extractedRoomTypes.every(
    roomType => roomMappings[roomType] && roomMappings[roomType] !== 'new' || 
    (roomMappings[roomType] === 'new' && newRoomNames[roomType])
  );

  // Handle new room name change
  const handleNewRoomNameChange = (extractedRoomType: string, newName: string) => {
    setNewRoomNames(prev => ({
      ...prev,
      [extractedRoomType]: newName
    }));
  };

  // Handle create new room
  const handleCreateNewRoom = (extractedRoomType: string) => {
    if (newRoomNames[extractedRoomType]) {
      onCreateNewRoom(extractedRoomType, newRoomNames[extractedRoomType]);
      setEditingRoom(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">
          Step 1: Map Room Types
        </h3>
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          {extractedRoomTypes.length} Room Types Found
        </Badge>
      </div>

      <Alert className="bg-blue-50 border-blue-200">
        <AlertCircle className="h-4 w-4 text-blue-600" />
        <AlertTitle className="text-blue-800">Map Room Types</AlertTitle>
        <AlertDescription className="text-blue-700">
          Match each room type found in the PDF to an existing room in your hotel, or create a new room type.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        {extractedRoomTypes.map((roomType) => (
          <div key={roomType} className="bg-white rounded-lg border p-4 shadow-sm">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              {/* Extracted Room Type */}
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-500 mb-1">From PDF:</div>
                <div className="font-medium text-gray-800">{roomType}</div>
              </div>

              {/* Arrow */}
              <div className="hidden md:flex text-gray-400">
                <ArrowRight size={20} />
              </div>

              {/* Mapping Control */}
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-500 mb-1">Map to:</div>
                
                {roomMappings[roomType] === 'new' && editingRoom === roomType ? (
                  <div className="flex items-center gap-2">
                    <Input
                      value={newRoomNames[roomType] || ''}
                      onChange={(e) => handleNewRoomNameChange(roomType, e.target.value)}
                      placeholder="Enter new room name"
                      className="flex-1"
                    />
                    <Button 
                      size="sm" 
                      onClick={() => handleCreateNewRoom(roomType)}
                      disabled={!newRoomNames[roomType]}
                    >
                      <Save size={16} className="mr-1" />
                      Save
                    </Button>
                  </div>
                ) : roomMappings[roomType] === 'new' ? (
                  <div className="flex items-center gap-2">
                    <div className="text-blue-600 font-medium">
                      New: {newRoomNames[roomType]}
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => setEditingRoom(roomType)}
                    >
                      <Edit size={16} />
                    </Button>
                  </div>
                ) : (
                  <select
                    value={roomMappings[roomType] || ''}
                    onChange={(e) => onMapRoom(roomType, e.target.value)}
                    className="w-full rounded-md border border-gray-300 py-2 px-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="">Select a room type</option>
                    {hotelRooms.map((room) => (
                      <option key={room.hotelRoomId} value={room.hotelRoomId}>
                        {room.hotelRoomType}
                      </option>
                    ))}
                    <option value="new">+ Create New Room Type</option>
                  </select>
                )}
              </div>

              {/* Status */}
              <div className="flex items-center">
                {roomMappings[roomType] && (roomMappings[roomType] !== 'new' || newRoomNames[roomType]) ? (
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    <Check size={14} className="mr-1" />
                    Mapped
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                    <AlertCircle size={14} className="mr-1" />
                    Not Mapped
                  </Badge>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end mt-6">
        <Button
          onClick={onNext}
          disabled={!allRoomsMapped}
          className="flex items-center"
        >
          Next Step
          <ChevronRight size={16} className="ml-1" />
        </Button>
      </div>
    </div>
  );
};

export default RoomTypeMapping;

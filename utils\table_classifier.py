#!/usr/bin/env python3
"""
Table Classifier for Tariff Extraction

This module provides functionality to classify tables in PDFs,
distinguishing between rate tables, information tables, and other types.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Union

# Configure logging
logger = logging.getLogger('tariff_extractor')

class TableClassifier:
    """Table classifier for identifying different types of tables in PDFs"""
    
    def __init__(self, config: Optional[Any] = None):
        """
        Initialize the table classifier
        
        Args:
            config: Optional configuration object
        """
        self.config = config
        
        # Default patterns for table classification
        self.patterns = {
            # Patterns for rate table headers
            'rate_table_headers': [
                r'(?:room|accommodation)\s+(?:type|category)',
                r'(?:rate|price|tariff|cost)',
                r'(?:cp|map|ap|ep|continental|american|european)',
                r'(?:single|double|twin|triple)\s+(?:occupancy|sharing)',
                r'(?:season|period|validity)',
                r'(?:weekday|weekend)',
                r'(?:rack|net|special|contract|agent)'
            ],
            
            # Patterns for rate table content
            'rate_table_content': [
                r'(?:deluxe|super|premium|executive|standard|suite|cottage|villa)',
                r'(?:garden|mountain|sea|lake|pool|beach|city)\s+(?:view|facing)',
                r'(?:rs\.?|inr|₹)\s*\d+',
                r'\d{3,}(?:\.\d{2})?'  # Numbers with 3+ digits (likely prices)
            ],
            
            # Patterns for information tables (not rate tables)
            'info_table_headers': [
                r'(?:amenity|facility|service)',
                r'(?:contact|address|phone|email)',
                r'(?:check-in|check-out)\s+(?:time|policy)',
                r'(?:cancellation|refund)\s+(?:policy|terms)',
                r'(?:inclusion|exclusion)'
            ],
            
            # Patterns for meal plan tables
            'meal_plan_table_headers': [
                r'(?:meal|food|dining)\s+(?:plan|package|option)',
                r'(?:breakfast|lunch|dinner)',
                r'(?:cp|map|ap|ep)\s+(?:supplement|charge|cost)'
            ]
        }
        
        # Load patterns from config if available
        if config and hasattr(config, 'get_full_config'):
            config_patterns = config.get_full_config().get('table_classification_patterns', {})
            for category, patterns in config_patterns.items():
                if category in self.patterns:
                    self.patterns[category].extend(patterns)
                else:
                    self.patterns[category] = patterns
        
        # Compile regex patterns for better performance
        self.compiled_patterns = {}
        for category, patterns in self.patterns.items():
            self.compiled_patterns[category] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        
        # Statistics for classification operations
        self.classification_stats = {
            'total_tables_classified': 0,
            'rate_tables_identified': 0,
            'info_tables_identified': 0,
            'meal_plan_tables_identified': 0,
            'unclassified_tables': 0
        }
    
    def classify_table(self, table: List[List[str]]) -> Dict[str, Any]:
        """
        Classify a table based on its content
        
        Args:
            table: Table to classify (list of rows, each row is a list of cells)
            
        Returns:
            Dictionary containing classification information
        """
        if not table or len(table) < 2:  # Need at least header + one data row
            return {
                'type': 'unknown',
                'confidence': 0.0,
                'is_rate_table': False,
                'is_info_table': False,
                'is_meal_plan_table': False,
                'has_prices': False,
                'has_room_types': False,
                'has_meal_plans': False,
                'has_dates': False
            }
        
        self.classification_stats['total_tables_classified'] += 1
        
        # Initialize scores for different table types
        scores = {
            'rate_table': 0,
            'info_table': 0,
            'meal_plan_table': 0
        }
        
        # Initialize feature flags
        features = {
            'has_prices': False,
            'has_room_types': False,
            'has_meal_plans': False,
            'has_dates': False,
            'numeric_columns': [],
            'room_type_column': -1,
            'meal_plan_columns': [],
            'date_columns': []
        }
        
        # Check header row for classification patterns
        header_row = table[0]
        header_text = ' '.join([str(cell).lower() for cell in header_row if cell])
        
        # Check for rate table headers
        for pattern in self.compiled_patterns['rate_table_headers']:
            if pattern.search(header_text):
                scores['rate_table'] += 2
                logger.debug(f"Rate table header pattern matched: {pattern.pattern}")
        
        # Check for info table headers
        for pattern in self.compiled_patterns['info_table_headers']:
            if pattern.search(header_text):
                scores['info_table'] += 2
                logger.debug(f"Info table header pattern matched: {pattern.pattern}")
        
        # Check for meal plan table headers
        for pattern in self.compiled_patterns['meal_plan_table_headers']:
            if pattern.search(header_text):
                scores['meal_plan_table'] += 2
                logger.debug(f"Meal plan table header pattern matched: {pattern.pattern}")
        
        # Analyze individual columns
        for col_idx, header in enumerate(header_row):
            header_text = str(header).lower()
            
            # Check for room type column
            if re.search(r'room|type|category|accommodation', header_text, re.I):
                features['room_type_column'] = col_idx
                features['has_room_types'] = True
                scores['rate_table'] += 1
            
            # Check for meal plan columns
            if re.search(r'cp|map|ap|ep|continental|american|european|meal', header_text, re.I):
                features['meal_plan_columns'].append(col_idx)
                features['has_meal_plans'] = True
                scores['rate_table'] += 1
                scores['meal_plan_table'] += 1
            
            # Check for date columns
            if re.search(r'date|period|season|validity', header_text, re.I):
                features['date_columns'].append(col_idx)
                features['has_dates'] = True
                scores['rate_table'] += 1
            
            # Check for price columns
            if re.search(r'rate|price|tariff|cost|rs|inr', header_text, re.I):
                features['numeric_columns'].append(col_idx)
                features['has_prices'] = True
                scores['rate_table'] += 1
        
        # If we haven't identified price columns from headers, look for numeric columns
        if not features['has_prices']:
            # Check data rows for numeric values that could be prices
            for row_idx in range(1, min(5, len(table))):  # Check first few data rows
                for col_idx, cell in enumerate(table[row_idx]):
                    # Skip columns already identified as other types
                    if col_idx == features['room_type_column'] or col_idx in features['date_columns']:
                        continue
                    
                    cell_text = str(cell).strip()
                    
                    # Check if cell contains a price-like value
                    if re.search(r'\d{3,}(?:\.\d{2})?', cell_text):  # 3+ digits, possibly with decimal
                        if col_idx not in features['numeric_columns']:
                            features['numeric_columns'].append(col_idx)
                            features['has_prices'] = True
                            scores['rate_table'] += 0.5  # Lower score for inferred price columns
        
        # Check content of data rows for additional classification clues
        content_text = ' '.join([' '.join([str(cell) for cell in row if cell]) for row in table[1:min(5, len(table))]])
        
        # Check for rate table content
        for pattern in self.compiled_patterns['rate_table_content']:
            if pattern.search(content_text):
                scores['rate_table'] += 1
                logger.debug(f"Rate table content pattern matched: {pattern.pattern}")
        
        # Determine the table type based on scores
        table_type = 'unknown'
        confidence = 0.0
        
        if scores['rate_table'] > scores['info_table'] and scores['rate_table'] > scores['meal_plan_table']:
            table_type = 'rate_table'
            confidence = min(1.0, scores['rate_table'] / 10.0)  # Normalize to 0-1 range
            self.classification_stats['rate_tables_identified'] += 1
        elif scores['meal_plan_table'] > scores['info_table'] and scores['meal_plan_table'] > 0:
            table_type = 'meal_plan_table'
            confidence = min(1.0, scores['meal_plan_table'] / 6.0)  # Normalize to 0-1 range
            self.classification_stats['meal_plan_tables_identified'] += 1
        elif scores['info_table'] > 0:
            table_type = 'info_table'
            confidence = min(1.0, scores['info_table'] / 6.0)  # Normalize to 0-1 range
            self.classification_stats['info_tables_identified'] += 1
        else:
            self.classification_stats['unclassified_tables'] += 1
        
        # Special case: If table has prices but wasn't classified as a rate table,
        # it might be a rate table with unconventional structure
        if features['has_prices'] and table_type != 'rate_table' and table_type != 'meal_plan_table':
            logger.debug(f"Table has prices but wasn't classified as rate table. Reclassifying.")
            table_type = 'rate_table'
            confidence = 0.6  # Moderate confidence for this heuristic
            self.classification_stats['rate_tables_identified'] += 1
            self.classification_stats['unclassified_tables'] -= 1
        
        return {
            'type': table_type,
            'confidence': confidence,
            'is_rate_table': table_type == 'rate_table',
            'is_info_table': table_type == 'info_table',
            'is_meal_plan_table': table_type == 'meal_plan_table',
            'features': features,
            'scores': scores
        }
    
    def classify_tables(self, tables: List[List[List[str]]]) -> List[Dict[str, Any]]:
        """
        Classify multiple tables
        
        Args:
            tables: List of tables to classify
            
        Returns:
            List of dictionaries containing classification information for each table
        """
        return [self.classify_table(table) for table in tables if table]
    
    def get_rate_tables(self, tables: List[List[List[str]]], classifications: Optional[List[Dict[str, Any]]] = None) -> List[Tuple[List[List[str]], Dict[str, Any]]]:
        """
        Get only the rate tables from a list of tables
        
        Args:
            tables: List of tables
            classifications: Optional pre-computed classifications
            
        Returns:
            List of tuples containing (table, classification) for rate tables
        """
        if not classifications:
            classifications = self.classify_tables(tables)
        
        rate_tables = []
        for i, (table, classification) in enumerate(zip(tables, classifications)):
            if classification['is_rate_table']:
                rate_tables.append((table, classification))
                logger.debug(f"Table {i} classified as rate table with confidence {classification['confidence']:.2f}")
        
        return rate_tables
    
    def get_classification_stats(self) -> Dict[str, int]:
        """
        Get statistics about classification operations
        
        Returns:
            Dictionary of classification statistics
        """
        return self.classification_stats

/* eslint-disable @typescript-eslint/no-explicit-any */

import { columns } from "@/components/activity/columns";
import { DataTable } from "@/components/activity/data-table";
import Loading from "@/components/package/Loading";
import { ActivityType } from "@/types/types";
import { fetchApiData } from "@/utils/api-functions/fetchApiData";
import { useEffect, useState } from "react";

export default function Activity() {
  const [allData, setAllData] = useState<ActivityType[]>([]);

  async function initAllActivity() {
    const resp = await fetchApiData("admin/activity");
    setAllData(resp);
  }

  useEffect(() => {
    initAllActivity();
  }, []);

  return (
    <div>
      <div className="">
      

        <div className="border flex flex-col gap-5  justify-between">
          <div className="px-5 pb-24">
            {allData.length > 0 ? (
              <DataTable
                noOfActivities={allData.length}
                columns={columns}
                data={allData}
              />
            ) : (
              <Loading />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

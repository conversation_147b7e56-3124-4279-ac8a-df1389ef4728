import { configureStore } from "@reduxjs/toolkit";
import { persistReducer, persistStore, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from "redux-persist";
import storage from "redux-persist/lib/storage";


import { TypedUseSelectorHook, useSelector } from "react-redux";
import HotelEditSlice from './features/hotelEditSlice'


const generatePersistConfig = (key: string) => ({
  key,
  storage,
});



const HotelSlice=persistReducer(generatePersistConfig("Authentication"),HotelEditSlice)
export const store = configureStore({
  reducer : {

    HotelEdit :HotelSlice,

  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
})


export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
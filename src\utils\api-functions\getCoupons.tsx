/* eslint-disable @typescript-eslint/no-explicit-any */
// https://api.tripxplo.com/v1/api/admin/coupon
import api from './auth';
export async function getCoupons(offset: number, search?: string){
    try {
      const response = await api.get('admin/coupon', {
        params: {
          limit: 10,
          offset: offset,
          search: search || '',
        },
      });
      return response.data.result;
    } catch (error: any) {
      console.error('Error fetching packages:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch packages');
    }
  }
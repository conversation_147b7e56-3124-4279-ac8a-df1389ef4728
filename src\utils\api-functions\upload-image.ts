import axios from 'axios';
import api from './auth';
import toast from 'react-hot-toast';
async function getSignedUrl(folder: string, format: string, name: string) {
  try {
    const body = {
      folderName: folder,
      ContentType: format,
      fileName: name,
    };
    const response = await api.post('admin/utils/signedUrl', body);
    return Promise.resolve(response.data.result);
  } catch (err) {
    console.log(err);
    Promise.reject(err);
  }
}
export async function handleImageUpload(
  folder: string,
  format: string,
  name: string,
  image: File
) {
  try {
    const resp = await getSignedUrl(folder, format, name);
    const uploadImage = await axios.put(
      resp.signedUrl,
      image,
      {
        headers: { 'Content-Type': image.type, 'x-amz-acl': 'public-read' },
      }
    );
    if (uploadImage.status === 200) {
      toast.success('image Uploaded');
      return resp.filePath;
    }
  } catch (error) {
    toast.error('Error Uploading Image');
    return { success: false, url: '' };
  }
}

/* eslint-disable @typescript-eslint/no-explicit-any */
import api from './auth';
export async function getBookings(offset: number, search?: string){
  try {
    const response = await api.get('admin/booking', {
      params: {
        limit: 10,
        offset: offset,
        search: search || '',
      },
    });
    return Promise.resolve(response.data.result);
  } catch (error: any) {
    return Promise.reject(error.response?.data?.message || 'Failed to fetch bookings');
  }
}

export async function getBooking(id: string) {
  try {
    const response = await api.get(`admin/booking/${id}/getOne`);
    return Promise.resolve(response.data.result);
  } catch (error: any) {
    console.error('Error fetching package:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch package');
  }
}

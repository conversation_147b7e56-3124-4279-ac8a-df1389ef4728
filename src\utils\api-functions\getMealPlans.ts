import api from './auth';
import { HOTEL_ROOM_GET_URL } from '../urls/urls';

// Get meal plans by room ID
export const getMealPlansByRoomId = async (roomId: string) => {
  try {
    const URL = `${HOTEL_ROOM_GET_URL("")}/${roomId}/meal-plans`;
    const response = await api.get(URL);
    
    if (response.data && response.data.success) {
      return response.data.data || [];
    } else {
      console.error("Error fetching meal plans:", response.data?.message);
      return [];
    }
  } catch (error) {
    console.error("Error fetching meal plans:", error);
    return [];
  }
}; 
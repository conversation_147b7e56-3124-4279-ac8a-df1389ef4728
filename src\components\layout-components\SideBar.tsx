// import {CarTaxiFrontIcon,LayoutDashboard ,  WifiIcon } from 'lucide-react'
import { Hotel,LogOut, ScrollText, LayoutDashboard, CalendarCheck, DiamondPercent, AlertTriangle } from 'lucide-react'
import SideBarRoutes from './SideBarRoutes'

import { Button } from '@/components/ui/button'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
const sideBarRoutes = [
    {
        label : 'Dashboard',
        icon : LayoutDashboard,
        href : '/dashboard'

    },
    {
        label : 'Packages',
        icon : ScrollText,
      
        href : '/packages'

    },
    {
        label : 'Hotels',
        icon : Hotel,
      
        href : '/hotels'

    },
    {
        label : 'Price Issues',
        icon : AlertTriangle,
        href : '/price-issues'
    },
   
    // {
    //     label : 'Vehicles',
    //     icon : CarTaxiFrontIcon,
      
    //     href : '/vehicles'

    // },
    // {
    //     label : 'Amenities',
    //     icon : WifiIcon,   
    //     href : '/amenities'

    // },
    {
        label : 'Activity',
        icon: CalendarCheck,
        href:'/activities'
    },
    {
        label : "Coupons", 
        icon : DiamondPercent,
        href : "/coupons"

    },
    {
        label : "Bookings", 
        icon : DiamondPercent,
        href : "/bookings"

    }
]

const SideBar = () => {
    const navigate = useNavigate()
  function handleLogout(){
    const loadingToast = toast.loading('Logging out...');
    localStorage.setItem("accessToken","")
    localStorage.setItem("refreshToken","")
    setTimeout(() =>{
        toast.dismiss(loadingToast)
    },1000)
    setTimeout(()=>{
        navigate("/")
   toast.success('Logged out')
    },1000)
  }
  return (
    <div className='w-1/6 border-r fixed shadow h-full  bg-white '>
        <div className="p-5">
        <h1 className='text-xl font-bold text-appprimary'>TripXplo</h1>
        </div>
        <div className=' flex flex-col  px-2 gap-1'>
            <h1 className='text-left text-neutral-600 pb-3 pt-2 text-xs font-medium tracking-wider drop-shadow-sm'>MAIN MENU</h1>
        {
            sideBarRoutes?.map((menus)=>(
                <SideBarRoutes  label={menus.label} href={menus.href} icon={menus.icon} key={menus.label}/>
                 
               
            ))
        }
        </div>
        <div className="fixed bottom-10  left-0 w-1/6 h-10 p-4">
            <Button variant={'outline'} onClick={handleLogout} className='flex items-center justify-center gap-2 bg-appprimary text-neutral-700 bg-white bg-opacity-20'>
           <LogOut size={18}/>
           Logout   
                

            </Button>

            
        </div>
    
      
    </div>
  )
}

export default SideBar
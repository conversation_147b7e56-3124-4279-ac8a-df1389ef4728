import { cn } from '@/lib/utils'
import { setTab } from '@/store/features/hotelEditSlice'
import { useAppSelector } from '@/store/store'
import { LucideIcon } from 'lucide-react'
import { useDispatch } from 'react-redux'




interface HotelSlideBarProps {
    label : string , 
    icon : LucideIcon,
    tab : string
}

const HotelSlideBar = ({label , icon : Icon , tab}:HotelSlideBarProps) => {
  
    const {cuurentTab} = useAppSelector((state)=>state.HotelEdit)
    const dispatch = useDispatch();
    const handletabChange = () =>{
        dispatch(setTab(tab))


    }
   
  return (
    <div onClick={handletabChange}   className={cn("cursor-pointer w-full h-full whitespace-nowrap px-8 flex  items-center cursor-pointer p-4 hover:bg-slate-50  text-slate-600 hover:text-slate-800  gap-1",tab === cuurentTab && 'bg-slate-50 border-r-[3px] border-[#27B182]') }>
        
            <Icon  className="text-slate-500"size={20}/>

         <div className="flex pl-4 justify-center items-center">
         <h1 className='text-slate-500 cursor-pointer'>
            {label}
            </h1>
            
         </div>
       
        
    </div>
  )
}

export default HotelSlideBar
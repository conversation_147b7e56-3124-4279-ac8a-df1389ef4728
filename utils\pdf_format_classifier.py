#!/usr/bin/env python3
"""
Enhanced PDF Format Classifier for Tariff Extraction

This module provides advanced classification of PDF formats for tariff extraction,
using a combination of text analysis, table structure, and content patterns.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Set, Union
from collections import defaultdict

# Configure logging
logger = logging.getLogger('tariff_extractor')

class PDFFormatClassifier:
    """
    Advanced classifier for PDF tariff formats
    """
    
    def __init__(self):
        """Initialize the classifier"""
        # Define format characteristics
        self.format_characteristics = {
            "standard_tabular": {
                "description": "Standard tabular format with room types and prices in columns",
                "indicators": [
                    "room_type_column",
                    "price_columns",
                    "simple_structure"
                ]
            },
            "season_meal_matrix": {
                "description": "Matrix format with seasons in rows/columns and meal plans in columns/rows",
                "indicators": [
                    "season_headers",
                    "meal_plan_headers",
                    "matrix_structure"
                ]
            },
            "dual_rate_format": {
                "description": "Format with both rack rates and net/special rates",
                "indicators": [
                    "rack_rate_column",
                    "net_rate_column",
                    "dual_pricing"
                ]
            },
            "season_based": {
                "description": "Format with different rates for different seasons",
                "indicators": [
                    "season_headers",
                    "date_ranges",
                    "seasonal_pricing"
                ]
            },
            "meal_plan_rows": {
                "description": "Format with different meal plans in separate rows",
                "indicators": [
                    "meal_plan_in_rows",
                    "room_type_column",
                    "price_columns"
                ]
            },
            "complex_nested": {
                "description": "Complex format with nested headers and multiple dimensions",
                "indicators": [
                    "multi_level_headers",
                    "nested_structure",
                    "complex_layout"
                ]
            },
            "text_heavy": {
                "description": "Text-heavy format with prices embedded in paragraphs",
                "indicators": [
                    "few_tables",
                    "prices_in_text",
                    "paragraph_structure"
                ]
            },
            "unknown_format": {
                "description": "Unknown or unrecognized format",
                "indicators": []
            }
        }
        
        # Keywords for different format indicators
        self.indicator_keywords = {
            "room_type_column": [
                "room type", "room category", "accommodation", "category", "room", 
                "cottage", "villa", "suite", "view", "rooms"
            ],
            "price_columns": [
                "rate", "price", "tariff", "cost", "charge", "amount", "inr", "rs", "₹"
            ],
            "rack_rate_column": [
                "rack rate", "published rate", "gross rate", "rack", "published", "gross",
                "mrp", "maximum retail price", "list price"
            ],
            "net_rate_column": [
                "special rate", "net rate", "agent rate", "contract rate", "offer price",
                "special tariff", "net tariff", "special", "net"
            ],
            "season_headers": [
                "season", "period", "validity", "applicable from", "valid from", 
                "valid till", "valid until", "valid between"
            ],
            "meal_plan_headers": [
                "meal plan", "plan", "cp", "map", "ap", "ep", "cpai", "mapai", "apai", "epai",
                "continental", "american", "modified", "european", "breakfast", "half board", "full board"
            ],
            "date_ranges": [
                "from", "to", "till", "until", "between", "valid", "applicable"
            ]
        }
        
    def classify_pdf(self, text: str, tables: List[List[List[Any]]], 
                    table_analysis: Optional[Dict[int, Dict[str, Any]]] = None,
                    text_blocks: Optional[Dict[str, Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Classify the PDF format based on text, tables, and analysis results
        
        Args:
            text: Full text content of the PDF
            tables: List of tables extracted from the PDF
            table_analysis: Optional analysis results from DynamicTableAnalyzer
            text_blocks: Optional text blocks from identify_text_blocks
            
        Returns:
            Dictionary with classification results
        """
        # Initialize scores for each format
        format_scores = {fmt: 0.0 for fmt in self.format_characteristics.keys()}
        
        # Analyze text content
        text_indicators = self._analyze_text_content(text)
        
        # Analyze table structure
        table_indicators = self._analyze_table_structure(tables, table_analysis)
        
        # Analyze text blocks if available
        block_indicators = {}
        if text_blocks:
            block_indicators = self._analyze_text_blocks(text_blocks)
            
        # Combine all indicators
        all_indicators = {}
        all_indicators.update(text_indicators)
        all_indicators.update(table_indicators)
        all_indicators.update(block_indicators)
        
        # Calculate scores for each format
        for fmt, characteristics in self.format_characteristics.items():
            if fmt == "unknown_format":
                continue  # Skip unknown format for now
                
            score = 0.0
            indicators = characteristics["indicators"]
            
            for indicator in indicators:
                if indicator in all_indicators:
                    score += all_indicators[indicator]
                    
            # Normalize score based on number of indicators
            if indicators:
                format_scores[fmt] = score / len(indicators)
            else:
                format_scores[fmt] = 0.0
                
        # Find the best format
        best_format = max(format_scores.items(), key=lambda x: x[1])
        
        # If best score is too low, classify as unknown
        if best_format[1] < 0.3:
            primary_format = "unknown_format"
            confidence = best_format[1]
        else:
            primary_format = best_format[0]
            confidence = best_format[1]
            
        # Get secondary formats (those with scores close to the best)
        threshold = best_format[1] * 0.7  # 70% of best score
        secondary_formats = [fmt for fmt, score in format_scores.items() 
                           if score >= threshold and fmt != primary_format]
        
        # Return classification results
        return {
            "primary_format": primary_format,
            "confidence": confidence,
            "secondary_formats": secondary_formats,
            "all_scores": format_scores,
            "indicators": all_indicators
        }
        
    def _analyze_text_content(self, text: str) -> Dict[str, float]:
        """
        Analyze text content for format indicators
        
        Args:
            text: Full text content of the PDF
            
        Returns:
            Dictionary mapping indicators to confidence scores
        """
        indicators = {}
        
        # Check for room type indicators
        room_type_score = 0.0
        for keyword in self.indicator_keywords["room_type_column"]:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.I):
                room_type_score += 0.2
        indicators["room_type_column"] = min(room_type_score, 1.0)
        
        # Check for price column indicators
        price_score = 0.0
        for keyword in self.indicator_keywords["price_columns"]:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.I):
                price_score += 0.2
        indicators["price_columns"] = min(price_score, 1.0)
        
        # Check for rack rate indicators
        rack_rate_score = 0.0
        for keyword in self.indicator_keywords["rack_rate_column"]:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.I):
                rack_rate_score += 0.3
        indicators["rack_rate_column"] = min(rack_rate_score, 1.0)
        
        # Check for net rate indicators
        net_rate_score = 0.0
        for keyword in self.indicator_keywords["net_rate_column"]:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.I):
                net_rate_score += 0.3
        indicators["net_rate_column"] = min(net_rate_score, 1.0)
        
        # Check for dual pricing
        if rack_rate_score > 0 and net_rate_score > 0:
            indicators["dual_pricing"] = (rack_rate_score + net_rate_score) / 2
        else:
            indicators["dual_pricing"] = 0.0
            
        # Check for season indicators
        season_score = 0.0
        for keyword in self.indicator_keywords["season_headers"]:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.I):
                season_score += 0.2
        indicators["season_headers"] = min(season_score, 1.0)
        
        # Check for meal plan indicators
        meal_plan_score = 0.0
        for keyword in self.indicator_keywords["meal_plan_headers"]:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.I):
                meal_plan_score += 0.2
        indicators["meal_plan_headers"] = min(meal_plan_score, 1.0)
        
        # Check for date range indicators
        date_range_score = 0.0
        for keyword in self.indicator_keywords["date_ranges"]:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.I):
                date_range_score += 0.2
        indicators["date_ranges"] = min(date_range_score, 1.0)
        
        # Check for prices in text
        price_pattern = r'(?:₹|rs\.?|inr|\$)?\s*\d[\d,]*\.?\d*\s*(?:\+?(?:tax|gst))?'
        price_matches = re.findall(price_pattern, text, re.I)
        if len(price_matches) > 10:
            indicators["prices_in_text"] = 0.8
        elif len(price_matches) > 5:
            indicators["prices_in_text"] = 0.5
        else:
            indicators["prices_in_text"] = 0.2
            
        # Check for paragraph structure
        paragraph_pattern = r'\n\n.{50,}'
        paragraph_matches = re.findall(paragraph_pattern, text)
        if len(paragraph_matches) > 5:
            indicators["paragraph_structure"] = 0.8
        elif len(paragraph_matches) > 2:
            indicators["paragraph_structure"] = 0.5
        else:
            indicators["paragraph_structure"] = 0.2
            
        return indicators
        
    def _analyze_table_structure(self, tables: List[List[List[Any]]], 
                               table_analysis: Optional[Dict[int, Dict[str, Any]]] = None) -> Dict[str, float]:
        """
        Analyze table structure for format indicators
        
        Args:
            tables: List of tables extracted from the PDF
            table_analysis: Optional analysis results from DynamicTableAnalyzer
            
        Returns:
            Dictionary mapping indicators to confidence scores
        """
        indicators = {}
        
        # Check if we have tables
        if not tables:
            indicators["few_tables"] = 1.0
            return indicators
            
        # Count tables
        if len(tables) <= 2:
            indicators["few_tables"] = 0.8
        elif len(tables) <= 5:
            indicators["few_tables"] = 0.5
        else:
            indicators["few_tables"] = 0.2
            
        # Check for simple structure
        simple_structure_score = 0.0
        complex_structure_score = 0.0
        
        for table in tables:
            if not table:
                continue
                
            # Simple tables usually have consistent row lengths
            row_lengths = [len(row) for row in table if row]
            if row_lengths:
                if len(set(row_lengths)) <= 2:  # At most 2 different row lengths
                    simple_structure_score += 0.3
                else:
                    complex_structure_score += 0.3
                    
            # Simple tables usually have fewer columns
            max_cols = max(row_lengths) if row_lengths else 0
            if max_cols <= 5:
                simple_structure_score += 0.2
            else:
                complex_structure_score += 0.2
                
        indicators["simple_structure"] = min(simple_structure_score / len(tables), 1.0) if tables else 0.0
        indicators["complex_layout"] = min(complex_structure_score / len(tables), 1.0) if tables else 0.0
        
        # Check for matrix structure
        matrix_score = 0.0
        for table in tables:
            if not table or len(table) < 3:
                continue
                
            # Matrix structures often have header cells in first column
            first_col_headers = True
            for row_idx in range(1, len(table)):
                if not table[row_idx] or len(table[row_idx]) == 0:
                    continue
                    
                first_cell = str(table[row_idx][0]) if table[row_idx][0] is not None else ""
                if not first_cell or re.search(r'^\s*\d+\s*$', first_cell):
                    first_col_headers = False
                    break
                    
            if first_col_headers:
                matrix_score += 0.5
                
        indicators["matrix_structure"] = min(matrix_score, 1.0)
        
        # Use table analysis if available
        if table_analysis:
            multi_header_count = sum(1 for analysis in table_analysis.values() 
                                   if analysis.get("is_multi_header", False))
            
            if multi_header_count > 0:
                indicators["multi_level_headers"] = min(multi_header_count / len(table_analysis), 1.0)
                indicators["nested_structure"] = min(multi_header_count / len(table_analysis), 1.0)
                
            # Check for meal plans in rows
            meal_plan_in_rows = False
            for table_idx, analysis in table_analysis.items():
                column_roles = analysis.get("column_roles", {})
                
                # If we have a room type column but no meal plan column, meal plans might be in rows
                has_room_type_col = any(role == "ROOM_TYPE" for role in column_roles.values())
                has_meal_plan_col = any(role == "MEAL_PLAN" for role in column_roles.values())
                
                if has_room_type_col and not has_meal_plan_col:
                    meal_plan_in_rows = True
                    break
                    
            indicators["meal_plan_in_rows"] = 1.0 if meal_plan_in_rows else 0.0
            
        return indicators
        
    def _analyze_text_blocks(self, text_blocks: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """
        Analyze text blocks for format indicators
        
        Args:
            text_blocks: Text blocks from identify_text_blocks
            
        Returns:
            Dictionary mapping indicators to confidence scores
        """
        indicators = {}
        
        # Check for seasonal pricing
        if "Date_Validity_Overall" in text_blocks:
            validity_blocks = text_blocks["Date_Validity_Overall"].get("blocks", [])
            if len(validity_blocks) >= 2:
                indicators["seasonal_pricing"] = 0.8
            elif len(validity_blocks) == 1:
                indicators["seasonal_pricing"] = 0.5
            else:
                indicators["seasonal_pricing"] = 0.2
                
        # Check for meal plan information
        if "Meal_Plan_Definitions" in text_blocks:
            meal_plan_blocks = text_blocks["Meal_Plan_Definitions"].get("blocks", [])
            if len(meal_plan_blocks) >= 3:  # Multiple meal plans defined
                indicators["meal_plan_headers"] = 0.8
            elif len(meal_plan_blocks) > 0:
                indicators["meal_plan_headers"] = 0.5
                
        return indicators
